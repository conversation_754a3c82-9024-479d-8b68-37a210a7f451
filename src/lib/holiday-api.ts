// 节假日API服务
export interface HolidayApiResponse {
  code: number
  message: string
  data: {
    list: Array<{
      name: string
      date: string
      isOffDay: boolean
      holiday?: {
        holiday: boolean
        name: string
        wage: number
        date: string
        rest: number
      }
    }>
  }
}

export interface ChinaHolidayApiResponse {
  code: number
  type: {
    type: number
    name: string
    week: number
  }
  holiday?: {
    holiday: boolean
    name: string
    wage: number
    date: string
    rest: number
  }
}

export interface TimorHolidayResponse {
  code: number
  holiday: {
    '01-01': { name: string, wage: number, date: string, rest: number }
    [key: string]: { name: string, wage: number, date: string, rest: number }
  }
}

// 节假日API配置
export const HOLIDAY_API_CONFIGS = {
  // 免费API - 天行数据（需要申请key）
  tianapi: {
    name: '天行数据',
    url: 'https://apis.tianapi.com/jiejiari/index',
    requiresKey: true,
    description: '提供中国法定节假日和调休信息',
    website: 'https://www.tianapi.com/'
  },
  
  // 免费API - 聚合数据（需要申请key）
  juhe: {
    name: '聚合数据',
    url: 'https://apis.juhe.cn/fapig/calendar/query.php',
    requiresKey: true,
    description: '提供节假日查询服务',
    website: 'https://www.juhe.cn/'
  },
  
  // 免费API - 万年历API
  wannianli: {
    name: '万年历API',
    url: 'https://timor.tech/api/holiday',
    requiresKey: false,
    description: '免费的中国节假日API',
    website: 'https://timor.tech/'
  },
  
  // 免费API - 中国节假日API
  china_holiday: {
    name: '中国节假日API',
    url: 'https://api.apihubs.cn/holiday/get',
    requiresKey: false,
    description: '免费的中国法定节假日查询',
    website: 'https://api.apihubs.cn/'
  }
}

export class HolidayApiService {
  private static apiKey: string = ''
  private static selectedApi: keyof typeof HOLIDAY_API_CONFIGS = 'wannianli'

  static setApiKey(key: string) {
    this.apiKey = key
  }

  static setSelectedApi(api: keyof typeof HOLIDAY_API_CONFIGS) {
    this.selectedApi = api
  }

  static getSelectedApi() {
    return this.selectedApi
  }

  static getApiConfig() {
    return HOLIDAY_API_CONFIGS[this.selectedApi]
  }

  // 获取指定年份的节假日
  static async getHolidays(year: number): Promise<any[]> {
    try {
      switch (this.selectedApi) {
        case 'wannianli':
          return await this.getHolidaysFromWannianli(year)
        case 'china_holiday':
          return await this.getHolidaysFromChinaHoliday(year)
        case 'tianapi':
          return await this.getHolidaysFromTianapi(year)
        case 'juhe':
          return await this.getHolidaysFromJuhe(year)
        default:
          throw new Error('不支持的API类型')
      }
    } catch (error) {
      console.error('获取节假日数据失败:', error)
      throw error
    }
  }

  // 万年历API - 免费，无需key
  private static async getHolidaysFromWannianli(year: number): Promise<any[]> {
    const response = await fetch(`https://timor.tech/api/holiday/year/${year}`)
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }

    const data: TimorHolidayResponse = await response.json()
    
    if (data.code !== 0) {
      throw new Error(`API返回错误: ${data.code}`)
    }

    // 转换数据格式
    const holidays = []
    for (const [dateKey, holiday] of Object.entries(data.holiday)) {
      holidays.push({
        id: `holiday-${year}-${dateKey}`,
        name: holiday.name,
        date: `${year}-${dateKey}`,
        type: 'fixed' as const,
        recurring: true,
        closed: holiday.rest === 1,
        description: `法定节假日 - ${holiday.name}`,
        source: 'wannianli'
      })
    }

    return holidays
  }

  // 中国节假日API - 免费，无需key
  private static async getHolidaysFromChinaHoliday(year: number): Promise<any[]> {
    const holidays = []
    
    // 需要逐月查询
    for (let month = 1; month <= 12; month++) {
      try {
        const monthStr = month.toString().padStart(2, '0')
        const response = await fetch(`https://api.apihubs.cn/holiday/get?year=${year}&month=${monthStr}`)
        
        if (!response.ok) continue
        
        const data: ChinaHolidayApiResponse = await response.json()
        
        if (data.code === 0 && data.holiday) {
          holidays.push({
            id: `holiday-${year}-${monthStr}`,
            name: data.holiday.name,
            date: data.holiday.date,
            type: 'fixed' as const,
            recurring: true,
            closed: data.holiday.rest === 1,
            description: `法定节假日 - ${data.holiday.name}`,
            source: 'china_holiday'
          })
        }
      } catch (error) {
        console.warn(`获取${year}年${month}月节假日失败:`, error)
      }
    }

    return holidays
  }

  // 天行数据API - 需要key
  private static async getHolidaysFromTianapi(year: number): Promise<any[]> {
    if (!this.apiKey) {
      throw new Error('天行数据API需要提供API Key')
    }

    const response = await fetch(`https://apis.tianapi.com/jiejiari/index?key=${this.apiKey}&year=${year}`)
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }

    const data: HolidayApiResponse = await response.json()
    
    if (data.code !== 200) {
      throw new Error(`API返回错误: ${data.message}`)
    }

    // 转换数据格式
    return data.data.list
      .filter(item => item.holiday)
      .map(item => ({
        id: `holiday-${item.date}`,
        name: item.holiday!.name,
        date: item.date,
        type: 'fixed' as const,
        recurring: true,
        closed: item.holiday!.rest === 1,
        description: `法定节假日 - ${item.holiday!.name}`,
        source: 'tianapi'
      }))
  }

  // 聚合数据API - 需要key
  private static async getHolidaysFromJuhe(year: number): Promise<any[]> {
    if (!this.apiKey) {
      throw new Error('聚合数据API需要提供API Key')
    }

    const response = await fetch(`https://apis.juhe.cn/fapig/calendar/query.php?key=${this.apiKey}&year=${year}`)
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }

    const data = await response.json()
    
    if (data.error_code !== 0) {
      throw new Error(`API返回错误: ${data.reason}`)
    }

    // 转换数据格式（需要根据实际API响应调整）
    return data.result.map((item: any) => ({
      id: `holiday-${item.date}`,
      name: item.name,
      date: item.date,
      type: 'fixed' as const,
      recurring: true,
      closed: item.status === 1,
      description: `法定节假日 - ${item.name}`,
      source: 'juhe'
    }))
  }

  // 检查API连接状态
  static async testApiConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const currentYear = new Date().getFullYear()
      await this.getHolidays(currentYear)
      return { success: true, message: 'API连接正常' }
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : '未知错误' 
      }
    }
  }

  // 获取今日是否为节假日
  static async isTodayHoliday(): Promise<{ isHoliday: boolean; holidayName?: string }> {
    try {
      const today = new Date()
      const year = today.getFullYear()
      const dateStr = today.toISOString().split('T')[0]
      
      const holidays = await this.getHolidays(year)
      const todayHoliday = holidays.find(h => h.date === dateStr)
      
      return {
        isHoliday: !!todayHoliday,
        holidayName: todayHoliday?.name
      }
    } catch (error) {
      console.error('检查今日节假日失败:', error)
      return { isHoliday: false }
    }
  }

  // 获取即将到来的节假日
  static async getUpcomingHolidays(days: number = 30): Promise<any[]> {
    try {
      const today = new Date()
      const year = today.getFullYear()
      const nextYear = year + 1
      
      // 获取今年和明年的节假日
      const [thisYearHolidays, nextYearHolidays] = await Promise.all([
        this.getHolidays(year),
        this.getHolidays(nextYear)
      ])
      
      const allHolidays = [...thisYearHolidays, ...nextYearHolidays]
      const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000)
      
      return allHolidays
        .filter(holiday => {
          const holidayDate = new Date(holiday.date)
          return holidayDate >= today && holidayDate <= futureDate
        })
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(0, 10) // 最多返回10个
    } catch (error) {
      console.error('获取即将到来的节假日失败:', error)
      return []
    }
  }
}

// 节假日缓存管理
export class HolidayCache {
  private static readonly CACHE_KEY = 'holiday_cache'
  private static readonly CACHE_DURATION = 24 * 60 * 60 * 1000 // 24小时

  static getCachedHolidays(year: number): any[] | null {
    if (typeof window === 'undefined') return null

    try {
      const cached = localStorage.getItem(`${this.CACHE_KEY}_${year}`)
      if (!cached) return null

      const { data, timestamp } = JSON.parse(cached)
      const now = Date.now()

      if (now - timestamp > this.CACHE_DURATION) {
        this.clearCache(year)
        return null
      }

      return data
    } catch (error) {
      console.error('读取节假日缓存失败:', error)
      return null
    }
  }

  static setCachedHolidays(year: number, holidays: any[]): void {
    if (typeof window === 'undefined') return

    try {
      const cacheData = {
        data: holidays,
        timestamp: Date.now()
      }
      localStorage.setItem(`${this.CACHE_KEY}_${year}`, JSON.stringify(cacheData))
    } catch (error) {
      console.error('保存节假日缓存失败:', error)
    }
  }

  static clearCache(year?: number): void {
    if (typeof window === 'undefined') return

    try {
      if (year) {
        localStorage.removeItem(`${this.CACHE_KEY}_${year}`)
      } else {
        // 清除所有节假日缓存
        const keys = Object.keys(localStorage)
        keys.forEach(key => {
          if (key.startsWith(this.CACHE_KEY)) {
            localStorage.removeItem(key)
          }
        })
      }
    } catch (error) {
      console.error('清除节假日缓存失败:', error)
    }
  }
}

// 带缓存的节假日服务
export class CachedHolidayService {
  static async getHolidays(year: number): Promise<any[]> {
    // 先尝试从缓存获取
    const cached = HolidayCache.getCachedHolidays(year)
    if (cached) {
      return cached
    }

    // 缓存未命中，从API获取
    try {
      const holidays = await HolidayApiService.getHolidays(year)
      HolidayCache.setCachedHolidays(year, holidays)
      return holidays
    } catch (error) {
      console.error('获取节假日失败，返回空数组:', error)
      return []
    }
  }

  static async refreshHolidays(year: number): Promise<any[]> {
    HolidayCache.clearCache(year)
    return await this.getHolidays(year)
  }
}
