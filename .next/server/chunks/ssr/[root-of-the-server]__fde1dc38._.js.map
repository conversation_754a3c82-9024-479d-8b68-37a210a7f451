{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { \n  Calendar, \n  Users, \n  Scissors, \n  UserCheck, \n  Package, \n  BarChart3, \n  Settings,\n  Home\n} from 'lucide-react'\n\nconst navigation = [\n  { name: '仪表板', href: '/', icon: Home },\n  { name: '预约管理', href: '/appointments', icon: Calendar },\n  { name: '客户管理', href: '/customers', icon: Users },\n  { name: '员工管理', href: '/staff', icon: UserCheck },\n  { name: '服务管理', href: '/services', icon: Scissors },\n  { name: '库存管理', href: '/inventory', icon: Package },\n  { name: '数据分析', href: '/analytics', icon: BarChart3 },\n  { name: '系统设置', href: '/settings', icon: Settings },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"flex flex-col space-y-2\">\n      {navigation.map((item, index) => {\n        const isActive = pathname === item.href\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            className={cn(\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',\n              isActive\n                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'\n                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'\n            )}\n            style={{ animationDelay: `${index * 50}ms` }}\n          >\n            <item.icon className={cn(\n              \"mr-3 h-5 w-5 transition-all duration-200\",\n              isActive\n                ? \"text-primary scale-110\"\n                : \"text-muted-foreground group-hover:text-primary group-hover:scale-105\"\n            )} />\n            <span className=\"font-medium\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary animate-pulse\"></div>\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACrC;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,oMAAA,CAAA,QAAK;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAU,MAAM,gNAAA,CAAA,YAAS;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,wMAAA,CAAA,UAAO;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACpD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACnD;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,WAAW,aAAa,KAAK,IAAI;YACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gBAEH,MAAM,KAAK,IAAI;gBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gHACA,WACI,qEACA;gBAEN,OAAO;oBAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAAC;;kCAE3C,8OAAC,KAAK,IAAI;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,2BACA;;;;;;kCAEN,8OAAC;wBAAK,WAAU;kCAAe,KAAK,IAAI;;;;;;oBACvC,0BACC,8OAAC;wBAAI,WAAU;;;;;;;eAlBZ,KAAK,IAAI;;;;;QAsBpB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-elegant hover:shadow-elegant-lg transform hover:scale-105 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-primary text-white hover:opacity-90\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700\",\n        outline:\n          \"border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-white\",\n        secondary:\n          \"bg-gradient-secondary text-secondary-foreground hover:opacity-90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-elegant\",\n        link: \"text-primary underline-offset-4 hover:underline shadow-none\",\n        success: \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\",\n        warning: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600\",\n        info: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-10 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getInitials } from '@/lib/utils'\n\nexport function Sidebar() {\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const userDisplayName = user?.user_metadata?.full_name ||\n                          user?.email?.split('@')[0] ||\n                          'User'\n\n  const userInitials = getInitials(userDisplayName)\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b bg-gradient-primary\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"relative\">\n            <Crown className=\"h-7 w-7 text-white\" />\n            <Scissors className=\"h-4 w-4 text-white absolute -bottom-1 -right-1\" />\n          </div>\n          <div>\n            <span className=\"text-lg font-bold text-white\">皇家理发店</span>\n            <p className=\"text-xs text-white/80\">专业美发沙龙</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 px-4 py-6 bg-gradient-secondary\">\n        <Navigation />\n      </div>\n\n      {/* User info */}\n      <div className=\"border-t bg-card p-4\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant\">\n            <span className=\"text-sm font-bold text-white\">{userInitials}</span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-foreground truncate\">{userDisplayName}</p>\n            <p className=\"text-xs text-muted-foreground truncate\">{user?.email}</p>\n          </div>\n          <div className=\"h-2 w-2 rounded-full bg-green-500\" title=\"在线\"></div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex space-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={() => {/* TODO: Open profile settings */}}\n          >\n            <User className=\"h-3 w-3 mr-1\" />\n            个人资料\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={handleSignOut}\n          >\n            <LogOut className=\"h-3 w-3 mr-1\" />\n            退出登录\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM,eAAe,aACrB,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAC1B;IAExB,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAA0C,MAAM;;;;;;;;;;;;0CAE/D,8OAAC;gCAAI,WAAU;gCAAoC,OAAM;;;;;;;;;;;;kCAI3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,KAAwC;;kDAEjD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { Sidebar } from './sidebar'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background overflow-hidden\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto bg-gradient-to-br from-background via-secondary/30 to-muted/50\">\n        <div className=\"p-8 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Crown, Scissors } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireAdmin?: boolean\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallback \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"flex flex-col items-center space-y-4\">\n            <div className=\"relative animate-pulse\">\n              <Crown className=\"h-12 w-12 text-primary\" />\n              <Scissors className=\"h-8 w-8 text-accent absolute -bottom-2 -right-2\" />\n            </div>\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-semibold text-foreground mb-2\">Royal Cuts</h2>\n              <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n            </div>\n            <div className=\"flex space-x-1\">\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Show unauthorized if user is not logged in\n  if (!user) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"text-center\">\n            <div className=\"mb-4\">\n              <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              You need to be logged in to access this page.\n            </p>\n            <button\n              onClick={() => router.push('/auth/login')}\n              className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Go to Login\n            </button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Check admin requirement\n  if (requireAdmin) {\n    const isAdmin = user?.user_metadata?.role === 'admin' || \n                    user?.email?.endsWith('@royalcuts.com') ||\n                    user?.app_metadata?.role === 'admin'\n\n    if (!isAdmin) {\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n          <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n            <CardContent className=\"text-center\">\n              <div className=\"mb-4\">\n                <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Admin Access Required</h2>\n              <p className=\"text-muted-foreground mb-4\">\n                You need administrator privileges to access this page.\n              </p>\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Go to Dashboard\n              </button>\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: { requireAdmin?: boolean }\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={options?.requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Hook for checking authentication status\nexport function useRequireAuth(requireAdmin = false) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireAdmin) {\n        const isAdmin = user?.user_metadata?.role === 'admin' || \n                        user?.email?.endsWith('@royalcuts.com') ||\n                        user?.app_metadata?.role === 'admin'\n        \n        if (!isAdmin) {\n          router.push('/')\n          return\n        }\n      }\n    }\n  }, [user, loading, requireAdmin, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,KAAK,EACpB,QAAQ,EACY;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAChG,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5G;IAEA,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,OAAO,0BACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,0BAA0B;IAC1B,IAAI,cAAc;QAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;QAE7C,IAAI,CAAC,SAAS;YACZ,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;QAOX;IACF;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,SACd,SAAiC,EACjC,OAAoC;IAEpC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,8OAAC;YAAe,cAAc,SAAS;sBACrC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,eAAe,KAAK;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,cAAc;gBAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;gBAE7C,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,OAAO;QAAE;QAAM;IAAQ;AACzB", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/page-header.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: React.ReactNode\n  actions?: React.ReactNode\n  breadcrumbs?: Array<{\n    label: string\n    href?: string\n  }>\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon,\n  actions,\n  breadcrumbs,\n  className\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 mb-8\", className)}>\n      {/* Breadcrumbs */}\n      {breadcrumbs && breadcrumbs.length > 0 && (\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n            {breadcrumbs.map((crumb, index) => (\n              <li key={index} className=\"flex items-center\">\n                {index > 0 && (\n                  <svg\n                    className=\"h-4 w-4 mx-2\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n                {crumb.href ? (\n                  <a\n                    href={crumb.href}\n                    className=\"hover:text-foreground transition-colors\"\n                  >\n                    {crumb.label}\n                  </a>\n                ) : (\n                  <span className=\"text-foreground font-medium\">\n                    {crumb.label}\n                  </span>\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      )}\n\n      {/* Header Content */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-4\">\n          {icon && (\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              {icon}\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-muted-foreground text-lg mt-1\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions && (\n          <div className=\"flex items-center space-x-2\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\ninterface PageHeaderActionsProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function PageHeaderActions({ children, className }: PageHeaderActionsProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2\", className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface QuickStatsProps {\n  stats: Array<{\n    label: string\n    value: string | number\n    icon?: React.ReactNode\n    trend?: {\n      value: number\n      isPositive: boolean\n    }\n  }>\n  className?: string\n}\n\nexport function QuickStats({ stats, className }: QuickStatsProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\", className)}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-card rounded-xl p-4 border shadow-elegant hover:shadow-elegant-lg transition-all duration-300\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">\n                {stat.label}\n              </p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {stat.value}\n              </p>\n              {stat.trend && (\n                <p className={cn(\n                  \"text-xs flex items-center mt-1\",\n                  stat.trend.isPositive ? \"text-green-600\" : \"text-red-600\"\n                )}>\n                  <span className=\"mr-1\">\n                    {stat.trend.isPositive ? \"↗\" : \"↘\"}\n                  </span>\n                  {Math.abs(stat.trend.value)}%\n                </p>\n              )}\n            </div>\n            {stat.icon && (\n              <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n                {stat.icon}\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAeO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACX,SAAS,EACO;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAElC,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAU;gBAAO,cAAW;0BAC/B,cAAA,8OAAC;oBAAG,WAAU;8BACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;4BAAe,WAAU;;gCACvB,QAAQ,mBACP,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAId,MAAM,IAAI,iBACT,8OAAC;oCACC,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;yDAGd,8OAAC;oCAAK,WAAU;8CACb,MAAM,KAAK;;;;;;;2BAvBT;;;;;;;;;;;;;;;0BAiCjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,sBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;AAOO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;;;;;;AAGP;AAeO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;kBAC7E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAEC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,KAAK,kBACT,8OAAC;oCAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,kCACA,KAAK,KAAK,CAAC,UAAU,GAAG,mBAAmB;;sDAE3C,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM;;;;;;wCAEhC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK;wCAAE;;;;;;;;;;;;;wBAIjC,KAAK,IAAI,kBACR,8OAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI;;;;;;;;;;;;eAzBX;;;;;;;;;;AAiCf", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA,SAAS,6CACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        purple:\n          \"border-transparent bg-purple-500 text-white hover:bg-purple-600\",\n        pink:\n          \"border-transparent bg-pink-500 text-white hover:bg-pink-600\",\n        indigo:\n          \"border-transparent bg-indigo-500 text-white hover:bg-indigo-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,QACE;YACF,MACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { PageHeader } from '@/components/layout/page-header'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Settings, \n  Building, \n  Clock, \n  DollarSign, \n  Bell, \n  Shield, \n  Palette,\n  Globe,\n  Mail,\n  Phone,\n  MapPin,\n  Save,\n  User,\n  Key,\n  Database,\n  Smartphone,\n  Download,\n  Upload,\n  RotateCcw\n} from 'lucide-react'\nimport { useAuth } from '@/contexts/auth-context'\nimport {\n  SettingsManager,\n  validateBusinessSettings,\n  validateOperatingHours,\n  applyTheme,\n  type BusinessSettings,\n  type OperatingHours,\n  type NotificationSettings\n} from '@/lib/settings'\n\nexport default function SettingsPage() {\n  const { user } = useAuth()\n  const [loading, setLoading] = useState(false)\n  const [activeTab, setActiveTab] = useState('business')\n  const [errors, setErrors] = useState<string[]>([])\n\n  // Settings state\n  const [businessSettings, setBusinessSettings] = useState<BusinessSettings>({\n    name: '皇家理发店',\n    address: '北京市朝阳区三里屯街道1号',\n    phone: '+86 138-0013-8000',\n    email: '<EMAIL>',\n    website: 'www.royalcuts.cn',\n    timezone: 'Asia/Shanghai',\n    currency: 'CNY'\n  })\n\n  const [operatingHours, setOperatingHours] = useState<OperatingHours>({\n    monday: { open: '09:00', close: '18:00', closed: false },\n    tuesday: { open: '09:00', close: '18:00', closed: false },\n    wednesday: { open: '09:00', close: '18:00', closed: false },\n    thursday: { open: '09:00', close: '19:00', closed: false },\n    friday: { open: '09:00', close: '19:00', closed: false },\n    saturday: { open: '08:00', close: '17:00', closed: false },\n    sunday: { open: '10:00', close: '16:00', closed: false }\n  })\n\n  const [notifications, setNotifications] = useState<NotificationSettings>({\n    emailNotifications: true,\n    smsNotifications: false,\n    appointmentReminders: true,\n    lowStockAlerts: true,\n    dailyReports: false,\n    weeklyReports: true\n  })\n\n  const [selectedTheme, setSelectedTheme] = useState('royal-gold')\n\n  // Load settings on component mount\n  useEffect(() => {\n    const settings = SettingsManager.getSettings()\n    setBusinessSettings(settings.business)\n    setOperatingHours(settings.operatingHours)\n    setNotifications(settings.notifications)\n    setSelectedTheme(settings.theme)\n  }, [])\n\n  const handleSaveSettings = async () => {\n    setLoading(true)\n    setErrors([])\n\n    try {\n      // Validate settings\n      const businessErrors = validateBusinessSettings(businessSettings)\n      const hoursErrors = validateOperatingHours(operatingHours)\n      const allErrors = [...businessErrors, ...hoursErrors]\n\n      if (allErrors.length > 0) {\n        setErrors(allErrors)\n        setLoading(false)\n        return\n      }\n\n      // Save settings\n      const settings = {\n        business: businessSettings,\n        operatingHours,\n        notifications,\n        theme: selectedTheme,\n        language: 'zh-CN'\n      }\n\n      SettingsManager.saveSettings(settings)\n\n      // Apply theme immediately\n      applyTheme(selectedTheme)\n\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      alert('设置保存成功！')\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      alert('保存设置时出错，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleThemeChange = (theme: string) => {\n    setSelectedTheme(theme)\n    applyTheme(theme)\n  }\n\n  const handleResetSettings = () => {\n    if (confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {\n      SettingsManager.resetToDefaults()\n      const settings = SettingsManager.getSettings()\n      setBusinessSettings(settings.business)\n      setOperatingHours(settings.operatingHours)\n      setNotifications(settings.notifications)\n      setSelectedTheme(settings.theme)\n      applyTheme(settings.theme)\n      alert('设置已重置为默认值')\n    }\n  }\n\n  const handleExportSettings = () => {\n    try {\n      const settingsJson = SettingsManager.exportSettings()\n      const blob = new Blob([settingsJson], { type: 'application/json' })\n      const url = URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = 'barbershop-settings.json'\n      document.body.appendChild(a)\n      a.click()\n      document.body.removeChild(a)\n      URL.revokeObjectURL(url)\n    } catch (error) {\n      alert('导出设置失败')\n    }\n  }\n\n  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      try {\n        const content = e.target?.result as string\n        SettingsManager.importSettings(content)\n        const settings = SettingsManager.getSettings()\n        setBusinessSettings(settings.business)\n        setOperatingHours(settings.operatingHours)\n        setNotifications(settings.notifications)\n        setSelectedTheme(settings.theme)\n        applyTheme(settings.theme)\n        alert('设置导入成功！')\n      } catch (error) {\n        alert('导入设置失败：文件格式不正确')\n      }\n    }\n    reader.readAsText(file)\n    // Reset input\n    event.target.value = ''\n  }\n\n  const tabs = [\n    { id: 'business', label: '商户信息', icon: Building },\n    { id: 'hours', label: '营业时间', icon: Clock },\n    { id: 'notifications', label: '通知设置', icon: Bell },\n    { id: 'appearance', label: '外观设置', icon: Palette },\n    { id: 'security', label: '安全设置', icon: Shield },\n    { id: 'integrations', label: '系统集成', icon: Database }\n  ]\n\n  const renderBusinessSettings = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Building className=\"h-5 w-5 mr-2\" />\n            商户信息\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">商户名称</label>\n              <Input\n                value={businessSettings.name}\n                onChange={(e) => setBusinessSettings({...businessSettings, name: e.target.value})}\n                placeholder=\"皇家理发店\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">联系电话</label>\n              <div className=\"relative\">\n                <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.phone}\n                  onChange={(e) => setBusinessSettings({...businessSettings, phone: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"+86 138-0013-8000\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">邮箱地址</label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.email}\n                  onChange={(e) => setBusinessSettings({...businessSettings, email: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">网站地址</label>\n              <div className=\"relative\">\n                <Globe className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.website}\n                  onChange={(e) => setBusinessSettings({...businessSettings, website: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"www.royalcuts.cn\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">商户地址</label>\n            <div className=\"relative\">\n              <MapPin className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\n              <textarea\n                value={businessSettings.address}\n                onChange={(e) => setBusinessSettings({...businessSettings, address: e.target.value})}\n                className=\"w-full pl-10 pt-2 pb-2 pr-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                rows={2}\n                placeholder=\"北京市朝阳区三里屯街道1号\"\n              />\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">时区设置</label>\n              <Select value={businessSettings.timezone} onValueChange={(value) => setBusinessSettings({...businessSettings, timezone: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"Asia/Shanghai\">北京时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"Asia/Hong_Kong\">香港时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"Asia/Taipei\">台北时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"America/New_York\">美国东部时间 (ET)</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">货币设置</label>\n              <Select value={businessSettings.currency} onValueChange={(value) => setBusinessSettings({...businessSettings, currency: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"CNY\">CNY - 人民币</SelectItem>\n                  <SelectItem value=\"HKD\">HKD - 港币</SelectItem>\n                  <SelectItem value=\"TWD\">TWD - 新台币</SelectItem>\n                  <SelectItem value=\"USD\">USD - 美元</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderOperatingHours = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Clock className=\"h-5 w-5 mr-2\" />\n            营业时间\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {Object.entries(operatingHours).map(([day, hours]) => (\n              <div key={day} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"font-medium capitalize w-20\">\n                    {day === 'monday' ? '周一' :\n                     day === 'tuesday' ? '周二' :\n                     day === 'wednesday' ? '周三' :\n                     day === 'thursday' ? '周四' :\n                     day === 'friday' ? '周五' :\n                     day === 'saturday' ? '周六' :\n                     day === 'sunday' ? '周日' : day}\n                  </span>\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={!hours.closed}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, closed: !e.target.checked }\n                      })}\n                      className=\"rounded\"\n                    />\n                    <span className=\"text-sm\">营业</span>\n                  </label>\n                </div>\n                {!hours.closed && (\n                  <div className=\"flex items-center space-x-2\">\n                    <Input\n                      type=\"time\"\n                      value={hours.open}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, open: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                    <span className=\"text-muted-foreground\">至</span>\n                    <Input\n                      type=\"time\"\n                      value={hours.close}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, close: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                  </div>\n                )}\n                {hours.closed && (\n                  <Badge variant=\"secondary\">休息</Badge>\n                )}\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderNotifications = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Bell className=\"h-5 w-5 mr-2\" />\n            通知偏好设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[\n              { key: 'emailNotifications', label: '邮件通知', description: '通过邮件接收通知' },\n              { key: 'smsNotifications', label: '短信通知', description: '通过短信接收通知' },\n              { key: 'appointmentReminders', label: '预约提醒', description: '向客户发送预约提醒' },\n              { key: 'lowStockAlerts', label: '库存不足提醒', description: '库存不足时接收通知' },\n              { key: 'dailyReports', label: '日报', description: '接收每日业务摘要' },\n              { key: 'weeklyReports', label: '周报', description: '接收每周分析报告' }\n            ].map((setting) => (\n              <div key={setting.key} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div>\n                  <p className=\"font-medium\">{setting.label}</p>\n                  <p className=\"text-sm text-muted-foreground\">{setting.description}</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notifications[setting.key as keyof typeof notifications]}\n                    onChange={(e) => setNotifications({\n                      ...notifications,\n                      [setting.key]: e.target.checked\n                    })}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderAppearance = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Palette className=\"h-5 w-5 mr-2\" />\n            外观设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"p-4 border rounded-lg bg-muted/30\">\n              <p className=\"font-medium mb-2\">主题设置</p>\n              <p className=\"text-sm text-muted-foreground mb-4\">选择您喜欢的配色方案</p>\n              <div className=\"grid grid-cols-3 gap-3\">\n                <div\n                  className={`p-3 border rounded-lg cursor-pointer hover:bg-accent transition-all ${\n                    selectedTheme === 'royal-gold' ? 'ring-2 ring-primary' : ''\n                  }`}\n                  onClick={() => handleThemeChange('royal-gold')}\n                >\n                  <div className=\"h-8 bg-gradient-to-r from-amber-600 to-amber-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">\n                    皇家金色 {selectedTheme === 'royal-gold' ? '(当前)' : ''}\n                  </p>\n                </div>\n                <div\n                  className={`p-3 border rounded-lg cursor-pointer hover:bg-accent transition-all ${\n                    selectedTheme === 'ocean-blue' ? 'ring-2 ring-primary' : ''\n                  }`}\n                  onClick={() => handleThemeChange('ocean-blue')}\n                >\n                  <div className=\"h-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">\n                    海洋蓝 {selectedTheme === 'ocean-blue' ? '(当前)' : ''}\n                  </p>\n                </div>\n                <div\n                  className={`p-3 border rounded-lg cursor-pointer hover:bg-accent transition-all ${\n                    selectedTheme === 'forest-green' ? 'ring-2 ring-primary' : ''\n                  }`}\n                  onClick={() => handleThemeChange('forest-green')}\n                >\n                  <div className=\"h-8 bg-gradient-to-r from-emerald-600 to-emerald-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">\n                    森林绿 {selectedTheme === 'forest-green' ? '(当前)' : ''}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderSecurity = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Shield className=\"h-5 w-5 mr-2\" />\n            安全设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">双重身份验证</p>\n                <p className=\"text-sm text-muted-foreground\">为您的账户添加额外的安全保护</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Smartphone className=\"h-4 w-4 mr-2\" />\n                启用双重验证\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">修改密码</p>\n                <p className=\"text-sm text-muted-foreground\">更新您的账户密码</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Key className=\"h-4 w-4 mr-2\" />\n                修改密码\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">活跃会话</p>\n                <p className=\"text-sm text-muted-foreground\">管理您的活跃登录会话</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                查看会话\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderIntegrations = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Database className=\"h-5 w-5 mr-2\" />\n            系统集成\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center\">\n                  <Database className=\"h-5 w-5 text-green-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">Supabase 数据库</p>\n                  <p className=\"text-sm text-muted-foreground\">已连接并同步</p>\n                </div>\n              </div>\n              <Badge variant=\"success\">已连接</Badge>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                  <Mail className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">邮件服务</p>\n                  <p className=\"text-sm text-muted-foreground\">发送自动化邮件</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">连接</Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n                  <Smartphone className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">短信服务</p>\n                  <p className=\"text-sm text-muted-foreground\">发送短信通知</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">连接</Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'business': return renderBusinessSettings()\n      case 'hours': return renderOperatingHours()\n      case 'notifications': return renderNotifications()\n      case 'appearance': return renderAppearance()\n      case 'security': return renderSecurity()\n      case 'integrations': return renderIntegrations()\n      default: return renderBusinessSettings()\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n        <PageHeader\n          title=\"系统设置\"\n          description=\"配置理发店管理系统\"\n          icon={<Settings className=\"h-6 w-6 text-white\" />}\n        />\n\n        {/* Error Display */}\n        {errors.length > 0 && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n            <div className=\"flex items-start space-x-2\">\n              <div className=\"h-4 w-4 text-red-500 mt-0.5\">⚠️</div>\n              <div>\n                <h4 className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n                  设置验证错误\n                </h4>\n                <ul className=\"text-sm text-red-600 dark:text-red-400 mt-1\">\n                  {errors.map((error, index) => (\n                    <li key={index}>• {error}</li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-col lg:flex-row gap-6\">\n          {/* Settings Navigation */}\n          <div className=\"lg:w-64\">\n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-4\">\n                <nav className=\"space-y-1\">\n                  {tabs.map((tab) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                        activeTab === tab.id\n                          ? 'bg-primary text-primary-foreground'\n                          : 'hover:bg-accent hover:text-accent-foreground'\n                      }`}\n                    >\n                      <tab.icon className=\"h-4 w-4\" />\n                      <span className=\"text-sm font-medium\">{tab.label}</span>\n                    </button>\n                  ))}\n                </nav>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Settings Content */}\n          <div className=\"flex-1\">\n            {renderTabContent()}\n            \n            {/* Action Buttons */}\n            <div className=\"mt-6 flex flex-col sm:flex-row gap-3 justify-between\">\n              <div className=\"flex gap-2\">\n                <Button variant=\"outline\" onClick={handleExportSettings}>\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  导出设置\n                </Button>\n                <div className=\"relative\">\n                  <input\n                    type=\"file\"\n                    accept=\".json\"\n                    onChange={handleImportSettings}\n                    className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n                  />\n                  <Button variant=\"outline\">\n                    <Upload className=\"h-4 w-4 mr-2\" />\n                    导入设置\n                  </Button>\n                </div>\n                <Button variant=\"outline\" onClick={handleResetSettings}>\n                  <RotateCcw className=\"h-4 w-4 mr-2\" />\n                  重置默认\n                </Button>\n              </div>\n\n              <Button onClick={handleSaveSettings} disabled={loading}>\n                {loading ? (\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                    <span>保存中...</span>\n                  </div>\n                ) : (\n                  <>\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    保存设置\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </MainLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;AAvCA;;;;;;;;;;;;;;AAiDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjD,iBAAiB;IACjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,SAAS;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACxD,WAAW;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QAC1D,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;IACzD;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QACvE,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,sHAAA,CAAA,kBAAe,CAAC,WAAW;QAC5C,oBAAoB,SAAS,QAAQ;QACrC,kBAAkB,SAAS,cAAc;QACzC,iBAAiB,SAAS,aAAa;QACvC,iBAAiB,SAAS,KAAK;IACjC,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,WAAW;QACX,UAAU,EAAE;QAEZ,IAAI;YACF,oBAAoB;YACpB,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE;YAC3C,MAAM,YAAY;mBAAI;mBAAmB;aAAY;YAErD,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,UAAU;gBACV,WAAW;gBACX;YACF;YAEA,gBAAgB;YAChB,MAAM,WAAW;gBACf,UAAU;gBACV;gBACA;gBACA,OAAO;gBACP,UAAU;YACZ;YAEA,sHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;YAE7B,0BAA0B;YAC1B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;YAEX,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;IACb;IAEA,MAAM,sBAAsB;QAC1B,IAAI,QAAQ,4BAA4B;YACtC,sHAAA,CAAA,kBAAe,CAAC,eAAe;YAC/B,MAAM,WAAW,sHAAA,CAAA,kBAAe,CAAC,WAAW;YAC5C,oBAAoB,SAAS,QAAQ;YACrC,kBAAkB,SAAS,cAAc;YACzC,iBAAiB,SAAS,aAAa;YACvC,iBAAiB,SAAS,KAAK;YAC/B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK;YACzB,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,eAAe,sHAAA,CAAA,kBAAe,CAAC,cAAc;YACnD,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAa,EAAE;gBAAE,MAAM;YAAmB;YACjE,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,UAAU,EAAE,MAAM,EAAE;gBAC1B,sHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;gBAC/B,MAAM,WAAW,sHAAA,CAAA,kBAAe,CAAC,WAAW;gBAC5C,oBAAoB,SAAS,QAAQ;gBACrC,kBAAkB,SAAS,cAAc;gBACzC,iBAAiB,SAAS,aAAa;gBACvC,iBAAiB,SAAS,KAAK;gBAC/B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK;gBACzB,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QACA,OAAO,UAAU,CAAC;QAClB,cAAc;QACd,MAAM,MAAM,CAAC,KAAK,GAAG;IACvB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAQ,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAChD;YAAE,IAAI;YAAS,OAAO;YAAQ,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC1C;YAAE,IAAI;YAAiB,OAAO;YAAQ,MAAM,kMAAA,CAAA,OAAI;QAAC;QACjD;YAAE,IAAI;YAAc,OAAO;YAAQ,MAAM,wMAAA,CAAA,UAAO;QAAC;QACjD;YAAE,IAAI;YAAY,OAAO;YAAQ,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC9C;YAAE,IAAI;YAAgB,OAAO;YAAQ,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACrD;IAED,MAAM,yBAAyB,kBAC7B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,iBAAiB,IAAI;gDAC5B,UAAU,CAAC,IAAM,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC/E,aAAY;;;;;;;;;;;;kDAGhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAIlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,OAAO;wDAC/B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAClF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,OAAO,iBAAiB,OAAO;gDAC/B,UAAU,CAAC,IAAM,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAClF,WAAU;gDACV,MAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;0EAClC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAiB;;;;;;0EACnC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAc;;;;;;0EAChC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;kDAI3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUxC,MAAM,uBAAuB,kBAC3B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAItC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC/C,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,QAAQ,WAAW,OACnB,QAAQ,YAAY,OACpB,QAAQ,cAAc,OACtB,QAAQ,aAAa,OACrB,QAAQ,WAAW,OACnB,QAAQ,aAAa,OACrB,QAAQ,WAAW,OAAO;;;;;;8DAE7B,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,CAAC,MAAM,MAAM;4DACtB,UAAU,CAAC,IAAM,kBAAkB;oEACjC,GAAG,cAAc;oEACjB,CAAC,IAAI,EAAE;wEAAE,GAAG,KAAK;wEAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,OAAO;oEAAC;gEAC/C;4DACA,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;wCAG7B,CAAC,MAAM,MAAM,kBACZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,MAAM,IAAI;oDACjB,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,CAAC,IAAI,EAAE;gEAAE,GAAG,KAAK;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1C;oDACA,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,MAAM,KAAK;oDAClB,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,CAAC,IAAI,EAAE;gEAAE,GAAG,KAAK;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC3C;oDACA,WAAU;;;;;;;;;;;;wCAIf,MAAM,MAAM,kBACX,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;;mCAhDrB;;;;;;;;;;;;;;;;;;;;;;;;;;IA0DtB,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAsB,OAAO;oCAAQ,aAAa;gCAAW;gCACpE;oCAAE,KAAK;oCAAoB,OAAO;oCAAQ,aAAa;gCAAW;gCAClE;oCAAE,KAAK;oCAAwB,OAAO;oCAAQ,aAAa;gCAAY;gCACvE;oCAAE,KAAK;oCAAkB,OAAO;oCAAU,aAAa;gCAAY;gCACnE;oCAAE,KAAK;oCAAgB,OAAO;oCAAM,aAAa;gCAAW;gCAC5D;oCAAE,KAAK;oCAAiB,OAAO;oCAAM,aAAa;gCAAW;6BAC9D,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC;oCAAsB,WAAU;;sDAC/B,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe,QAAQ,KAAK;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAAiC,QAAQ,WAAW;;;;;;;;;;;;sDAEnE,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,aAAa,CAAC,QAAQ,GAAG,CAA+B;oDACjE,UAAU,CAAC,IAAM,iBAAiB;4DAChC,GAAG,aAAa;4DAChB,CAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;wDACjC;oDACA,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAfT,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;IAyBjC,MAAM,mBAAmB,kBACvB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIxC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAClD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,kBAAkB,eAAe,wBAAwB,IACzD;gDACF,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;4DAAsB;4DAC3B,kBAAkB,eAAe,SAAS;;;;;;;;;;;;;0DAGpD,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,kBAAkB,eAAe,wBAAwB,IACzD;gDACF,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;4DAAsB;4DAC5B,kBAAkB,eAAe,SAAS;;;;;;;;;;;;;0DAGnD,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,kBAAkB,iBAAiB,wBAAwB,IAC3D;gDACF,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAE,WAAU;;4DAAsB;4DAC5B,kBAAkB,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWnE,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIvC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUhD,MAAM,qBAAqB,kBACzB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;0CAI7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQhD,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC,gJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;;8BACT,8OAAC,8IAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;oBACZ,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;gBAI3B,OAAO,MAAM,GAAG,mBACf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC;wCAAG,WAAU;kDACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;;oDAAe;oDAAG;;+CAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;gDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,uCACA,gDACJ;;kEAEF,8OAAC,IAAI,IAAI;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAuB,IAAI,KAAK;;;;;;;+CAT3C,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBvB,8OAAC;4BAAI,WAAU;;gCACZ;8CAGD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,QAAO;4DACP,UAAU;4DACV,WAAU;;;;;;sEAEZ,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;;8EACd,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAIvC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAK1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAoB,UAAU;sDAC5C,wBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;;;;;;qEAGR;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}]}