{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport {\n  Calendar,\n  Users,\n  Scissors,\n  UserCheck,\n  Package,\n  BarChart3,\n  Settings,\n  Home,\n  Navigation as NavigationIcon\n} from 'lucide-react'\n\nconst navigation = [\n  { name: '仪表板', href: '/', icon: Home },\n  { name: '预约管理', href: '/appointments', icon: Calendar },\n  { name: '客户管理', href: '/customers', icon: Users },\n  { name: '员工管理', href: '/staff', icon: UserCheck },\n  { name: '服务管理', href: '/services', icon: Scissors },\n  { name: '库存管理', href: '/inventory', icon: Package },\n  { name: '数据分析', href: '/analytics', icon: BarChart3 },\n  { name: '系统设置', href: '/settings', icon: Settings },\n  { name: '定位演示', href: '/demo-location', icon: NavigationIcon },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"flex flex-col space-y-2\">\n      {navigation.map((item, index) => {\n        const isActive = pathname === item.href\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            className={cn(\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',\n              isActive\n                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'\n                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'\n            )}\n            style={{ animationDelay: `${index * 50}ms` }}\n          >\n            <item.icon className={cn(\n              \"mr-3 h-5 w-5 transition-all duration-200\",\n              isActive\n                ? \"text-primary scale-110\"\n                : \"text-muted-foreground group-hover:text-primary group-hover:scale-105\"\n            )} />\n            <span className=\"font-medium\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary animate-pulse\"></div>\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACrC;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,oMAAA,CAAA,QAAK;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAU,MAAM,gNAAA,CAAA,YAAS;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,wMAAA,CAAA,UAAO;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACpD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAkB,MAAM,8MAAA,CAAA,aAAc;IAAC;CAC9D;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,WAAW,aAAa,KAAK,IAAI;YACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gBAEH,MAAM,KAAK,IAAI;gBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gHACA,WACI,qEACA;gBAEN,OAAO;oBAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAAC;;kCAE3C,8OAAC,KAAK,IAAI;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,2BACA;;;;;;kCAEN,8OAAC;wBAAK,WAAU;kCAAe,KAAK,IAAI;;;;;;oBACvC,0BACC,8OAAC;wBAAI,WAAU;;;;;;;eAlBZ,KAAK,IAAI;;;;;QAsBpB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-elegant hover:shadow-elegant-lg transform hover:scale-105 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-primary text-white hover:opacity-90\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700\",\n        outline:\n          \"border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-white\",\n        secondary:\n          \"bg-gradient-secondary text-secondary-foreground hover:opacity-90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-elegant\",\n        link: \"text-primary underline-offset-4 hover:underline shadow-none\",\n        success: \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\",\n        warning: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600\",\n        info: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-10 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getInitials } from '@/lib/utils'\n\nexport function Sidebar() {\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const userDisplayName = user?.user_metadata?.full_name ||\n                          user?.email?.split('@')[0] ||\n                          'User'\n\n  const userInitials = getInitials(userDisplayName)\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b bg-gradient-primary\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"relative\">\n            <Crown className=\"h-7 w-7 text-white\" />\n            <Scissors className=\"h-4 w-4 text-white absolute -bottom-1 -right-1\" />\n          </div>\n          <div>\n            <span className=\"text-lg font-bold text-white\">皇家理发店</span>\n            <p className=\"text-xs text-white/80\">专业美发沙龙</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 px-4 py-6 bg-gradient-secondary\">\n        <Navigation />\n      </div>\n\n      {/* User info */}\n      <div className=\"border-t bg-card p-4\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant\">\n            <span className=\"text-sm font-bold text-white\">{userInitials}</span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-foreground truncate\">{userDisplayName}</p>\n            <p className=\"text-xs text-muted-foreground truncate\">{user?.email}</p>\n          </div>\n          <div className=\"h-2 w-2 rounded-full bg-green-500\" title=\"在线\"></div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex space-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={() => {/* TODO: Open profile settings */}}\n          >\n            <User className=\"h-3 w-3 mr-1\" />\n            个人资料\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={handleSignOut}\n          >\n            <LogOut className=\"h-3 w-3 mr-1\" />\n            退出登录\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM,eAAe,aACrB,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAC1B;IAExB,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAA0C,MAAM;;;;;;;;;;;;0CAE/D,8OAAC;gCAAI,WAAU;gCAAoC,OAAM;;;;;;;;;;;;kCAI3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,KAAwC;;kDAEjD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { Sidebar } from './sidebar'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background overflow-hidden\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto bg-gradient-to-br from-background via-secondary/30 to-muted/50\">\n        <div className=\"p-8 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Crown, Scissors } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireAdmin?: boolean\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallback \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"flex flex-col items-center space-y-4\">\n            <div className=\"relative animate-pulse\">\n              <Crown className=\"h-12 w-12 text-primary\" />\n              <Scissors className=\"h-8 w-8 text-accent absolute -bottom-2 -right-2\" />\n            </div>\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-semibold text-foreground mb-2\">Royal Cuts</h2>\n              <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n            </div>\n            <div className=\"flex space-x-1\">\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Show unauthorized if user is not logged in\n  if (!user) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"text-center\">\n            <div className=\"mb-4\">\n              <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              You need to be logged in to access this page.\n            </p>\n            <button\n              onClick={() => router.push('/auth/login')}\n              className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Go to Login\n            </button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Check admin requirement\n  if (requireAdmin) {\n    const isAdmin = user?.user_metadata?.role === 'admin' || \n                    user?.email?.endsWith('@royalcuts.com') ||\n                    user?.app_metadata?.role === 'admin'\n\n    if (!isAdmin) {\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n          <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n            <CardContent className=\"text-center\">\n              <div className=\"mb-4\">\n                <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Admin Access Required</h2>\n              <p className=\"text-muted-foreground mb-4\">\n                You need administrator privileges to access this page.\n              </p>\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Go to Dashboard\n              </button>\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: { requireAdmin?: boolean }\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={options?.requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Hook for checking authentication status\nexport function useRequireAuth(requireAdmin = false) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireAdmin) {\n        const isAdmin = user?.user_metadata?.role === 'admin' || \n                        user?.email?.endsWith('@royalcuts.com') ||\n                        user?.app_metadata?.role === 'admin'\n        \n        if (!isAdmin) {\n          router.push('/')\n          return\n        }\n      }\n    }\n  }, [user, loading, requireAdmin, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,KAAK,EACpB,QAAQ,EACY;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAChG,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5G;IAEA,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,OAAO,0BACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,0BAA0B;IAC1B,IAAI,cAAc;QAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;QAE7C,IAAI,CAAC,SAAS;YACZ,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;QAOX;IACF;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,SACd,SAAiC,EACjC,OAAoC;IAEpC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,8OAAC;YAAe,cAAc,SAAS;sBACrC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,eAAe,KAAK;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,cAAc;gBAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;gBAE7C,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,OAAO;QAAE;QAAM;IAAQ;AACzB", "debugId": null}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/page-header.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: React.ReactNode\n  actions?: React.ReactNode\n  breadcrumbs?: Array<{\n    label: string\n    href?: string\n  }>\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon,\n  actions,\n  breadcrumbs,\n  className\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 mb-8\", className)}>\n      {/* Breadcrumbs */}\n      {breadcrumbs && breadcrumbs.length > 0 && (\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n            {breadcrumbs.map((crumb, index) => (\n              <li key={index} className=\"flex items-center\">\n                {index > 0 && (\n                  <svg\n                    className=\"h-4 w-4 mx-2\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n                {crumb.href ? (\n                  <a\n                    href={crumb.href}\n                    className=\"hover:text-foreground transition-colors\"\n                  >\n                    {crumb.label}\n                  </a>\n                ) : (\n                  <span className=\"text-foreground font-medium\">\n                    {crumb.label}\n                  </span>\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      )}\n\n      {/* Header Content */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-4\">\n          {icon && (\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              {icon}\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-muted-foreground text-lg mt-1\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions && (\n          <div className=\"flex items-center space-x-2\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\ninterface PageHeaderActionsProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function PageHeaderActions({ children, className }: PageHeaderActionsProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2\", className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface QuickStatsProps {\n  stats: Array<{\n    label: string\n    value: string | number\n    icon?: React.ReactNode\n    trend?: {\n      value: number\n      isPositive: boolean\n    }\n  }>\n  className?: string\n}\n\nexport function QuickStats({ stats, className }: QuickStatsProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\", className)}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-card rounded-xl p-4 border shadow-elegant hover:shadow-elegant-lg transition-all duration-300\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">\n                {stat.label}\n              </p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {stat.value}\n              </p>\n              {stat.trend && (\n                <p className={cn(\n                  \"text-xs flex items-center mt-1\",\n                  stat.trend.isPositive ? \"text-green-600\" : \"text-red-600\"\n                )}>\n                  <span className=\"mr-1\">\n                    {stat.trend.isPositive ? \"↗\" : \"↘\"}\n                  </span>\n                  {Math.abs(stat.trend.value)}%\n                </p>\n              )}\n            </div>\n            {stat.icon && (\n              <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n                {stat.icon}\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAeO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACX,SAAS,EACO;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAElC,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAU;gBAAO,cAAW;0BAC/B,cAAA,8OAAC;oBAAG,WAAU;8BACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;4BAAe,WAAU;;gCACvB,QAAQ,mBACP,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAId,MAAM,IAAI,iBACT,8OAAC;oCACC,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;yDAGd,8OAAC;oCAAK,WAAU;8CACb,MAAM,KAAK;;;;;;;2BAvBT;;;;;;;;;;;;;;;0BAiCjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,sBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;AAOO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;;;;;;AAGP;AAeO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;kBAC7E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAEC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,KAAK,kBACT,8OAAC;oCAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,kCACA,KAAK,KAAK,CAAC,UAAU,GAAG,mBAAmB;;sDAE3C,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM;;;;;;wCAEhC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK;wCAAE;;;;;;;;;;;;;wBAIjC,KAAK,IAAI,kBACR,8OAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI;;;;;;;;;;;;eAzBX;;;;;;;;;;AAiCf", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA,SAAS,6CACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1364, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { Crown, Scissors } from \"lucide-react\"\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  text?: string\n  fullScreen?: boolean\n}\n\nexport function Loading({ \n  size = 'md', \n  className, \n  text = 'Loading...', \n  fullScreen = false \n}: LoadingProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  const containerClasses = fullScreen \n    ? 'fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50'\n    : 'flex items-center justify-center p-4'\n\n  return (\n    <div className={cn(containerClasses, className)}>\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"relative animate-pulse\">\n          <Crown className={cn(sizeClasses[size], \"text-primary\")} />\n          <Scissors className={cn(\n            size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-5 w-5' : 'h-8 w-8',\n            \"text-accent absolute -bottom-1 -right-1\"\n          )} />\n        </div>\n        {text && (\n          <p className={cn(\n            \"text-muted-foreground\",\n            size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'\n          )}>\n            {text}\n          </p>\n        )}\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n          <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n          <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingSpinner({ \n  size = 'md', \n  className \n}: { \n  size?: 'sm' | 'md' | 'lg'\n  className?: string \n}) {\n  const sizeClasses = {\n    sm: 'h-4 w-4 border-2',\n    md: 'h-6 w-6 border-2',\n    lg: 'h-8 w-8 border-3'\n  }\n\n  return (\n    <div className={cn(\n      \"border-primary border-t-transparent rounded-full animate-spin\",\n      sizeClasses[size],\n      className\n    )} />\n  )\n}\n\nexport function LoadingDots({ \n  size = 'md', \n  className \n}: { \n  size?: 'sm' | 'md' | 'lg'\n  className?: string \n}) {\n  const dotSizes = {\n    sm: 'h-1 w-1',\n    md: 'h-2 w-2',\n    lg: 'h-3 w-3'\n  }\n\n  return (\n    <div className={cn(\"flex space-x-1\", className)}>\n      <div className={cn(dotSizes[size], \"bg-primary rounded-full animate-bounce\")}></div>\n      <div className={cn(dotSizes[size], \"bg-primary rounded-full animate-bounce\")} style={{ animationDelay: '0.1s' }}></div>\n      <div className={cn(dotSizes[size], \"bg-primary rounded-full animate-bounce\")} style={{ animationDelay: '0.2s' }}></div>\n    </div>\n  )\n}\n\nexport function LoadingCard({ \n  className,\n  children \n}: { \n  className?: string\n  children?: React.ReactNode \n}) {\n  return (\n    <div className={cn(\n      \"rounded-xl border bg-card p-6 shadow-elegant animate-pulse\",\n      className\n    )}>\n      <div className=\"space-y-3\">\n        <div className=\"h-4 bg-muted rounded w-3/4\"></div>\n        <div className=\"h-4 bg-muted rounded w-1/2\"></div>\n        <div className=\"h-4 bg-muted rounded w-5/6\"></div>\n      </div>\n      {children}\n    </div>\n  )\n}\n\nexport function LoadingTable({ \n  rows = 5,\n  columns = 4,\n  className \n}: { \n  rows?: number\n  columns?: number\n  className?: string \n}) {\n  return (\n    <div className={cn(\"space-y-3\", className)}>\n      {/* Header */}\n      <div className=\"flex space-x-4\">\n        {Array.from({ length: columns }).map((_, i) => (\n          <div key={i} className=\"h-4 bg-muted rounded flex-1 animate-pulse\"></div>\n        ))}\n      </div>\n      \n      {/* Rows */}\n      {Array.from({ length: rows }).map((_, rowIndex) => (\n        <div key={rowIndex} className=\"flex space-x-4\">\n          {Array.from({ length: columns }).map((_, colIndex) => (\n            <div \n              key={colIndex} \n              className=\"h-4 bg-muted/60 rounded flex-1 animate-pulse\"\n              style={{ animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s` }}\n            ></div>\n          ))}\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;;;;AASO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,SAAS,EACT,OAAO,YAAY,EACnB,aAAa,KAAK,EACL;IACb,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,mBAAmB,aACrB,0FACA;IAEJ,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;sCACxC,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACpB,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WACxD;;;;;;;;;;;;gBAGH,sBACC,8OAAC;oBAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,yBACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY;8BAEvD;;;;;;8BAGL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;4BAAiD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCAChG,8OAAC;4BAAI,WAAU;4BAAiD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;;;;;;;;;;;;;AAK1G;AAEO,SAAS,eAAe,EAC7B,OAAO,IAAI,EACX,SAAS,EAIV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,iEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAGN;AAEO,SAAS,YAAY,EAC1B,OAAO,IAAI,EACX,SAAS,EAIV;IACC,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;0BACnC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE;;;;;;0BACnC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE;gBAA2C,OAAO;oBAAE,gBAAgB;gBAAO;;;;;;0BAC9G,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE;gBAA2C,OAAO;oBAAE,gBAAgB;gBAAO;;;;;;;;;;;;AAGpH;AAEO,SAAS,YAAY,EAC1B,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8DACA;;0BAEA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;YAEhB;;;;;;;AAGP;AAEO,SAAS,aAAa,EAC3B,OAAO,CAAC,EACR,UAAU,CAAC,EACX,SAAS,EAKV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,kBACvC,8OAAC;wBAAY,WAAU;uBAAb;;;;;;;;;;YAKb,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,8OAAC;oBAAmB,WAAU;8BAC3B,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,CAAC,WAAW,UAAU,QAAQ,IAAI,IAAI,CAAC,CAAC;4BAAC;2BAFhE;;;;;mBAHD;;;;;;;;;;;AAYlB", "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/appointments/appointment-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogFooter, \n  DialogHeader, \n  DialogTitle \n} from '@/components/ui/dialog'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { LoadingSpinner } from '@/components/ui/loading'\nimport { \n  Calendar, \n  Clock, \n  User, \n  Scissors, \n  DollarSign, \n  FileText,\n  AlertCircle\n} from 'lucide-react'\nimport { Appointment, AppointmentFormData, Customer, Staff, Service } from '@/types'\nimport { db } from '@/lib/supabase'\nimport { formatCurrency } from '@/lib/utils'\n\nconst appointmentSchema = z.object({\n  customer_id: z.string().min(1, 'Please select a customer'),\n  staff_id: z.string().min(1, 'Please select a staff member'),\n  service_id: z.string().min(1, 'Please select a service'),\n  appointment_date: z.string().min(1, 'Appointment date is required'),\n  start_time: z.string().min(1, 'Start time is required'),\n  notes: z.string().optional()\n})\n\ninterface AppointmentFormProps {\n  appointment?: Appointment\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess: (appointment: Appointment) => void\n}\n\nexport function AppointmentForm({ appointment, open, onOpenChange, onSuccess }: AppointmentFormProps) {\n  const [loading, setLoading] = useState(false)\n  const [customers, setCustomers] = useState<Customer[]>([])\n  const [staff, setStaff] = useState<Staff[]>([])\n  const [services, setServices] = useState<Service[]>([])\n  const [selectedService, setSelectedService] = useState<Service | null>(null)\n  const [conflicts, setConflicts] = useState<string[]>([])\n  const isEditing = !!appointment\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    setValue,\n    watch\n  } = useForm<AppointmentFormData>({\n    resolver: zodResolver(appointmentSchema),\n    defaultValues: appointment ? {\n      customer_id: appointment.customer_id,\n      staff_id: appointment.staff_id,\n      service_id: appointment.service_id,\n      appointment_date: appointment.appointment_date,\n      start_time: appointment.start_time,\n      notes: appointment.notes || ''\n    } : {\n      customer_id: '',\n      staff_id: '',\n      service_id: '',\n      appointment_date: '',\n      start_time: '',\n      notes: ''\n    }\n  })\n\n  const watchedValues = watch()\n\n  useEffect(() => {\n    if (open) {\n      loadFormData()\n    }\n  }, [open])\n\n  useEffect(() => {\n    // Update selected service when service_id changes\n    const service = services.find(s => s.id === watchedValues.service_id)\n    setSelectedService(service || null)\n  }, [watchedValues.service_id, services])\n\n  useEffect(() => {\n    // Check for conflicts when date, time, or staff changes\n    if (watchedValues.appointment_date && watchedValues.start_time && watchedValues.staff_id && selectedService) {\n      checkConflicts()\n    }\n  }, [watchedValues.appointment_date, watchedValues.start_time, watchedValues.staff_id, selectedService])\n\n  const loadFormData = async () => {\n    try {\n      // Load customers, staff, and services\n      const [customersResult, staffResult, servicesResult] = await Promise.all([\n        db.customers.getAll(),\n        db.staff.getActive(),\n        db.services.getActive()\n      ])\n\n      // Use mock data if database calls fail\n      setCustomers(customersResult.data || mockCustomers)\n      setStaff(staffResult.data || mockStaff)\n      setServices(servicesResult.data || mockServices)\n    } catch (error) {\n      console.error('Error loading form data:', error)\n      setCustomers(mockCustomers)\n      setStaff(mockStaff)\n      setServices(mockServices)\n    }\n  }\n\n  const checkConflicts = async () => {\n    if (!selectedService) return\n\n    try {\n      const startTime = new Date(`${watchedValues.appointment_date}T${watchedValues.start_time}`)\n      const endTime = new Date(startTime.getTime() + selectedService.duration * 60000)\n      \n      // Check staff availability (simplified - in real app would check database)\n      const conflictMessages: string[] = []\n      \n      // Mock conflict detection\n      if (watchedValues.start_time === '12:00') {\n        conflictMessages.push('Staff member has lunch break at this time')\n      }\n      \n      setConflicts(conflictMessages)\n    } catch (error) {\n      console.error('Error checking conflicts:', error)\n    }\n  }\n\n  const calculateEndTime = (startTime: string, duration: number) => {\n    const start = new Date(`2000-01-01T${startTime}`)\n    const end = new Date(start.getTime() + duration * 60000)\n    return end.toTimeString().slice(0, 5)\n  }\n\n  const onSubmit = async (data: AppointmentFormData) => {\n    try {\n      setLoading(true)\n      \n      if (!selectedService) {\n        alert('Please select a service')\n        return\n      }\n\n      const endTime = calculateEndTime(data.start_time, selectedService.duration)\n      \n      const appointmentData = {\n        ...data,\n        end_time: endTime,\n        total_amount: selectedService.price,\n        status: 'scheduled' as const,\n        notes: data.notes || undefined\n      }\n\n      let result\n      if (isEditing) {\n        result = await db.appointments.update(appointment.id, appointmentData)\n      } else {\n        result = await db.appointments.create(appointmentData)\n      }\n\n      if (result.error) {\n        console.error('Error saving appointment:', result.error)\n        alert('Error saving appointment. Please try again.')\n        return\n      }\n\n      onSuccess(result.data)\n      onOpenChange(false)\n      reset()\n    } catch (error) {\n      console.error('Error saving appointment:', error)\n      alert('Error saving appointment. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    onOpenChange(false)\n    reset()\n    setConflicts([])\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center space-x-2\">\n            <Calendar className=\"h-5 w-5\" />\n            <span>{isEditing ? 'Edit Appointment' : 'Book New Appointment'}</span>\n          </DialogTitle>\n          <DialogDescription>\n            {isEditing \n              ? 'Update appointment details and schedule'\n              : 'Schedule a new appointment for a customer'\n            }\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* Customer and Staff Selection */}\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg flex items-center\">\n                <User className=\"h-4 w-4 mr-2\" />\n                Customer & Staff\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"customer_id\" className=\"text-sm font-medium\">\n                    Customer *\n                  </label>\n                  <Select\n                    value={watchedValues.customer_id}\n                    onValueChange={(value) => setValue('customer_id', value)}\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select customer\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {customers.map((customer) => (\n                        <SelectItem key={customer.id} value={customer.id}>\n                          {customer.first_name} {customer.last_name}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  {errors.customer_id && (\n                    <p className=\"text-sm text-red-500\">{errors.customer_id.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"staff_id\" className=\"text-sm font-medium\">\n                    Staff Member *\n                  </label>\n                  <Select\n                    value={watchedValues.staff_id}\n                    onValueChange={(value) => setValue('staff_id', value)}\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select staff\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {staff.map((member) => (\n                        <SelectItem key={member.id} value={member.id}>\n                          {member.first_name} {member.last_name} ({member.role})\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  {errors.staff_id && (\n                    <p className=\"text-sm text-red-500\">{errors.staff_id.message}</p>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Service and Schedule */}\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg flex items-center\">\n                <Scissors className=\"h-4 w-4 mr-2\" />\n                Service & Schedule\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label htmlFor=\"service_id\" className=\"text-sm font-medium\">\n                  Service *\n                </label>\n                <Select\n                  value={watchedValues.service_id}\n                  onValueChange={(value) => setValue('service_id', value)}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select service\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {services.map((service) => (\n                      <SelectItem key={service.id} value={service.id}>\n                        <div className=\"flex justify-between items-center w-full\">\n                          <span>{service.name}</span>\n                          <span className=\"text-muted-foreground ml-2\">\n                            {formatCurrency(service.price)} • {service.duration}m\n                          </span>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {errors.service_id && (\n                  <p className=\"text-sm text-red-500\">{errors.service_id.message}</p>\n                )}\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"appointment_date\" className=\"text-sm font-medium\">\n                    Date *\n                  </label>\n                  <div className=\"relative\">\n                    <Calendar className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      id=\"appointment_date\"\n                      type=\"date\"\n                      {...register('appointment_date')}\n                      className=\"pl-10\"\n                      min={new Date().toISOString().split('T')[0]}\n                      error={!!errors.appointment_date}\n                    />\n                  </div>\n                  {errors.appointment_date && (\n                    <p className=\"text-sm text-red-500\">{errors.appointment_date.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"start_time\" className=\"text-sm font-medium\">\n                    Start Time *\n                  </label>\n                  <div className=\"relative\">\n                    <Clock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      id=\"start_time\"\n                      type=\"time\"\n                      {...register('start_time')}\n                      className=\"pl-10\"\n                      error={!!errors.start_time}\n                    />\n                  </div>\n                  {errors.start_time && (\n                    <p className=\"text-sm text-red-500\">{errors.start_time.message}</p>\n                  )}\n                </div>\n              </div>\n\n              {/* Service Summary */}\n              {selectedService && (\n                <div className=\"p-4 bg-muted/30 rounded-lg\">\n                  <h4 className=\"font-medium mb-2\">Appointment Summary</h4>\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-muted-foreground\">Service:</span>\n                      <p className=\"font-medium\">{selectedService.name}</p>\n                    </div>\n                    <div>\n                      <span className=\"text-muted-foreground\">Duration:</span>\n                      <p className=\"font-medium\">{selectedService.duration} minutes</p>\n                    </div>\n                    <div>\n                      <span className=\"text-muted-foreground\">Price:</span>\n                      <p className=\"font-medium\">{formatCurrency(selectedService.price)}</p>\n                    </div>\n                    {watchedValues.start_time && (\n                      <div>\n                        <span className=\"text-muted-foreground\">End Time:</span>\n                        <p className=\"font-medium\">\n                          {calculateEndTime(watchedValues.start_time, selectedService.duration)}\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Conflicts Warning */}\n              {conflicts.length > 0 && (\n                <div className=\"p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n                  <div className=\"flex items-start space-x-2\">\n                    <AlertCircle className=\"h-4 w-4 text-red-500 mt-0.5\" />\n                    <div>\n                      <h4 className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n                        Scheduling Conflicts\n                      </h4>\n                      <ul className=\"text-sm text-red-600 dark:text-red-400 mt-1\">\n                        {conflicts.map((conflict, index) => (\n                          <li key={index}>• {conflict}</li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Notes */}\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg flex items-center\">\n                <FileText className=\"h-4 w-4 mr-2\" />\n                Additional Notes\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                <label htmlFor=\"notes\" className=\"text-sm font-medium\">\n                  Notes\n                </label>\n                <textarea\n                  id=\"notes\"\n                  {...register('notes')}\n                  placeholder=\"Any special requests or notes for this appointment...\"\n                  className=\"w-full p-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                  rows={3}\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button \n              type=\"submit\" \n              disabled={loading || conflicts.length > 0}\n            >\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <LoadingSpinner size=\"sm\" />\n                  <span>{isEditing ? 'Updating...' : 'Booking...'}</span>\n                </div>\n              ) : (\n                isEditing ? 'Update Appointment' : 'Book Appointment'\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\n// Mock data for demonstration\nconst mockCustomers: Customer[] = [\n  { id: '1', first_name: 'John', last_name: 'Doe', email: '<EMAIL>', phone: '******-0101', created_at: '', updated_at: '' },\n  { id: '2', first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>', phone: '******-0102', created_at: '', updated_at: '' }\n]\n\nconst mockStaff: Staff[] = [\n  { id: '1', first_name: 'Maria', last_name: 'Garcia', email: '<EMAIL>', phone: '******-0202', role: 'barber', hire_date: '2021-03-10', is_active: true, created_at: '', updated_at: '' },\n  { id: '2', first_name: 'James', last_name: 'Thompson', email: '<EMAIL>', phone: '******-0203', role: 'stylist', hire_date: '2021-06-20', is_active: true, created_at: '', updated_at: '' }\n]\n\nconst mockServices: Service[] = [\n  { id: '1', name: 'Classic Haircut', duration: 30, price: 25.00, category: 'haircut', is_active: true, created_at: '', updated_at: '' },\n  { id: '2', name: 'Women\\'s Cut & Style', duration: 60, price: 45.00, category: 'styling', is_active: true, created_at: '', updated_at: '' }\n]\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAQA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AApCA;;;;;;;;;;;;;;;AAsCA,MAAM,oBAAoB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,kBAAkB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACpC,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AASO,SAAS,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAwB;IAClG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,YAAY,CAAC,CAAC;IAEpB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAuB;QAC/B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe,cAAc;YAC3B,aAAa,YAAY,WAAW;YACpC,UAAU,YAAY,QAAQ;YAC9B,YAAY,YAAY,UAAU;YAClC,kBAAkB,YAAY,gBAAgB;YAC9C,YAAY,YAAY,UAAU;YAClC,OAAO,YAAY,KAAK,IAAI;QAC9B,IAAI;YACF,aAAa;YACb,UAAU;YACV,YAAY;YACZ,kBAAkB;YAClB,YAAY;YACZ,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,UAAU;QACpE,mBAAmB,WAAW;IAChC,GAAG;QAAC,cAAc,UAAU;QAAE;KAAS;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wDAAwD;QACxD,IAAI,cAAc,gBAAgB,IAAI,cAAc,UAAU,IAAI,cAAc,QAAQ,IAAI,iBAAiB;YAC3G;QACF;IACF,GAAG;QAAC,cAAc,gBAAgB;QAAE,cAAc,UAAU;QAAE,cAAc,QAAQ;QAAE;KAAgB;IAEtG,MAAM,eAAe;QACnB,IAAI;YACF,sCAAsC;YACtC,MAAM,CAAC,iBAAiB,aAAa,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACvE,sHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,MAAM;gBACnB,sHAAA,CAAA,KAAE,CAAC,KAAK,CAAC,SAAS;gBAClB,sHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC,SAAS;aACtB;YAED,uCAAuC;YACvC,aAAa,gBAAgB,IAAI,IAAI;YACrC,SAAS,YAAY,IAAI,IAAI;YAC7B,YAAY,eAAe,IAAI,IAAI;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,aAAa;YACb,SAAS;YACT,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,YAAY,IAAI,KAAK,GAAG,cAAc,gBAAgB,CAAC,CAAC,EAAE,cAAc,UAAU,EAAE;YAC1F,MAAM,UAAU,IAAI,KAAK,UAAU,OAAO,KAAK,gBAAgB,QAAQ,GAAG;YAE1E,2EAA2E;YAC3E,MAAM,mBAA6B,EAAE;YAErC,0BAA0B;YAC1B,IAAI,cAAc,UAAU,KAAK,SAAS;gBACxC,iBAAiB,IAAI,CAAC;YACxB;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,mBAAmB,CAAC,WAAmB;QAC3C,MAAM,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,WAAW;QAChD,MAAM,MAAM,IAAI,KAAK,MAAM,OAAO,KAAK,WAAW;QAClD,OAAO,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG;IACrC;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,iBAAiB;gBACpB,MAAM;gBACN;YACF;YAEA,MAAM,UAAU,iBAAiB,KAAK,UAAU,EAAE,gBAAgB,QAAQ;YAE1E,MAAM,kBAAkB;gBACtB,GAAG,IAAI;gBACP,UAAU;gBACV,cAAc,gBAAgB,KAAK;gBACnC,QAAQ;gBACR,OAAO,KAAK,KAAK,IAAI;YACvB;YAEA,IAAI;YACJ,IAAI,WAAW;gBACb,SAAS,MAAM,sHAAA,CAAA,KAAE,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;YACxD,OAAO;gBACL,SAAS,MAAM,sHAAA,CAAA,KAAE,CAAC,YAAY,CAAC,MAAM,CAAC;YACxC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,QAAQ,KAAK,CAAC,6BAA6B,OAAO,KAAK;gBACvD,MAAM;gBACN;YACF;YAEA,UAAU,OAAO,IAAI;YACrB,aAAa;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb;QACA,aAAa,EAAE;IACjB;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,YAAY,qBAAqB;;;;;;;;;;;;sCAE1C,8OAAC,kIAAA,CAAA,oBAAiB;sCACf,YACG,4CACA;;;;;;;;;;;;8BAKR,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIrC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAAsB;;;;;;kEAG7D,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,cAAc,WAAW;wDAChC,eAAe,CAAC,QAAU,SAAS,eAAe;;0EAElD,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;0EACX,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;wEAAmB,OAAO,SAAS,EAAE;;4EAC7C,SAAS,UAAU;4EAAC;4EAAE,SAAS,SAAS;;uEAD1B,SAAS,EAAE;;;;;;;;;;;;;;;;oDAMjC,OAAO,WAAW,kBACjB,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0DAInE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAsB;;;;;;kEAG1D,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,cAAc,QAAQ;wDAC7B,eAAe,CAAC,QAAU,SAAS,YAAY;;0EAE/C,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,8OAAC,kIAAA,CAAA,gBAAa;0EACX,MAAM,GAAG,CAAC,CAAC,uBACV,8OAAC,kIAAA,CAAA,aAAU;wEAAiB,OAAO,OAAO,EAAE;;4EACzC,OAAO,UAAU;4EAAC;4EAAE,OAAO,SAAS;4EAAC;4EAAG,OAAO,IAAI;4EAAC;;uEADtC,OAAO,EAAE;;;;;;;;;;;;;;;;oDAM/B,OAAO,QAAQ,kBACd,8OAAC;wDAAE,WAAU;kEAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQtE,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIzC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAAsB;;;;;;8DAG5D,8OAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,cAAc,UAAU;oDAC/B,eAAe,CAAC,QAAU,SAAS,cAAc;;sEAEjD,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;sEACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,QAAQ,EAAE;8EAC5C,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;0FAAM,QAAQ,IAAI;;;;;;0FACnB,8OAAC;gFAAK,WAAU;;oFACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;oFAAE;oFAAI,QAAQ,QAAQ;oFAAC;;;;;;;;;;;;;mEAJzC,QAAQ,EAAE;;;;;;;;;;;;;;;;gDAWhC,OAAO,UAAU,kBAChB,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;sDAIlE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAmB,WAAU;sEAAsB;;;;;;sEAGlE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACJ,GAAG,SAAS,mBAAmB;oEAChC,WAAU;oEACV,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oEAC3C,OAAO,CAAC,CAAC,OAAO,gBAAgB;;;;;;;;;;;;wDAGnC,OAAO,gBAAgB,kBACtB,8OAAC;4DAAE,WAAU;sEAAwB,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;8DAIxE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAAsB;;;;;;sEAG5D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACJ,GAAG,SAAS,aAAa;oEAC1B,WAAU;oEACV,OAAO,CAAC,CAAC,OAAO,UAAU;;;;;;;;;;;;wDAG7B,OAAO,UAAU,kBAChB,8OAAC;4DAAE,WAAU;sEAAwB,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;wCAMnE,iCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAE,WAAU;8EAAe,gBAAgB,IAAI;;;;;;;;;;;;sEAElD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAE,WAAU;;wEAAe,gBAAgB,QAAQ;wEAAC;;;;;;;;;;;;;sEAEvD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAE,WAAU;8EAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,KAAK;;;;;;;;;;;;wDAEjE,cAAc,UAAU,kBACvB,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAE,WAAU;8EACV,iBAAiB,cAAc,UAAU,EAAE,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;wCAS/E,UAAU,MAAM,GAAG,mBAClB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqD;;;;;;0EAGnE,8OAAC;gEAAG,WAAU;0EACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;;4EAAe;4EAAG;;uEAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWzB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIzC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAsB;;;;;;0DAGvD,8OAAC;gDACC,IAAG;gDACF,GAAG,SAAS,QAAQ;gDACrB,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAMd,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,WAAW,UAAU,MAAM,GAAG;8CAEvC,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,MAAK;;;;;;0DACrB,8OAAC;0DAAM,YAAY,gBAAgB;;;;;;;;;;;+CAGrC,YAAY,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;AAEA,8BAA8B;AAC9B,MAAM,gBAA4B;IAChC;QAAE,IAAI;QAAK,YAAY;QAAQ,WAAW;QAAO,OAAO;QAAoB,OAAO;QAAe,YAAY;QAAI,YAAY;IAAG;IACjI;QAAE,IAAI;QAAK,YAAY;QAAQ,WAAW;QAAS,OAAO;QAAoB,OAAO;QAAe,YAAY;QAAI,YAAY;IAAG;CACpI;AAED,MAAM,YAAqB;IACzB;QAAE,IAAI;QAAK,YAAY;QAAS,WAAW;QAAU,OAAO;QAAuB,OAAO;QAAe,MAAM;QAAU,WAAW;QAAc,WAAW;QAAM,YAAY;QAAI,YAAY;IAAG;IAClM;QAAE,IAAI;QAAK,YAAY;QAAS,WAAW;QAAY,OAAO;QAAuB,OAAO;QAAe,MAAM;QAAW,WAAW;QAAc,WAAW;QAAM,YAAY;QAAI,YAAY;IAAG;CACtM;AAED,MAAM,eAA0B;IAC9B;QAAE,IAAI;QAAK,MAAM;QAAmB,UAAU;QAAI,OAAO;QAAO,UAAU;QAAW,WAAW;QAAM,YAAY;QAAI,YAAY;IAAG;IACrI;QAAE,IAAI;QAAK,MAAM;QAAwB,UAAU;QAAI,OAAO;QAAO,UAAU;QAAW,WAAW;QAAM,YAAY;QAAI,YAAY;IAAG;CAC3I", "debugId": null}}, {"offset": {"line": 2866, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        purple:\n          \"border-transparent bg-purple-500 text-white hover:bg-purple-600\",\n        pink:\n          \"border-transparent bg-pink-500 text-white hover:bg-pink-600\",\n        indigo:\n          \"border-transparent bg-indigo-500 text-white hover:bg-indigo-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,QACE;YACF,MACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 2914, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/empty-state.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"./button\"\nimport { Card, CardContent } from \"./card\"\n\ninterface EmptyStateProps {\n  icon?: React.ReactNode\n  title: string\n  description?: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n  className?: string\n}\n\nexport function EmptyState({\n  icon,\n  title,\n  description,\n  action,\n  className\n}: EmptyStateProps) {\n  return (\n    <Card className={cn(\"shadow-elegant border-dashed border-2\", className)}>\n      <CardContent className=\"flex flex-col items-center justify-center py-12 px-6 text-center\">\n        {icon && (\n          <div className=\"mb-4 p-3 rounded-full bg-muted/50\">\n            {icon}\n          </div>\n        )}\n        \n        <h3 className=\"text-lg font-semibold text-foreground mb-2\">\n          {title}\n        </h3>\n        \n        {description && (\n          <p className=\"text-muted-foreground text-sm mb-6 max-w-sm\">\n            {description}\n          </p>\n        )}\n        \n        {action && (\n          <Button onClick={action.onClick} className=\"mt-2\">\n            {action.label}\n          </Button>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n\nexport function EmptyTableState({\n  icon,\n  title,\n  description,\n  action,\n  className\n}: EmptyStateProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center py-16 px-6 text-center\", className)}>\n      {icon && (\n        <div className=\"mb-4 p-4 rounded-full bg-muted/30\">\n          {icon}\n        </div>\n      )}\n      \n      <h3 className=\"text-xl font-semibold text-foreground mb-2\">\n        {title}\n      </h3>\n      \n      {description && (\n        <p className=\"text-muted-foreground mb-6 max-w-md\">\n          {description}\n        </p>\n      )}\n      \n      {action && (\n        <Button onClick={action.onClick} size=\"lg\">\n          {action.label}\n        </Button>\n      )}\n    </div>\n  )\n}\n\nexport function EmptySearchState({\n  searchTerm,\n  onClear,\n  className\n}: {\n  searchTerm: string\n  onClear?: () => void\n  className?: string\n}) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center py-12 px-6 text-center\", className)}>\n      <div className=\"mb-4 p-4 rounded-full bg-muted/30\">\n        <svg\n          className=\"h-8 w-8 text-muted-foreground\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n          />\n        </svg>\n      </div>\n      \n      <h3 className=\"text-lg font-semibold text-foreground mb-2\">\n        No results found\n      </h3>\n      \n      <p className=\"text-muted-foreground mb-4\">\n        No results found for <span className=\"font-medium\">\"{searchTerm}\"</span>\n      </p>\n      \n      {onClear && (\n        <Button variant=\"outline\" onClick={onClear}>\n          Clear search\n        </Button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAaO,SAAS,WAAW,EACzB,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,SAAS,EACO;IAChB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;kBAC3D,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;gBACpB,sBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,8OAAC;oBAAG,WAAU;8BACX;;;;;;gBAGF,6BACC,8OAAC;oBAAE,WAAU;8BACV;;;;;;gBAIJ,wBACC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS,OAAO,OAAO;oBAAE,WAAU;8BACxC,OAAO,KAAK;;;;;;;;;;;;;;;;;AAMzB;AAEO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,SAAS,EACO;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;;YACpF,sBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAGF,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,wBACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,SAAS,OAAO,OAAO;gBAAE,MAAK;0BACnC,OAAO,KAAK;;;;;;;;;;;;AAKvB;AAEO,SAAS,iBAAiB,EAC/B,UAAU,EACV,OAAO,EACP,SAAS,EAKV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;;0BACrF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;0BAKR,8OAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAI3D,8OAAC;gBAAE,WAAU;;oBAA6B;kCACnB,8OAAC;wBAAK,WAAU;;4BAAc;4BAAE;4BAAW;;;;;;;;;;;;;YAGjE,yBACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,SAAS;0BAAS;;;;;;;;;;;;AAMpD", "debugId": null}}, {"offset": {"line": 3105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/appointments/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { PageHeader, PageHeaderActions } from '@/components/layout/page-header'\nimport { AppointmentForm } from '@/components/appointments/appointment-form'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { EmptyTableState } from '@/components/ui/empty-state'\nimport { Loading } from '@/components/ui/loading'\nimport { \n  Calendar, \n  Plus, \n  Search, \n  Filter, \n  Clock,\n  User,\n  Scissors,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  Eye,\n  Edit,\n  Trash2\n} from 'lucide-react'\nimport { Appointment } from '@/types'\nimport { db } from '@/lib/supabase'\nimport { formatDate, formatTime, formatCurrency } from '@/lib/utils'\n\nexport default function AppointmentsPage() {\n  const [appointments, setAppointments] = useState<Appointment[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([])\n  const [selectedStatus, setSelectedStatus] = useState<string>('all')\n  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0])\n  const [showForm, setShowForm] = useState(false)\n  const [editingAppointment, setEditingAppointment] = useState<Appointment | undefined>()\n\n  useEffect(() => {\n    loadAppointments()\n  }, [])\n\n  useEffect(() => {\n    // Filter appointments based on search term and status\n    let filtered = appointments\n\n    if (searchTerm.trim() !== '') {\n      filtered = filtered.filter(appointment =>\n        appointment.customer?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        appointment.customer?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        appointment.staff?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        appointment.staff?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        appointment.service?.name?.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    if (selectedStatus !== 'all') {\n      filtered = filtered.filter(appointment => appointment.status === selectedStatus)\n    }\n\n    setFilteredAppointments(filtered)\n  }, [appointments, searchTerm, selectedStatus])\n\n  const loadAppointments = async () => {\n    try {\n      setLoading(true)\n      const { data, error } = await db.appointments.getAll()\n      \n      if (error) {\n        console.error('Error loading appointments:', error)\n        // For demo purposes, use mock data if Supabase is not configured\n        setAppointments(mockAppointments)\n      } else {\n        setAppointments(data || [])\n      }\n    } catch (error) {\n      console.error('Error loading appointments:', error)\n      // Use mock data as fallback\n      setAppointments(mockAppointments)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDeleteAppointment = async (appointmentId: string) => {\n    if (confirm('Are you sure you want to delete this appointment?')) {\n      try {\n        const { error } = await db.appointments.delete(appointmentId)\n        if (error) {\n          console.error('Error deleting appointment:', error)\n        } else {\n          setAppointments(appointments.filter(a => a.id !== appointmentId))\n        }\n      } catch (error) {\n        console.error('Error deleting appointment:', error)\n      }\n    }\n  }\n\n  const handleUpdateStatus = async (appointment: Appointment, newStatus: string) => {\n    try {\n      const { error } = await db.appointments.update(appointment.id, { \n        status: newStatus \n      })\n      if (error) {\n        console.error('Error updating appointment:', error)\n      } else {\n        setAppointments(appointments.map(a => \n          a.id === appointment.id ? { ...a, status: newStatus as any } : a\n        ))\n      }\n    } catch (error) {\n      console.error('Error updating appointment:', error)\n    }\n  }\n\n  const handleAddAppointment = () => {\n    setEditingAppointment(undefined)\n    setShowForm(true)\n  }\n\n  const handleEditAppointment = (appointment: Appointment) => {\n    setEditingAppointment(appointment)\n    setShowForm(true)\n  }\n\n  const handleFormSuccess = (appointment: Appointment) => {\n    if (editingAppointment) {\n      // Update existing appointment\n      setAppointments(appointments.map(a => a.id === appointment.id ? appointment : a))\n    } else {\n      // Add new appointment\n      setAppointments([appointment, ...appointments])\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4\" />\n      case 'cancelled':\n      case 'no_show':\n        return <XCircle className=\"h-4 w-4\" />\n      case 'in_progress':\n        return <Clock className=\"h-4 w-4\" />\n      case 'confirmed':\n        return <CheckCircle className=\"h-4 w-4\" />\n      default:\n        return <AlertCircle className=\"h-4 w-4\" />\n    }\n  }\n\n  const getStatusBadgeVariant = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'success'\n      case 'cancelled':\n      case 'no_show':\n        return 'destructive'\n      case 'in_progress':\n        return 'warning'\n      case 'confirmed':\n        return 'info'\n      default:\n        return 'secondary'\n    }\n  }\n\n  const statusOptions = [\n    { value: 'all', label: '全部状态' },\n    { value: 'scheduled', label: '已安排' },\n    { value: 'confirmed', label: '已确认' },\n    { value: 'in_progress', label: '进行中' },\n    { value: 'completed', label: '已完成' },\n    { value: 'cancelled', label: '已取消' },\n    { value: 'no_show', label: '未到场' }\n  ]\n\n  const todayAppointments = appointments.filter(a => a.appointment_date === selectedDate)\n  const upcomingAppointments = appointments.filter(a => \n    new Date(a.appointment_date) > new Date() && a.status !== 'cancelled'\n  )\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n        <PageHeader\n          title=\"预约管理\"\n          description=\"管理预约、排班和预约日历\"\n          icon={<Calendar className=\"h-6 w-6 text-white\" />}\n          actions={\n            <PageHeaderActions>\n              <Button variant=\"outline\">\n                <Calendar className=\"h-4 w-4 mr-2\" />\n                日历视图\n              </Button>\n              <Button onClick={handleAddAppointment}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                预约服务\n              </Button>\n            </PageHeaderActions>\n          }\n        />\n\n        <div className=\"space-y-6\">\n          {/* Search and Filters */}\n          <Card className=\"shadow-elegant\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"relative flex-1\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                  <Input\n                    placeholder=\"按客户、员工或服务搜索...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n                <div className=\"flex gap-2\">\n                  <Input\n                    type=\"date\"\n                    value={selectedDate}\n                    onChange={(e) => setSelectedDate(e.target.value)}\n                    className=\"w-auto\"\n                  />\n                  {statusOptions.map((status) => (\n                    <Button\n                      key={status.value}\n                      variant={selectedStatus === status.value ? \"default\" : \"outline\"}\n                      size=\"sm\"\n                      onClick={() => setSelectedStatus(status.value)}\n                    >\n                      {status.label}\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Appointment Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">今日预约</p>\n                    <p className=\"text-2xl font-bold\">{todayAppointments.length}</p>\n                  </div>\n                  <Calendar className=\"h-8 w-8 text-blue-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">即将到来</p>\n                    <p className=\"text-2xl font-bold\">{upcomingAppointments.length}</p>\n                  </div>\n                  <Clock className=\"h-8 w-8 text-orange-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">已完成</p>\n                    <p className=\"text-2xl font-bold\">\n                      {appointments.filter(a => a.status === 'completed').length}\n                    </p>\n                  </div>\n                  <CheckCircle className=\"h-8 w-8 text-green-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">今日收入</p>\n                    <p className=\"text-2xl font-bold\">\n                      {formatCurrency(\n                        todayAppointments\n                          .filter(a => a.status === 'completed')\n                          .reduce((sum, a) => sum + a.total_amount, 0)\n                      )}\n                    </p>\n                  </div>\n                  <div className=\"h-8 w-8 rounded-full bg-green-100 flex items-center justify-center\">\n                    <span className=\"text-green-600 font-bold\">$</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Appointments List */}\n          <Card className=\"shadow-elegant\">\n            <CardHeader>\n              <CardTitle>预约安排</CardTitle>\n            </CardHeader>\n            <CardContent>\n              {loading ? (\n                <Loading text=\"Loading appointments...\" />\n              ) : filteredAppointments.length === 0 ? (\n                searchTerm || selectedStatus !== 'all' ? (\n                  <EmptyTableState\n                    icon={<Search className=\"h-8 w-8 text-muted-foreground\" />}\n                    title=\"No appointments found\"\n                    description=\"No appointments match your current filters\"\n                    action={{\n                      label: \"Clear filters\",\n                      onClick: () => {\n                        setSearchTerm('')\n                        setSelectedStatus('all')\n                      }\n                    }}\n                  />\n                ) : (\n                  <EmptyTableState\n                    icon={<Calendar className=\"h-8 w-8 text-muted-foreground\" />}\n                    title=\"No appointments scheduled\"\n                    description=\"Start booking appointments for your customers\"\n                    action={{\n                      label: \"Book Appointment\",\n                      onClick: handleAddAppointment\n                    }}\n                  />\n                )\n              ) : (\n                <div className=\"space-y-4\">\n                  {filteredAppointments.map((appointment) => (\n                    <AppointmentCard\n                      key={appointment.id}\n                      appointment={appointment}\n                      onEdit={() => handleEditAppointment(appointment)}\n                      onDelete={handleDeleteAppointment}\n                      onUpdateStatus={handleUpdateStatus}\n                      getStatusIcon={getStatusIcon}\n                      getStatusBadgeVariant={getStatusBadgeVariant}\n                    />\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Appointment Form Dialog */}\n        <AppointmentForm\n          appointment={editingAppointment}\n          open={showForm}\n          onOpenChange={setShowForm}\n          onSuccess={handleFormSuccess}\n        />\n      </MainLayout>\n    </ProtectedRoute>\n  )\n}\n\ninterface AppointmentCardProps {\n  appointment: Appointment\n  onEdit: () => void\n  onDelete: (id: string) => void\n  onUpdateStatus: (appointment: Appointment, status: string) => void\n  getStatusIcon: (status: string) => React.ReactNode\n  getStatusBadgeVariant: (status: string) => any\n}\n\nfunction AppointmentCard({ \n  appointment, \n  onEdit, \n  onDelete, \n  onUpdateStatus, \n  getStatusIcon, \n  getStatusBadgeVariant \n}: AppointmentCardProps) {\n  return (\n    <div className=\"p-4 border rounded-lg hover:shadow-elegant transition-all duration-200 bg-card\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center\">\n            <span className=\"text-white font-semibold\">\n              {appointment.customer?.first_name?.[0]}{appointment.customer?.last_name?.[0]}\n            </span>\n          </div>\n          <div>\n            <div className=\"flex items-center space-x-2 mb-1\">\n              <h3 className=\"font-semibold text-foreground\">\n                {appointment.customer?.first_name} {appointment.customer?.last_name}\n              </h3>\n              <Badge variant={getStatusBadgeVariant(appointment.status)} className=\"flex items-center space-x-1\">\n                {getStatusIcon(appointment.status)}\n                <span className=\"capitalize\">{appointment.status.replace('_', ' ')}</span>\n              </Badge>\n            </div>\n            <div className=\"flex items-center space-x-4 text-sm text-muted-foreground\">\n              <div className=\"flex items-center\">\n                <Calendar className=\"h-3 w-3 mr-1\" />\n                {formatDate(appointment.appointment_date)}\n              </div>\n              <div className=\"flex items-center\">\n                <Clock className=\"h-3 w-3 mr-1\" />\n                {formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}\n              </div>\n              <div className=\"flex items-center\">\n                <Scissors className=\"h-3 w-3 mr-1\" />\n                {appointment.service?.name}\n              </div>\n              <div className=\"flex items-center\">\n                <User className=\"h-3 w-3 mr-1\" />\n                {appointment.staff?.first_name} {appointment.staff?.last_name}\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <div className=\"text-right mr-4\">\n            <p className=\"font-semibold\">{formatCurrency(appointment.total_amount)}</p>\n            <p className=\"text-xs text-muted-foreground\">{appointment.service?.duration}m</p>\n          </div>\n          \n          {appointment.status === 'scheduled' && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => onUpdateStatus(appointment, 'confirmed')}\n            >\n              确认\n            </Button>\n          )}\n          \n          {appointment.status === 'confirmed' && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => onUpdateStatus(appointment, 'in_progress')}\n            >\n              开始\n            </Button>\n          )}\n          \n          {appointment.status === 'in_progress' && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => onUpdateStatus(appointment, 'completed')}\n            >\n              完成\n            </Button>\n          )}\n          \n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onEdit}\n          >\n            <Edit className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => onDelete(appointment.id)}\n          >\n            <Trash2 className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n      \n      {appointment.notes && (\n        <div className=\"mt-3 pt-3 border-t\">\n          <p className=\"text-sm text-muted-foreground\">\n            <strong>备注:</strong> {appointment.notes}\n          </p>\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Mock data for demonstration\nconst mockAppointments: Appointment[] = [\n  {\n    id: '1',\n    customer_id: '1',\n    staff_id: '1',\n    service_id: '1',\n    appointment_date: new Date().toISOString().split('T')[0],\n    start_time: '10:00',\n    end_time: '10:30',\n    status: 'confirmed',\n    total_amount: 25.00,\n    notes: '老客户，喜欢短发',\n    created_at: '2024-12-10T10:00:00Z',\n    updated_at: '2024-12-15T10:30:00Z',\n    customer: { \n      id: '1', \n      first_name: '张',\n      last_name: '三',\n      email: '<EMAIL>', \n      phone: '******-0101',\n      created_at: '', \n      updated_at: '' \n    },\n    staff: { \n      id: '1', \n      first_name: '玛丽亚',\n      last_name: '加西亚',\n      email: '<EMAIL>', \n      phone: '******-0202', \n      role: 'barber', \n      hire_date: '2021-03-10', \n      is_active: true, \n      created_at: '', \n      updated_at: '' \n    },\n    service: { \n      id: '1', \n      name: '经典理发',\n      duration: 30, \n      price: 25.00, \n      category: 'haircut', \n      is_active: true, \n      created_at: '', \n      updated_at: '' \n    }\n  },\n  {\n    id: '2',\n    customer_id: '2',\n    staff_id: '2',\n    service_id: '2',\n    appointment_date: new Date().toISOString().split('T')[0],\n    start_time: '14:00',\n    end_time: '15:00',\n    status: 'in_progress',\n    total_amount: 45.00,\n    created_at: '2024-12-10T10:00:00Z',\n    updated_at: '2024-12-15T14:00:00Z',\n    customer: { \n      id: '2', \n      first_name: '李',\n      last_name: '四',\n      email: '<EMAIL>', \n      phone: '******-0102',\n      created_at: '', \n      updated_at: '' \n    },\n    staff: { \n      id: '2', \n      first_name: '詹姆斯',\n      last_name: '汤普森',\n      email: '<EMAIL>', \n      phone: '******-0203', \n      role: 'stylist', \n      hire_date: '2021-06-20', \n      is_active: true, \n      created_at: '', \n      updated_at: '' \n    },\n    service: { \n      id: '2', \n      name: '女士剪发造型',\n      duration: 60, \n      price: 45.00, \n      category: 'styling', \n      is_active: true, \n      created_at: '', \n      updated_at: '' \n    }\n  }\n]\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AA9BA;;;;;;;;;;;;;;;;AAgCe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC/F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sDAAsD;QACtD,IAAI,WAAW;QAEf,IAAI,WAAW,IAAI,OAAO,IAAI;YAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,cACzB,YAAY,QAAQ,EAAE,YAAY,cAAc,SAAS,WAAW,WAAW,OAC/E,YAAY,QAAQ,EAAE,WAAW,cAAc,SAAS,WAAW,WAAW,OAC9E,YAAY,KAAK,EAAE,YAAY,cAAc,SAAS,WAAW,WAAW,OAC5E,YAAY,KAAK,EAAE,WAAW,cAAc,SAAS,WAAW,WAAW,OAC3E,YAAY,OAAO,EAAE,MAAM,cAAc,SAAS,WAAW,WAAW;QAE5E;QAEA,IAAI,mBAAmB,OAAO;YAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,cAAe,YAAY,MAAM,KAAK;QACnE;QAEA,wBAAwB;IAC1B,GAAG;QAAC;QAAc;QAAY;KAAe;IAE7C,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,KAAE,CAAC,YAAY,CAAC,MAAM;YAEpD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,iEAAiE;gBACjE,gBAAgB;YAClB,OAAO;gBACL,gBAAgB,QAAQ,EAAE;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,4BAA4B;YAC5B,gBAAgB;QAClB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,QAAQ,sDAAsD;YAChE,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,KAAE,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC/C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C,OAAO;oBACL,gBAAgB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;IACF;IAEA,MAAM,qBAAqB,OAAO,aAA0B;QAC1D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,KAAE,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;gBAC7D,QAAQ;YACV;YACA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,OAAO;gBACL,gBAAgB,aAAa,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,YAAY,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,QAAQ;oBAAiB,IAAI;YAEnE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,uBAAuB;QAC3B,sBAAsB;QACtB,YAAY;IACd;IAEA,MAAM,wBAAwB,CAAC;QAC7B,sBAAsB;QACtB,YAAY;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,oBAAoB;YACtB,8BAA8B;YAC9B,gBAAgB,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;QAChF,OAAO;YACL,sBAAsB;YACtB,gBAAgB;gBAAC;mBAAgB;aAAa;QAChD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAO,OAAO;QAAO;QAC9B;YAAE,OAAO;YAAa,OAAO;QAAM;QACnC;YAAE,OAAO;YAAa,OAAO;QAAM;QACnC;YAAE,OAAO;YAAe,OAAO;QAAM;QACrC;YAAE,OAAO;YAAa,OAAO;QAAM;QACnC;YAAE,OAAO;YAAa,OAAO;QAAM;QACnC;YAAE,OAAO;YAAW,OAAO;QAAM;KAClC;IAED,MAAM,oBAAoB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK;IAC1E,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA,IAC/C,IAAI,KAAK,EAAE,gBAAgB,IAAI,IAAI,UAAU,EAAE,MAAM,KAAK;IAG5D,qBACE,8OAAC,gJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;;8BACT,8OAAC,8IAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;oBACZ,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,uBACE,8OAAC,8IAAA,CAAA,oBAAiB;;0CAChB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,WAAU;;;;;;gDAEX,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,kIAAA,CAAA,SAAM;wDAEL,SAAS,mBAAmB,OAAO,KAAK,GAAG,YAAY;wDACvD,MAAK;wDACL,SAAS,IAAM,kBAAkB,OAAO,KAAK;kEAE5C,OAAO,KAAK;uDALR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAc7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAsB,kBAAkB,MAAM;;;;;;;;;;;;8DAE7D,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAsB,qBAAqB,MAAM;;;;;;;;;;;;8DAEhE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKvB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEACV,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;8DAG9D,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK7B,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EACZ,kBACG,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;;;;;;;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;8CACT,wBACC,8OAAC,mIAAA,CAAA,UAAO;wCAAC,MAAK;;;;;+CACZ,qBAAqB,MAAM,KAAK,IAClC,cAAc,mBAAmB,sBAC/B,8OAAC,0IAAA,CAAA,kBAAe;wCACd,oBAAM,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACxB,OAAM;wCACN,aAAY;wCACZ,QAAQ;4CACN,OAAO;4CACP,SAAS;gDACP,cAAc;gDACd,kBAAkB;4CACpB;wCACF;;;;;6DAGF,8OAAC,0IAAA,CAAA,kBAAe;wCACd,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAC1B,OAAM;wCACN,aAAY;wCACZ,QAAQ;4CACN,OAAO;4CACP,SAAS;wCACX;;;;;6DAIJ,8OAAC;wCAAI,WAAU;kDACZ,qBAAqB,GAAG,CAAC,CAAC,4BACzB,8OAAC;gDAEC,aAAa;gDACb,QAAQ,IAAM,sBAAsB;gDACpC,UAAU;gDACV,gBAAgB;gDAChB,eAAe;gDACf,uBAAuB;+CANlB,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgBjC,8OAAC,yJAAA,CAAA,kBAAe;oBACd,aAAa;oBACb,MAAM;oBACN,cAAc;oBACd,WAAW;;;;;;;;;;;;;;;;;AAKrB;AAWA,SAAS,gBAAgB,EACvB,WAAW,EACX,MAAM,EACN,QAAQ,EACR,cAAc,EACd,aAAa,EACb,qBAAqB,EACA;IACrB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCACb,YAAY,QAAQ,EAAE,YAAY,CAAC,EAAE;wCAAE,YAAY,QAAQ,EAAE,WAAW,CAAC,EAAE;;;;;;;;;;;;0CAGhF,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDACX,YAAY,QAAQ,EAAE;oDAAW;oDAAE,YAAY,QAAQ,EAAE;;;;;;;0DAE5D,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,sBAAsB,YAAY,MAAM;gDAAG,WAAU;;oDAClE,cAAc,YAAY,MAAM;kEACjC,8OAAC;wDAAK,WAAU;kEAAc,YAAY,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;kDAGlE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,gBAAgB;;;;;;;0DAE1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,UAAU;oDAAE;oDAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,YAAY,QAAQ;;;;;;;0DAEzE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,YAAY,OAAO,EAAE;;;;;;;0DAExB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,YAAY,KAAK,EAAE;oDAAW;oDAAE,YAAY,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAiB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,YAAY;;;;;;kDACrE,8OAAC;wCAAE,WAAU;;4CAAiC,YAAY,OAAO,EAAE;4CAAS;;;;;;;;;;;;;4BAG7E,YAAY,MAAM,KAAK,6BACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe,aAAa;0CAC5C;;;;;;4BAKF,YAAY,MAAM,KAAK,6BACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe,aAAa;0CAC5C;;;;;;4BAKF,YAAY,MAAM,KAAK,+BACtB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe,aAAa;0CAC5C;;;;;;0CAKH,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;0CAET,cAAA,8OAAC,2MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,YAAY,EAAE;0CAEtC,cAAA,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAKvB,YAAY,KAAK,kBAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAAY;wBAAE,YAAY,KAAK;;;;;;;;;;;;;;;;;;AAMnD;AAEA,8BAA8B;AAC9B,MAAM,mBAAkC;IACtC;QACE,IAAI;QACJ,aAAa;QACb,UAAU;QACV,YAAY;QACZ,kBAAkB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,cAAc;QACd,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,IAAI;YACJ,YAAY;YACZ,WAAW;YACX,OAAO;YACP,OAAO;YACP,YAAY;YACZ,YAAY;QACd;QACA,OAAO;YACL,IAAI;YACJ,YAAY;YACZ,WAAW;YACX,OAAO;YACP,OAAO;YACP,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;YACZ,YAAY;QACd;QACA,SAAS;YACP,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;YACV,WAAW;YACX,YAAY;YACZ,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,aAAa;QACb,UAAU;QACV,YAAY;QACZ,kBAAkB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACxD,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,IAAI;YACJ,YAAY;YACZ,WAAW;YACX,OAAO;YACP,OAAO;YACP,YAAY;YACZ,YAAY;QACd;QACA,OAAO;YACL,IAAI;YACJ,YAAY;YACZ,WAAW;YACX,OAAO;YACP,OAAO;YACP,MAAM;YACN,WAAW;YACX,WAAW;YACX,YAAY;YACZ,YAAY;QACd;QACA,SAAS;YACP,IAAI;YACJ,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;YACV,WAAW;YACX,YAAY;YACZ,YAAY;QACd;IACF;CACD", "debugId": null}}]}