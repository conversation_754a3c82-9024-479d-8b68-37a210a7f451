'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Settings, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  ExternalLink,
  Key,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { 
  HolidayApiService, 
  CachedHolidayService,
  HolidayCache,
  HOLIDAY_API_CONFIGS 
} from '@/lib/holiday-api'

interface HolidayApiConfigProps {
  onHolidaysUpdate?: (holidays: any[]) => void
}

export function HolidayApiConfig({ onHolidaysUpdate }: HolidayApiConfigProps) {
  const [selectedApi, setSelectedApi] = useState<keyof typeof HOLIDAY_API_CONFIGS>('wannianli')
  const [apiKey, setApiKey] = useState('')
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle')
  const [connectionMessage, setConnectionMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [lastSync, setLastSync] = useState<string>('')

  useEffect(() => {
    // 加载保存的API配置
    const savedApi = localStorage.getItem('holiday_api_selected')
    const savedKey = localStorage.getItem('holiday_api_key')
    const savedLastSync = localStorage.getItem('holiday_last_sync')

    if (savedApi && savedApi in HOLIDAY_API_CONFIGS) {
      setSelectedApi(savedApi as keyof typeof HOLIDAY_API_CONFIGS)
      HolidayApiService.setSelectedApi(savedApi as keyof typeof HOLIDAY_API_CONFIGS)
    }

    if (savedKey) {
      setApiKey(savedKey)
      HolidayApiService.setApiKey(savedKey)
    }

    if (savedLastSync) {
      setLastSync(savedLastSync)
    }
  }, [])

  const handleApiChange = (api: keyof typeof HOLIDAY_API_CONFIGS) => {
    setSelectedApi(api)
    HolidayApiService.setSelectedApi(api)
    localStorage.setItem('holiday_api_selected', api)
    setConnectionStatus('idle')
  }

  const handleApiKeyChange = (key: string) => {
    setApiKey(key)
    HolidayApiService.setApiKey(key)
    localStorage.setItem('holiday_api_key', key)
    setConnectionStatus('idle')
  }

  const testConnection = async () => {
    setConnectionStatus('testing')
    setConnectionMessage('正在测试API连接...')

    try {
      const result = await HolidayApiService.testApiConnection()
      if (result.success) {
        setConnectionStatus('success')
        setConnectionMessage(result.message)
      } else {
        setConnectionStatus('error')
        setConnectionMessage(result.message)
      }
    } catch (error) {
      setConnectionStatus('error')
      setConnectionMessage(error instanceof Error ? error.message : '连接测试失败')
    }
  }

  const syncHolidays = async () => {
    setIsLoading(true)
    try {
      const currentYear = new Date().getFullYear()
      const nextYear = currentYear + 1

      // 获取今年和明年的节假日
      const [thisYearHolidays, nextYearHolidays] = await Promise.all([
        CachedHolidayService.refreshHolidays(currentYear),
        CachedHolidayService.refreshHolidays(nextYear)
      ])

      const allHolidays = [...thisYearHolidays, ...nextYearHolidays]
      
      // 更新最后同步时间
      const now = new Date().toLocaleString('zh-CN')
      setLastSync(now)
      localStorage.setItem('holiday_last_sync', now)

      // 通知父组件更新节假日
      if (onHolidaysUpdate) {
        onHolidaysUpdate(allHolidays)
      }

      setConnectionStatus('success')
      setConnectionMessage(`成功同步 ${allHolidays.length} 个节假日`)
    } catch (error) {
      setConnectionStatus('error')
      setConnectionMessage(error instanceof Error ? error.message : '同步失败')
    } finally {
      setIsLoading(false)
    }
  }

  const clearCache = () => {
    HolidayCache.clearCache()
    setLastSync('')
    localStorage.removeItem('holiday_last_sync')
    setConnectionMessage('缓存已清除')
  }

  const currentApiConfig = HOLIDAY_API_CONFIGS[selectedApi]

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'testing':
        return <RefreshCw className="h-4 w-4 animate-spin" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'success':
        return 'text-green-600 dark:text-green-400'
      case 'error':
        return 'text-red-600 dark:text-red-400'
      case 'testing':
        return 'text-blue-600 dark:text-blue-400'
      default:
        return 'text-gray-600 dark:text-gray-400'
    }
  }

  return (
    <div className="space-y-6">
      {/* API选择和配置 */}
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            节假日API配置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">选择节假日API</label>
            <Select value={selectedApi} onValueChange={handleApiChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(HOLIDAY_API_CONFIGS).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center space-x-2">
                      <span>{config.name}</span>
                      {!config.requiresKey && (
                        <Badge variant="secondary" className="text-xs">免费</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {currentApiConfig.description}
            </p>
          </div>

          {/* API Key输入 */}
          {currentApiConfig.requiresKey && (
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Key className="h-4 w-4 mr-1" />
                API Key
              </label>
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => handleApiKeyChange(e.target.value)}
                placeholder="请输入API Key"
              />
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <ExternalLink className="h-3 w-3" />
                <a 
                  href={currentApiConfig.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:underline"
                >
                  前往 {currentApiConfig.name} 申请API Key
                </a>
              </div>
            </div>
          )}

          {/* 连接状态 */}
          <div className="flex items-center justify-between p-3 rounded-lg border bg-muted/30">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className={`text-sm font-medium ${getStatusColor()}`}>
                {connectionStatus === 'idle' ? '未测试' :
                 connectionStatus === 'testing' ? '测试中...' :
                 connectionStatus === 'success' ? '连接正常' : '连接失败'}
              </span>
            </div>
            <Button variant="outline" size="sm" onClick={testConnection} disabled={connectionStatus === 'testing'}>
              测试连接
            </Button>
          </div>

          {connectionMessage && (
            <div className={`text-sm ${getStatusColor()}`}>
              {connectionMessage}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 节假日同步 */}
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <RefreshCw className="h-5 w-5 mr-2" />
            节假日数据同步
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">自动获取节假日</p>
              <p className="text-sm text-muted-foreground">
                从API自动获取最新的法定节假日信息
              </p>
            </div>
            <Button 
              onClick={syncHolidays} 
              disabled={isLoading || connectionStatus === 'error'}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  同步中...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  立即同步
                </>
              )}
            </Button>
          </div>

          {lastSync && (
            <div className="text-sm text-muted-foreground">
              最后同步时间: {lastSync}
            </div>
          )}

          <div className="flex items-center justify-between pt-2 border-t">
            <div>
              <p className="text-sm font-medium">清除缓存</p>
              <p className="text-xs text-muted-foreground">
                清除本地缓存的节假日数据
              </p>
            </div>
            <Button variant="outline" size="sm" onClick={clearCache}>
              清除缓存
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* API说明 */}
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            API说明
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div>
              <h4 className="font-medium">推荐API</h4>
              <ul className="mt-1 space-y-1 text-muted-foreground">
                <li>• <strong>万年历API</strong>: 免费，无需注册，数据准确</li>
                <li>• <strong>中国节假日API</strong>: 免费，响应快速</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium">付费API</h4>
              <ul className="mt-1 space-y-1 text-muted-foreground">
                <li>• <strong>天行数据</strong>: 需要注册获取API Key，数据丰富</li>
                <li>• <strong>聚合数据</strong>: 需要注册获取API Key，服务稳定</li>
              </ul>
            </div>
            <div className="p-2 bg-blue-50 dark:bg-blue-950/30 rounded-lg">
              <p className="text-blue-700 dark:text-blue-300 text-xs">
                💡 建议优先使用免费API，如需更稳定的服务可考虑付费API
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
