{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport {\n  Calendar,\n  Users,\n  Scissors,\n  UserCheck,\n  Package,\n  BarChart3,\n  Settings,\n  Home,\n  Navigation as NavigationIcon\n} from 'lucide-react'\n\nconst navigation = [\n  { name: '仪表板', href: '/', icon: Home },\n  { name: '预约管理', href: '/appointments', icon: Calendar },\n  { name: '客户管理', href: '/customers', icon: Users },\n  { name: '员工管理', href: '/staff', icon: UserCheck },\n  { name: '服务管理', href: '/services', icon: Scissors },\n  { name: '库存管理', href: '/inventory', icon: Package },\n  { name: '数据分析', href: '/analytics', icon: BarChart3 },\n  { name: '系统设置', href: '/settings', icon: Settings },\n  { name: '定位演示', href: '/demo-location', icon: NavigationIcon },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"flex flex-col space-y-2\">\n      {navigation.map((item, index) => {\n        const isActive = pathname === item.href\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            className={cn(\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',\n              isActive\n                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'\n                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'\n            )}\n            style={{ animationDelay: `${index * 50}ms` }}\n          >\n            <item.icon className={cn(\n              \"mr-3 h-5 w-5 transition-all duration-200\",\n              isActive\n                ? \"text-primary scale-110\"\n                : \"text-muted-foreground group-hover:text-primary group-hover:scale-105\"\n            )} />\n            <span className=\"font-medium\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary animate-pulse\"></div>\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAiBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IACrC;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,uMAAA,CAAA,QAAK;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAU,MAAM,mNAAA,CAAA,YAAS;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,2MAAA,CAAA,UAAO;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACpD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAkB,MAAM,iNAAA,CAAA,aAAc;IAAC;CAC9D;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,WAAW,aAAa,KAAK,IAAI;YACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gBAEH,MAAM,KAAK,IAAI;gBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gHACA,WACI,qEACA;gBAEN,OAAO;oBAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAAC;;kCAE3C,6LAAC,KAAK,IAAI;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,2BACA;;;;;;kCAEN,6LAAC;wBAAK,WAAU;kCAAe,KAAK,IAAI;;;;;;oBACvC,0BACC,6LAAC;wBAAI,WAAU;;;;;;;eAlBZ,KAAK,IAAI;;;;;QAsBpB;;;;;;AAGN;GAlCgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-elegant hover:shadow-elegant-lg transform hover:scale-105 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-primary text-white hover:opacity-90\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700\",\n        outline:\n          \"border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-white\",\n        secondary:\n          \"bg-gradient-secondary text-secondary-foreground hover:opacity-90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-elegant\",\n        link: \"text-primary underline-offset-4 hover:underline shadow-none\",\n        success: \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\",\n        warning: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600\",\n        info: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-10 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getInitials } from '@/lib/utils'\n\nexport function Sidebar() {\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const userDisplayName = user?.user_metadata?.full_name ||\n                          user?.email?.split('@')[0] ||\n                          'User'\n\n  const userInitials = getInitials(userDisplayName)\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b bg-gradient-primary\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"relative\">\n            <Crown className=\"h-7 w-7 text-white\" />\n            <Scissors className=\"h-4 w-4 text-white absolute -bottom-1 -right-1\" />\n          </div>\n          <div>\n            <span className=\"text-lg font-bold text-white\">皇家理发店</span>\n            <p className=\"text-xs text-white/80\">专业美发沙龙</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 px-4 py-6 bg-gradient-secondary\">\n        <Navigation />\n      </div>\n\n      {/* User info */}\n      <div className=\"border-t bg-card p-4\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant\">\n            <span className=\"text-sm font-bold text-white\">{userInitials}</span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-foreground truncate\">{userDisplayName}</p>\n            <p className=\"text-xs text-muted-foreground truncate\">{user?.email}</p>\n          </div>\n          <div className=\"h-2 w-2 rounded-full bg-green-500\" title=\"在线\"></div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex space-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={() => {/* TODO: Open profile settings */}}\n          >\n            <User className=\"h-3 w-3 mr-1\" />\n            个人资料\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={handleSignOut}\n          >\n            <LogOut className=\"h-3 w-3 mr-1\" />\n            退出登录\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM,eAAe,aACrB,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAC1B;IAExB,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;;8CACC,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAC/C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,6LAAC;wCAAE,WAAU;kDAA0C,MAAM;;;;;;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;gCAAoC,OAAM;;;;;;;;;;;;kCAI3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,KAAwC;;kDAEjD,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAvEgB;;QACY,sIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { Sidebar } from './sidebar'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background overflow-hidden\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto bg-gradient-to-br from-background via-secondary/30 to-muted/50\">\n        <div className=\"p-8 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;KAbgB", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Crown, Scissors } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireAdmin?: boolean\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallback \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"flex flex-col items-center space-y-4\">\n            <div className=\"relative animate-pulse\">\n              <Crown className=\"h-12 w-12 text-primary\" />\n              <Scissors className=\"h-8 w-8 text-accent absolute -bottom-2 -right-2\" />\n            </div>\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-semibold text-foreground mb-2\">Royal Cuts</h2>\n              <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n            </div>\n            <div className=\"flex space-x-1\">\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Show unauthorized if user is not logged in\n  if (!user) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"text-center\">\n            <div className=\"mb-4\">\n              <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              You need to be logged in to access this page.\n            </p>\n            <button\n              onClick={() => router.push('/auth/login')}\n              className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Go to Login\n            </button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Check admin requirement\n  if (requireAdmin) {\n    const isAdmin = user?.user_metadata?.role === 'admin' || \n                    user?.email?.endsWith('@royalcuts.com') ||\n                    user?.app_metadata?.role === 'admin'\n\n    if (!isAdmin) {\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n          <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n            <CardContent className=\"text-center\">\n              <div className=\"mb-4\">\n                <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Admin Access Required</h2>\n              <p className=\"text-muted-foreground mb-4\">\n                You need administrator privileges to access this page.\n              </p>\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Go to Dashboard\n              </button>\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: { requireAdmin?: boolean }\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={options?.requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Hook for checking authentication status\nexport function useRequireAuth(requireAdmin = false) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireAdmin) {\n        const isAdmin = user?.user_metadata?.role === 'admin' || \n                        user?.email?.endsWith('@royalcuts.com') ||\n                        user?.app_metadata?.role === 'admin'\n        \n        if (!isAdmin) {\n          router.push('/')\n          return\n        }\n      }\n    }\n  }, [user, loading, requireAdmin, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,KAAK,EACpB,QAAQ,EACY;;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAChG,6LAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5G;IAEA,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,OAAO,0BACL,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,0BAA0B;IAC1B,IAAI,cAAc;QAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;QAE7C,IAAI,CAAC,SAAS;YACZ,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;QAOX;IACF;IAEA,qBAAO;kBAAG;;AACZ;GAhGgB;;QAKY,sIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KANV;AAmGT,SAAS,SACd,SAAiC,EACjC,OAAoC;IAEpC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,6LAAC;YAAe,cAAc,SAAS;sBACrC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,eAAe,KAAK;;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,cAAc;oBAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;oBAE7C,IAAI,CAAC,SAAS;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;gBACF;YACF;QACF;mCAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,OAAO;QAAE;QAAM;IAAQ;AACzB;IAzBgB;;QACY,sIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/page-header.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: React.ReactNode\n  actions?: React.ReactNode\n  breadcrumbs?: Array<{\n    label: string\n    href?: string\n  }>\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon,\n  actions,\n  breadcrumbs,\n  className\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 mb-8\", className)}>\n      {/* Breadcrumbs */}\n      {breadcrumbs && breadcrumbs.length > 0 && (\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n            {breadcrumbs.map((crumb, index) => (\n              <li key={index} className=\"flex items-center\">\n                {index > 0 && (\n                  <svg\n                    className=\"h-4 w-4 mx-2\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n                {crumb.href ? (\n                  <a\n                    href={crumb.href}\n                    className=\"hover:text-foreground transition-colors\"\n                  >\n                    {crumb.label}\n                  </a>\n                ) : (\n                  <span className=\"text-foreground font-medium\">\n                    {crumb.label}\n                  </span>\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      )}\n\n      {/* Header Content */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-4\">\n          {icon && (\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              {icon}\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-muted-foreground text-lg mt-1\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions && (\n          <div className=\"flex items-center space-x-2\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\ninterface PageHeaderActionsProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function PageHeaderActions({ children, className }: PageHeaderActionsProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2\", className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface QuickStatsProps {\n  stats: Array<{\n    label: string\n    value: string | number\n    icon?: React.ReactNode\n    trend?: {\n      value: number\n      isPositive: boolean\n    }\n  }>\n  className?: string\n}\n\nexport function QuickStats({ stats, className }: QuickStatsProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\", className)}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-card rounded-xl p-4 border shadow-elegant hover:shadow-elegant-lg transition-all duration-300\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">\n                {stat.label}\n              </p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {stat.value}\n              </p>\n              {stat.trend && (\n                <p className={cn(\n                  \"text-xs flex items-center mt-1\",\n                  stat.trend.isPositive ? \"text-green-600\" : \"text-red-600\"\n                )}>\n                  <span className=\"mr-1\">\n                    {stat.trend.isPositive ? \"↗\" : \"↘\"}\n                  </span>\n                  {Math.abs(stat.trend.value)}%\n                </p>\n              )}\n            </div>\n            {stat.icon && (\n              <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n                {stat.icon}\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAeO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACX,SAAS,EACO;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAElC,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;gBAAI,WAAU;gBAAO,cAAW;0BAC/B,cAAA,6LAAC;oBAAG,WAAU;8BACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;4BAAe,WAAU;;gCACvB,QAAQ,mBACP,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAId,MAAM,IAAI,iBACT,6LAAC;oCACC,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;yDAGd,6LAAC;oCAAK,WAAU;8CACb,MAAM,KAAK;;;;;;;2BAvBT;;;;;;;;;;;;;;;0BAiCjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;KA3EgB;AAkFT,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;;;;;;AAGP;MANgB;AAqBT,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;kBAC7E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAEC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,KAAK,kBACT,6LAAC;oCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,kCACA,KAAK,KAAK,CAAC,UAAU,GAAG,mBAAmB;;sDAE3C,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM;;;;;;wCAEhC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK;wCAAE;;;;;;;;;;;;;wBAIjC,KAAK,IAAI,kBACR,6LAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI;;;;;;;;;;;;eAzBX;;;;;;;;;;AAiCf;MAtCgB", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA,SAAS,6CACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/geolocation.ts"], "sourcesContent": ["// 地理定位工具\nexport interface LocationCoordinates {\n  latitude: number\n  longitude: number\n  accuracy: number\n}\n\nexport interface AddressInfo {\n  formatted_address: string\n  country: string\n  province: string\n  city: string\n  district: string\n  street: string\n  street_number: string\n}\n\nexport interface GeolocationResult {\n  coordinates: LocationCoordinates\n  address: AddressInfo\n}\n\n// 地理定位错误类型\nexport enum GeolocationError {\n  PERMISSION_DENIED = 'PERMISSION_DENIED',\n  POSITION_UNAVAILABLE = 'POSITION_UNAVAILABLE',\n  TIMEOUT = 'TIMEOUT',\n  NOT_SUPPORTED = 'NOT_SUPPORTED',\n  REVERSE_GEOCODING_FAILED = 'REVERSE_GEOCODING_FAILED'\n}\n\nexport class GeolocationService {\n  private static readonly TIMEOUT = 10000 // 10秒超时\n  private static readonly MAX_AGE = 300000 // 5分钟缓存\n\n  /**\n   * 检查浏览器是否支持地理定位\n   */\n  static isSupported(): boolean {\n    return 'geolocation' in navigator\n  }\n\n  /**\n   * 获取当前位置坐标\n   */\n  static async getCurrentPosition(): Promise<LocationCoordinates> {\n    if (!this.isSupported()) {\n      throw new Error(GeolocationError.NOT_SUPPORTED)\n    }\n\n    return new Promise((resolve, reject) => {\n      const options: PositionOptions = {\n        enableHighAccuracy: true,\n        timeout: this.TIMEOUT,\n        maximumAge: this.MAX_AGE\n      }\n\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          resolve({\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            accuracy: position.coords.accuracy\n          })\n        },\n        (error) => {\n          switch (error.code) {\n            case error.PERMISSION_DENIED:\n              reject(new Error(GeolocationError.PERMISSION_DENIED))\n              break\n            case error.POSITION_UNAVAILABLE:\n              reject(new Error(GeolocationError.POSITION_UNAVAILABLE))\n              break\n            case error.TIMEOUT:\n              reject(new Error(GeolocationError.TIMEOUT))\n              break\n            default:\n              reject(new Error(GeolocationError.POSITION_UNAVAILABLE))\n              break\n          }\n        },\n        options\n      )\n    })\n  }\n\n  /**\n   * 使用高德地图API进行逆地理编码（坐标转地址）\n   * 注意：在生产环境中需要申请高德地图API密钥\n   */\n  static async reverseGeocode(coordinates: LocationCoordinates): Promise<AddressInfo> {\n    try {\n      // 这里使用高德地图的逆地理编码API\n      // 在实际使用中，需要替换为真实的API密钥\n      const apiKey = 'your_amap_api_key' // 需要替换为实际的API密钥\n      const url = `https://restapi.amap.com/v3/geocode/regeo?key=${apiKey}&location=${coordinates.longitude},${coordinates.latitude}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`\n\n      // 由于跨域限制，这里提供一个模拟的逆地理编码结果\n      // 在实际项目中，应该通过后端API来调用地图服务\n      return this.mockReverseGeocode(coordinates)\n    } catch (error) {\n      console.error('逆地理编码失败:', error)\n      throw new Error(GeolocationError.REVERSE_GEOCODING_FAILED)\n    }\n  }\n\n  /**\n   * 模拟逆地理编码（用于演示）\n   * 在实际项目中应该调用真实的地图API\n   */\n  private static mockReverseGeocode(coordinates: LocationCoordinates): AddressInfo {\n    // 根据坐标范围模拟不同的地址\n    const { latitude, longitude } = coordinates\n\n    // 北京地区\n    if (latitude >= 39.4 && latitude <= 41.0 && longitude >= 115.7 && longitude <= 117.4) {\n      return {\n        formatted_address: '北京市朝阳区三里屯街道工人体育场北路8号',\n        country: '中国',\n        province: '北京市',\n        city: '北京市',\n        district: '朝阳区',\n        street: '工人体育场北路',\n        street_number: '8号'\n      }\n    }\n    \n    // 上海地区\n    if (latitude >= 30.7 && latitude <= 31.9 && longitude >= 120.8 && longitude <= 122.2) {\n      return {\n        formatted_address: '上海市黄浦区南京东路399号',\n        country: '中国',\n        province: '上海市',\n        city: '上海市',\n        district: '黄浦区',\n        street: '南京东路',\n        street_number: '399号'\n      }\n    }\n\n    // 广州地区\n    if (latitude >= 22.7 && latitude <= 23.9 && longitude >= 112.9 && longitude <= 114.0) {\n      return {\n        formatted_address: '广东省广州市天河区天河路208号',\n        country: '中国',\n        province: '广东省',\n        city: '广州市',\n        district: '天河区',\n        street: '天河路',\n        street_number: '208号'\n      }\n    }\n\n    // 深圳地区\n    if (latitude >= 22.4 && latitude <= 22.8 && longitude >= 113.7 && longitude <= 114.6) {\n      return {\n        formatted_address: '广东省深圳市南山区深南大道9988号',\n        country: '中国',\n        province: '广东省',\n        city: '深圳市',\n        district: '南山区',\n        street: '深南大道',\n        street_number: '9988号'\n      }\n    }\n\n    // 默认地址\n    return {\n      formatted_address: `中国某地区 (${latitude.toFixed(4)}, ${longitude.toFixed(4)})`,\n      country: '中国',\n      province: '未知省份',\n      city: '未知城市',\n      district: '未知区域',\n      street: '未知街道',\n      street_number: ''\n    }\n  }\n\n  /**\n   * 获取当前位置并转换为地址\n   */\n  static async getCurrentLocationAddress(): Promise<GeolocationResult> {\n    try {\n      const coordinates = await this.getCurrentPosition()\n      const address = await this.reverseGeocode(coordinates)\n      \n      return {\n        coordinates,\n        address\n      }\n    } catch (error) {\n      throw error\n    }\n  }\n\n  /**\n   * 获取错误信息的中文描述\n   */\n  static getErrorMessage(error: string): string {\n    switch (error) {\n      case GeolocationError.PERMISSION_DENIED:\n        return '用户拒绝了定位请求。请在浏览器设置中允许定位权限。'\n      case GeolocationError.POSITION_UNAVAILABLE:\n        return '无法获取位置信息。请检查GPS是否开启或网络连接。'\n      case GeolocationError.TIMEOUT:\n        return '定位请求超时。请重试或检查网络连接。'\n      case GeolocationError.NOT_SUPPORTED:\n        return '您的浏览器不支持地理定位功能。'\n      case GeolocationError.REVERSE_GEOCODING_FAILED:\n        return '地址解析失败。请手动输入地址或重试。'\n      default:\n        return '定位失败，请重试或手动输入地址。'\n    }\n  }\n}\n\n// 地址格式化工具\nexport class AddressFormatter {\n  /**\n   * 格式化完整地址\n   */\n  static formatFullAddress(address: AddressInfo): string {\n    return address.formatted_address\n  }\n\n  /**\n   * 格式化简短地址\n   */\n  static formatShortAddress(address: AddressInfo): string {\n    const parts = []\n    if (address.city && address.city !== address.province) {\n      parts.push(address.city)\n    }\n    if (address.district) {\n      parts.push(address.district)\n    }\n    if (address.street) {\n      parts.push(address.street)\n    }\n    if (address.street_number) {\n      parts.push(address.street_number)\n    }\n    return parts.join('')\n  }\n\n  /**\n   * 验证地址格式\n   */\n  static validateAddress(address: string): boolean {\n    // 基本的地址验证\n    if (!address || address.trim().length < 5) {\n      return false\n    }\n    \n    // 检查是否包含中文字符（适用于中国地址）\n    const chineseRegex = /[\\u4e00-\\u9fa5]/\n    return chineseRegex.test(address)\n  }\n}\n\n// 坐标工具\nexport class CoordinateUtils {\n  /**\n   * 计算两点之间的距离（米）\n   */\n  static calculateDistance(\n    coord1: LocationCoordinates,\n    coord2: LocationCoordinates\n  ): number {\n    const R = 6371e3 // 地球半径（米）\n    const φ1 = (coord1.latitude * Math.PI) / 180\n    const φ2 = (coord2.latitude * Math.PI) / 180\n    const Δφ = ((coord2.latitude - coord1.latitude) * Math.PI) / 180\n    const Δλ = ((coord2.longitude - coord1.longitude) * Math.PI) / 180\n\n    const a =\n      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +\n      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))\n\n    return R * c\n  }\n\n  /**\n   * 格式化坐标显示\n   */\n  static formatCoordinates(coordinates: LocationCoordinates): string {\n    return `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`\n  }\n}\n"], "names": [], "mappings": "AAAA,SAAS;;;;;;;AAuBF,IAAA,AAAK,0CAAA;;;;;;WAAA;;AAQL,MAAM;IACX,OAAwB,UAAU,MAAM,QAAQ;KAAT;IACvC,OAAwB,UAAU,OAAO,QAAQ;KAAT;IAExC;;GAEC,GACD,OAAO,cAAuB;QAC5B,OAAO,iBAAiB;IAC1B;IAEA;;GAEC,GACD,aAAa,qBAAmD;QAC9D,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;YACvB,MAAM,IAAI;QACZ;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,UAA2B;gBAC/B,oBAAoB;gBACpB,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY,IAAI,CAAC,OAAO;YAC1B;YAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;gBACC,QAAQ;oBACN,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;oBACpC,UAAU,SAAS,MAAM,CAAC,QAAQ;gBACpC;YACF,GACA,CAAC;gBACC,OAAQ,MAAM,IAAI;oBAChB,KAAK,MAAM,iBAAiB;wBAC1B,OAAO,IAAI;wBACX;oBACF,KAAK,MAAM,oBAAoB;wBAC7B,OAAO,IAAI;wBACX;oBACF,KAAK,MAAM,OAAO;wBAChB,OAAO,IAAI;wBACX;oBACF;wBACE,OAAO,IAAI;wBACX;gBACJ;YACF,GACA;QAEJ;IACF;IAEA;;;GAGC,GACD,aAAa,eAAe,WAAgC,EAAwB;QAClF,IAAI;YACF,oBAAoB;YACpB,uBAAuB;YACvB,MAAM,SAAS,oBAAoB,gBAAgB;;YACnD,MAAM,MAAM,CAAC,8CAA8C,EAAE,OAAO,UAAU,EAAE,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,CAAC,6DAA6D,CAAC;YAE5L,0BAA0B;YAC1B,0BAA0B;YAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,MAAM,IAAI;QACZ;IACF;IAEA;;;GAGC,GACD,OAAe,mBAAmB,WAAgC,EAAe;QAC/E,gBAAgB;QAChB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;QAEhC,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,OAAO;YACL,mBAAmB,CAAC,OAAO,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5E,SAAS;YACT,UAAU;YACV,MAAM;YACN,UAAU;YACV,QAAQ;YACR,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,aAAa,4BAAwD;QACnE,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,kBAAkB;YACjD,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CAAC;YAE1C,OAAO;gBACL;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAa,EAAU;QAC5C,OAAQ;YACN;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;QACX;IACF;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,kBAAkB,OAAoB,EAAU;QACrD,OAAO,QAAQ,iBAAiB;IAClC;IAEA;;GAEC,GACD,OAAO,mBAAmB,OAAoB,EAAU;QACtD,MAAM,QAAQ,EAAE;QAChB,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,QAAQ,QAAQ,EAAE;YACrD,MAAM,IAAI,CAAC,QAAQ,IAAI;QACzB;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,IAAI,CAAC,QAAQ,QAAQ;QAC7B;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,IAAI,CAAC,QAAQ,MAAM;QAC3B;QACA,IAAI,QAAQ,aAAa,EAAE;YACzB,MAAM,IAAI,CAAC,QAAQ,aAAa;QAClC;QACA,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAW;QAC/C,UAAU;QACV,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,GAAG,GAAG;YACzC,OAAO;QACT;QAEA,sBAAsB;QACtB,MAAM,eAAe;QACrB,OAAO,aAAa,IAAI,CAAC;IAC3B;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,kBACL,MAA2B,EAC3B,MAA2B,EACnB;QACR,MAAM,IAAI,OAAO,UAAU;;QAC3B,MAAM,KAAK,AAAC,OAAO,QAAQ,GAAG,KAAK,EAAE,GAAI;QACzC,MAAM,KAAK,AAAC,OAAO,QAAQ,GAAG,KAAK,EAAE,GAAI;QACzC,MAAM,KAAK,AAAC,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAI;QAC7D,MAAM,KAAK,AAAC,CAAC,OAAO,SAAS,GAAG,OAAO,SAAS,IAAI,KAAK,EAAE,GAAI;QAE/D,MAAM,IACJ,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,KAAK,KACjC,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,KAAK;QACjE,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;QAErD,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,OAAO,kBAAkB,WAAgC,EAAU;QACjE,OAAO,GAAG,YAAY,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,SAAS,CAAC,OAAO,CAAC,IAAI;IAClF;AACF", "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/address-input.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MapPin, Loader2, AlertCircle, CheckCircle } from 'lucide-react'\nimport { Button } from './button'\nimport { Input } from './input'\nimport { \n  GeolocationService, \n  GeolocationError,\n  type GeolocationResult \n} from '@/lib/geolocation'\n\ninterface AddressInputProps {\n  value: string\n  onChange: (value: string) => void\n  placeholder?: string\n  disabled?: boolean\n  className?: string\n}\n\nexport function AddressInput({\n  value,\n  onChange,\n  placeholder = \"请输入地址\",\n  disabled = false,\n  className = \"\"\n}: AddressInputProps) {\n  const [isLocating, setIsLocating] = useState(false)\n  const [locationStatus, setLocationStatus] = useState<'idle' | 'success' | 'error'>('idle')\n  const [locationError, setLocationError] = useState<string>('')\n\n  const handleGetLocation = async () => {\n    if (!GeolocationService.isSupported()) {\n      setLocationError('您的浏览器不支持地理定位功能')\n      setLocationStatus('error')\n      return\n    }\n\n    setIsLocating(true)\n    setLocationStatus('idle')\n    setLocationError('')\n\n    try {\n      const result: GeolocationResult = await GeolocationService.getCurrentLocationAddress()\n      \n      // 使用获取到的地址\n      onChange(result.address.formatted_address)\n      setLocationStatus('success')\n      \n      // 3秒后清除成功状态\n      setTimeout(() => {\n        setLocationStatus('idle')\n      }, 3000)\n      \n    } catch (error) {\n      const errorMessage = GeolocationService.getErrorMessage((error as Error).message)\n      setLocationError(errorMessage)\n      setLocationStatus('error')\n      \n      // 5秒后清除错误状态\n      setTimeout(() => {\n        setLocationStatus('idle')\n        setLocationError('')\n      }, 5000)\n    } finally {\n      setIsLocating(false)\n    }\n  }\n\n  const getLocationButtonVariant = () => {\n    switch (locationStatus) {\n      case 'success':\n        return 'default'\n      case 'error':\n        return 'destructive'\n      default:\n        return 'outline'\n    }\n  }\n\n  const getLocationButtonIcon = () => {\n    if (isLocating) {\n      return <Loader2 className=\"h-4 w-4 animate-spin\" />\n    }\n    \n    switch (locationStatus) {\n      case 'success':\n        return <CheckCircle className=\"h-4 w-4\" />\n      case 'error':\n        return <AlertCircle className=\"h-4 w-4\" />\n      default:\n        return <MapPin className=\"h-4 w-4\" />\n    }\n  }\n\n  const getLocationButtonText = () => {\n    if (isLocating) {\n      return '定位中...'\n    }\n    \n    switch (locationStatus) {\n      case 'success':\n        return '定位成功'\n      case 'error':\n        return '定位失败'\n      default:\n        return '获取位置'\n    }\n  }\n\n  return (\n    <div className=\"space-y-2\">\n      <div className=\"flex space-x-2\">\n        <Input\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          placeholder={placeholder}\n          disabled={disabled}\n          className={className}\n        />\n        <Button\n          type=\"button\"\n          variant={getLocationButtonVariant()}\n          size=\"default\"\n          onClick={handleGetLocation}\n          disabled={disabled || isLocating}\n          className=\"flex-shrink-0 min-w-[100px]\"\n        >\n          {getLocationButtonIcon()}\n          <span className=\"ml-2\">{getLocationButtonText()}</span>\n        </Button>\n      </div>\n      \n      {/* 错误信息显示 */}\n      {locationStatus === 'error' && locationError && (\n        <div className=\"flex items-start space-x-2 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n          <AlertCircle className=\"h-4 w-4 text-red-500 mt-0.5 flex-shrink-0\" />\n          <div>\n            <p className=\"text-sm text-red-700 dark:text-red-300 font-medium\">\n              定位失败\n            </p>\n            <p className=\"text-sm text-red-600 dark:text-red-400\">\n              {locationError}\n            </p>\n          </div>\n        </div>\n      )}\n      \n      {/* 成功信息显示 */}\n      {locationStatus === 'success' && (\n        <div className=\"flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg\">\n          <CheckCircle className=\"h-4 w-4 text-green-500\" />\n          <p className=\"text-sm text-green-700 dark:text-green-300\">\n            已成功获取当前位置地址\n          </p>\n        </div>\n      )}\n      \n      {/* 定位提示信息 */}\n      {locationStatus === 'idle' && (\n        <div className=\"text-xs text-muted-foreground\">\n          点击\"获取位置\"按钮可自动获取当前位置作为商户地址\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAoBO,SAAS,aAAa,EAC3B,KAAK,EACL,QAAQ,EACR,cAAc,OAAO,EACrB,WAAW,KAAK,EAChB,YAAY,EAAE,EACI;;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACnF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,oBAAoB;QACxB,IAAI,CAAC,4HAAA,CAAA,qBAAkB,CAAC,WAAW,IAAI;YACrC,iBAAiB;YACjB,kBAAkB;YAClB;QACF;QAEA,cAAc;QACd,kBAAkB;QAClB,iBAAiB;QAEjB,IAAI;YACF,MAAM,SAA4B,MAAM,4HAAA,CAAA,qBAAkB,CAAC,yBAAyB;YAEpF,WAAW;YACX,SAAS,OAAO,OAAO,CAAC,iBAAiB;YACzC,kBAAkB;YAElB,YAAY;YACZ,WAAW;gBACT,kBAAkB;YACpB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,4HAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC,AAAC,MAAgB,OAAO;YAChF,iBAAiB;YACjB,kBAAkB;YAElB,YAAY;YACZ,WAAW;gBACT,kBAAkB;gBAClB,iBAAiB;YACnB,GAAG;QACL,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,2BAA2B;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,YAAY;YACd,qBAAO,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,YAAY;YACd,OAAO;QACT;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBACJ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,aAAa;wBACb,UAAU;wBACV,WAAW;;;;;;kCAEb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,MAAK;wBACL,SAAS;wBACT,UAAU,YAAY;wBACtB,WAAU;;4BAET;0CACD,6LAAC;gCAAK,WAAU;0CAAQ;;;;;;;;;;;;;;;;;;YAK3B,mBAAmB,WAAW,+BAC7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAqD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;YAOR,mBAAmB,2BAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAA6C;;;;;;;;;;;;YAO7D,mBAAmB,wBAClB,6LAAC;gBAAI,WAAU;0BAAgC;;;;;;;;;;;;AAMvD;GAlJgB;KAAA", "debugId": null}}, {"offset": {"line": 1810, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/demo-location/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { PageHeader } from '@/components/layout/page-header'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AddressInput } from '@/components/ui/address-input'\nimport { Button } from '@/components/ui/button'\nimport { MapPin, Navigation, Info } from 'lucide-react'\nimport { GeolocationService, type GeolocationResult } from '@/lib/geolocation'\n\nexport default function DemoLocationPage() {\n  const [address, setAddress] = useState('')\n  const [locationResult, setLocationResult] = useState<GeolocationResult | null>(null)\n  const [isTestingLocation, setIsTestingLocation] = useState(false)\n\n  const handleTestLocation = async () => {\n    setIsTestingLocation(true)\n    try {\n      const result = await GeolocationService.getCurrentLocationAddress()\n      setLocationResult(result)\n      setAddress(result.address.formatted_address)\n    } catch (error) {\n      console.error('定位测试失败:', error)\n    } finally {\n      setIsTestingLocation(false)\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n        <PageHeader\n          title=\"地理定位演示\"\n          description=\"测试商户地址定位功能\"\n          icon={<Navigation className=\"h-6 w-6 text-white\" />}\n        />\n\n        <div className=\"space-y-6\">\n          {/* 功能介绍 */}\n          <Card className=\"shadow-elegant\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Info className=\"h-5 w-5 mr-2\" />\n                功能说明\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3 text-sm\">\n                <p>\n                  <strong>地理定位功能</strong>可以帮助您快速获取当前位置作为商户地址，无需手动输入。\n                </p>\n                <div className=\"bg-blue-50 dark:bg-blue-950/30 p-3 rounded-lg\">\n                  <p className=\"font-medium text-blue-700 dark:text-blue-300 mb-2\">使用步骤：</p>\n                  <ol className=\"list-decimal list-inside space-y-1 text-blue-600 dark:text-blue-400\">\n                    <li>点击地址输入框旁边的\"获取位置\"按钮</li>\n                    <li>浏览器会请求定位权限，请点击\"允许\"</li>\n                    <li>系统会自动获取您的当前位置并转换为地址</li>\n                    <li>获取到的地址会自动填入输入框</li>\n                  </ol>\n                </div>\n                <div className=\"bg-amber-50 dark:bg-amber-950/30 p-3 rounded-lg\">\n                  <p className=\"font-medium text-amber-700 dark:text-amber-300 mb-2\">注意事项：</p>\n                  <ul className=\"list-disc list-inside space-y-1 text-amber-600 dark:text-amber-400\">\n                    <li>首次使用需要授权浏览器定位权限</li>\n                    <li>定位精度取决于设备GPS和网络状况</li>\n                    <li>在室内或信号较弱的地方可能影响定位精度</li>\n                    <li>如果定位失败，可以手动输入地址</li>\n                  </ul>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 地址输入演示 */}\n          <Card className=\"shadow-elegant\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <MapPin className=\"h-5 w-5 mr-2\" />\n                地址输入演示\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">商户地址</label>\n                <AddressInput\n                  value={address}\n                  onChange={setAddress}\n                  placeholder=\"请输入地址或点击获取位置\"\n                />\n              </div>\n\n              <div className=\"flex space-x-2\">\n                <Button\n                  onClick={handleTestLocation}\n                  disabled={isTestingLocation}\n                  variant=\"outline\"\n                >\n                  {isTestingLocation ? (\n                    <>\n                      <div className=\"h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\" />\n                      测试定位中...\n                    </>\n                  ) : (\n                    <>\n                      <Navigation className=\"h-4 w-4 mr-2\" />\n                      测试定位功能\n                    </>\n                  )}\n                </Button>\n                <Button\n                  onClick={() => {\n                    setAddress('')\n                    setLocationResult(null)\n                  }}\n                  variant=\"outline\"\n                >\n                  清空地址\n                </Button>\n              </div>\n\n              {/* 当前地址显示 */}\n              {address && (\n                <div className=\"p-3 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg\">\n                  <p className=\"text-sm font-medium text-green-700 dark:text-green-300 mb-1\">\n                    当前地址：\n                  </p>\n                  <p className=\"text-sm text-green-600 dark:text-green-400\">\n                    {address}\n                  </p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* 定位结果详情 */}\n          {locationResult && (\n            <Card className=\"shadow-elegant\">\n              <CardHeader>\n                <CardTitle>定位结果详情</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-3\">\n                    <h4 className=\"font-medium\">坐标信息</h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">纬度：</span>\n                        <span>{locationResult.coordinates.latitude.toFixed(6)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">经度：</span>\n                        <span>{locationResult.coordinates.longitude.toFixed(6)}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">精度：</span>\n                        <span>{Math.round(locationResult.coordinates.accuracy)}米</span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-3\">\n                    <h4 className=\"font-medium\">地址信息</h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">国家：</span>\n                        <span>{locationResult.address.country}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">省份：</span>\n                        <span>{locationResult.address.province}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">城市：</span>\n                        <span>{locationResult.address.city}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">区域：</span>\n                        <span>{locationResult.address.district}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-muted-foreground\">街道：</span>\n                        <span>{locationResult.address.street}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* 浏览器支持检查 */}\n          <Card className=\"shadow-elegant\">\n            <CardHeader>\n              <CardTitle>浏览器兼容性</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between p-3 border rounded-lg\">\n                  <span>地理定位API支持</span>\n                  <span className={`px-2 py-1 rounded text-xs font-medium ${\n                    GeolocationService.isSupported() \n                      ? 'bg-green-100 text-green-700 dark:bg-green-950/30 dark:text-green-300'\n                      : 'bg-red-100 text-red-700 dark:bg-red-950/30 dark:text-red-300'\n                  }`}>\n                    {GeolocationService.isSupported() ? '支持' : '不支持'}\n                  </span>\n                </div>\n                <div className=\"text-xs text-muted-foreground\">\n                  现代浏览器（Chrome、Firefox、Safari、Edge）都支持地理定位功能\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </MainLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,4HAAA,CAAA,qBAAkB,CAAC,yBAAyB;YACjE,kBAAkB;YAClB,WAAW,OAAO,OAAO,CAAC,iBAAiB;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,qBACE,6LAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,iJAAA,CAAA,aAAU;;8BACT,6LAAC,iJAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;oBACZ,oBAAM,6LAAC,iNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;8BAG9B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIrC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;kEAAO;;;;;;oDAAe;;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoD;;;;;;kEACjE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAGR,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAsD;;;;;;kEACnE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIvC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,6LAAC,+IAAA,CAAA,eAAY;oDACX,OAAO;oDACP,UAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,SAAQ;8DAEP,kCACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;4DAAwF;;qFAIzG;;0EACE,6LAAC,iNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;8DAK7C,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;wDACP,WAAW;wDACX,kBAAkB;oDACpB;oDACA,SAAQ;8DACT;;;;;;;;;;;;wCAMF,yBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA8D;;;;;;8DAG3E,6LAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;;;;;;;;;;;;;wBAQV,gCACC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM,eAAe,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM,eAAe,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAEtD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;;4EAAM,KAAK,KAAK,CAAC,eAAe,WAAW,CAAC,QAAQ;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0DAK7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAc;;;;;;kEAC5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM,eAAe,OAAO,CAAC,OAAO;;;;;;;;;;;;0EAEvC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM,eAAe,OAAO,CAAC,QAAQ;;;;;;;;;;;;0EAExC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM,eAAe,OAAO,CAAC,IAAI;;;;;;;;;;;;0EAEpC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM,eAAe,OAAO,CAAC,QAAQ;;;;;;;;;;;;0EAExC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;kFAAM,eAAe,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUlD,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAW,CAAC,sCAAsC,EACtD,4HAAA,CAAA,qBAAkB,CAAC,WAAW,KAC1B,yEACA,gEACJ;kEACC,4HAAA,CAAA,qBAAkB,CAAC,WAAW,KAAK,OAAO;;;;;;;;;;;;0DAG/C,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/D;GA/MwB;KAAA", "debugId": null}}]}