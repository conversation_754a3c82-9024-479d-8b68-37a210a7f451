export interface Customer {
  id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  date_of_birth?: string
  address?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Staff {
  id: string
  first_name: string
  last_name: string
  email: string
  phone: string
  role: 'barber' | 'stylist' | 'manager' | 'admin'
  hire_date: string
  hourly_rate?: number
  is_active: boolean
  avatar_url?: string
  specialties?: string[]
  schedule?: StaffSchedule[]
  created_at: string
  updated_at: string
}

export interface StaffSchedule {
  id: string
  staff_id: string
  day_of_week: number // 0 = Sunday, 1 = Monday, etc.
  start_time: string // HH:MM format
  end_time: string // HH:MM format
  is_available: boolean
}

export interface Service {
  id: string
  name: string
  description?: string
  duration: number // in minutes
  price: number
  category: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Appointment {
  id: string
  customer_id: string
  staff_id: string
  service_id: string
  appointment_date: string
  start_time: string
  end_time: string
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
  notes?: string
  total_amount: number
  created_at: string
  updated_at: string
  
  // Relations
  customer?: Customer
  staff?: Staff
  service?: Service
}

export interface InventoryItem {
  id: string
  name: string
  description?: string
  category: 'shampoo' | 'conditioner' | 'styling' | 'tools' | 'supplies' | 'other'
  brand?: string
  sku?: string
  current_stock: number
  min_stock_level: number
  unit_cost: number
  supplier?: string
  last_restocked?: string
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  appointment_id?: string
  customer_id: string
  staff_id: string
  amount: number
  payment_method: 'cash' | 'card' | 'digital'
  transaction_date: string
  notes?: string
  created_at: string
}

export interface DashboardStats {
  today_appointments: number
  today_revenue: number
  monthly_revenue: number
  total_customers: number
  active_staff: number
  low_stock_items: number
}

export interface AppointmentFormData {
  customer_id: string
  staff_id: string
  service_id: string
  appointment_date: string
  start_time: string
  notes?: string
}

export interface CustomerFormData {
  first_name: string
  last_name: string
  email: string
  phone: string
  date_of_birth?: string
  address?: string
  notes?: string
}

export interface StaffFormData {
  first_name: string
  last_name: string
  email: string
  phone: string
  role: 'barber' | 'stylist' | 'manager' | 'admin'
  hire_date: string
  hourly_rate?: number
  specialties?: string[]
}

export interface ServiceFormData {
  name: string
  description?: string
  duration: number
  price: number
  category: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'
}

export interface InventoryFormData {
  name: string
  description?: string
  category: 'shampoo' | 'conditioner' | 'styling' | 'tools' | 'supplies' | 'other'
  brand?: string
  sku?: string
  current_stock: number
  min_stock_level: number
  unit_cost: number
  supplier?: string
}
