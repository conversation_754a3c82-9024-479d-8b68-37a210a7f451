// 地理定位工具
export interface LocationCoordinates {
  latitude: number
  longitude: number
  accuracy: number
}

export interface AddressInfo {
  formatted_address: string
  country: string
  province: string
  city: string
  district: string
  street: string
  street_number: string
}

export interface GeolocationResult {
  coordinates: LocationCoordinates
  address: AddressInfo
}

// 地理定位错误类型
export enum GeolocationError {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  POSITION_UNAVAILABLE = 'POSITION_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT',
  NOT_SUPPORTED = 'NOT_SUPPORTED',
  REVERSE_GEOCODING_FAILED = 'REVERSE_GEOCODING_FAILED'
}

export class GeolocationService {
  private static readonly TIMEOUT = 10000 // 10秒超时
  private static readonly MAX_AGE = 300000 // 5分钟缓存

  /**
   * 检查浏览器是否支持地理定位
   */
  static isSupported(): boolean {
    return 'geolocation' in navigator
  }

  /**
   * 获取当前位置坐标
   */
  static async getCurrentPosition(): Promise<LocationCoordinates> {
    if (!this.isSupported()) {
      throw new Error(GeolocationError.NOT_SUPPORTED)
    }

    return new Promise((resolve, reject) => {
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: this.TIMEOUT,
        maximumAge: this.MAX_AGE
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy
          })
        },
        (error) => {
          switch (error.code) {
            case error.PERMISSION_DENIED:
              reject(new Error(GeolocationError.PERMISSION_DENIED))
              break
            case error.POSITION_UNAVAILABLE:
              reject(new Error(GeolocationError.POSITION_UNAVAILABLE))
              break
            case error.TIMEOUT:
              reject(new Error(GeolocationError.TIMEOUT))
              break
            default:
              reject(new Error(GeolocationError.POSITION_UNAVAILABLE))
              break
          }
        },
        options
      )
    })
  }

  /**
   * 使用高德地图API进行逆地理编码（坐标转地址）
   * 注意：在生产环境中需要申请高德地图API密钥
   */
  static async reverseGeocode(coordinates: LocationCoordinates): Promise<AddressInfo> {
    try {
      // 这里使用高德地图的逆地理编码API
      // 在实际使用中，需要替换为真实的API密钥
      const apiKey = 'your_amap_api_key' // 需要替换为实际的API密钥
      const url = `https://restapi.amap.com/v3/geocode/regeo?key=${apiKey}&location=${coordinates.longitude},${coordinates.latitude}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`

      // 由于跨域限制，这里提供一个模拟的逆地理编码结果
      // 在实际项目中，应该通过后端API来调用地图服务
      return this.mockReverseGeocode(coordinates)
    } catch (error) {
      console.error('逆地理编码失败:', error)
      throw new Error(GeolocationError.REVERSE_GEOCODING_FAILED)
    }
  }

  /**
   * 模拟逆地理编码（用于演示）
   * 在实际项目中应该调用真实的地图API
   */
  private static mockReverseGeocode(coordinates: LocationCoordinates): AddressInfo {
    // 根据坐标范围模拟不同的地址
    const { latitude, longitude } = coordinates

    // 北京地区
    if (latitude >= 39.4 && latitude <= 41.0 && longitude >= 115.7 && longitude <= 117.4) {
      return {
        formatted_address: '北京市朝阳区三里屯街道工人体育场北路8号',
        country: '中国',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        street: '工人体育场北路',
        street_number: '8号'
      }
    }
    
    // 上海地区
    if (latitude >= 30.7 && latitude <= 31.9 && longitude >= 120.8 && longitude <= 122.2) {
      return {
        formatted_address: '上海市黄浦区南京东路399号',
        country: '中国',
        province: '上海市',
        city: '上海市',
        district: '黄浦区',
        street: '南京东路',
        street_number: '399号'
      }
    }

    // 广州地区
    if (latitude >= 22.7 && latitude <= 23.9 && longitude >= 112.9 && longitude <= 114.0) {
      return {
        formatted_address: '广东省广州市天河区天河路208号',
        country: '中国',
        province: '广东省',
        city: '广州市',
        district: '天河区',
        street: '天河路',
        street_number: '208号'
      }
    }

    // 深圳地区
    if (latitude >= 22.4 && latitude <= 22.8 && longitude >= 113.7 && longitude <= 114.6) {
      return {
        formatted_address: '广东省深圳市南山区深南大道9988号',
        country: '中国',
        province: '广东省',
        city: '深圳市',
        district: '南山区',
        street: '深南大道',
        street_number: '9988号'
      }
    }

    // 默认地址
    return {
      formatted_address: `中国某地区 (${latitude.toFixed(4)}, ${longitude.toFixed(4)})`,
      country: '中国',
      province: '未知省份',
      city: '未知城市',
      district: '未知区域',
      street: '未知街道',
      street_number: ''
    }
  }

  /**
   * 获取当前位置并转换为地址
   */
  static async getCurrentLocationAddress(): Promise<GeolocationResult> {
    try {
      const coordinates = await this.getCurrentPosition()
      const address = await this.reverseGeocode(coordinates)
      
      return {
        coordinates,
        address
      }
    } catch (error) {
      throw error
    }
  }

  /**
   * 获取错误信息的中文描述
   */
  static getErrorMessage(error: string): string {
    switch (error) {
      case GeolocationError.PERMISSION_DENIED:
        return '用户拒绝了定位请求。请在浏览器设置中允许定位权限。'
      case GeolocationError.POSITION_UNAVAILABLE:
        return '无法获取位置信息。请检查GPS是否开启或网络连接。'
      case GeolocationError.TIMEOUT:
        return '定位请求超时。请重试或检查网络连接。'
      case GeolocationError.NOT_SUPPORTED:
        return '您的浏览器不支持地理定位功能。'
      case GeolocationError.REVERSE_GEOCODING_FAILED:
        return '地址解析失败。请手动输入地址或重试。'
      default:
        return '定位失败，请重试或手动输入地址。'
    }
  }
}

// 地址格式化工具
export class AddressFormatter {
  /**
   * 格式化完整地址
   */
  static formatFullAddress(address: AddressInfo): string {
    return address.formatted_address
  }

  /**
   * 格式化简短地址
   */
  static formatShortAddress(address: AddressInfo): string {
    const parts = []
    if (address.city && address.city !== address.province) {
      parts.push(address.city)
    }
    if (address.district) {
      parts.push(address.district)
    }
    if (address.street) {
      parts.push(address.street)
    }
    if (address.street_number) {
      parts.push(address.street_number)
    }
    return parts.join('')
  }

  /**
   * 验证地址格式
   */
  static validateAddress(address: string): boolean {
    // 基本的地址验证
    if (!address || address.trim().length < 5) {
      return false
    }
    
    // 检查是否包含中文字符（适用于中国地址）
    const chineseRegex = /[\u4e00-\u9fa5]/
    return chineseRegex.test(address)
  }
}

// 坐标工具
export class CoordinateUtils {
  /**
   * 计算两点之间的距离（米）
   */
  static calculateDistance(
    coord1: LocationCoordinates,
    coord2: LocationCoordinates
  ): number {
    const R = 6371e3 // 地球半径（米）
    const φ1 = (coord1.latitude * Math.PI) / 180
    const φ2 = (coord2.latitude * Math.PI) / 180
    const Δφ = ((coord2.latitude - coord1.latitude) * Math.PI) / 180
    const Δλ = ((coord2.longitude - coord1.longitude) * Math.PI) / 180

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c
  }

  /**
   * 格式化坐标显示
   */
  static formatCoordinates(coordinates: LocationCoordinates): string {
    return `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`
  }
}
