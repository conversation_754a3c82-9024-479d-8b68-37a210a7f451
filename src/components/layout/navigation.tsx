'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  Calendar, 
  Users, 
  Scissors, 
  UserCheck, 
  Package, 
  BarChart3, 
  Settings,
  Home
} from 'lucide-react'

const navigation = [
  { name: '仪表板', href: '/', icon: Home },
  { name: '预约管理', href: '/appointments', icon: Calendar },
  { name: '客户管理', href: '/customers', icon: Users },
  { name: '员工管理', href: '/staff', icon: UserCheck },
  { name: '服务管理', href: '/services', icon: Scissors },
  { name: '库存管理', href: '/inventory', icon: Package },
  { name: '数据分析', href: '/analytics', icon: BarChart3 },
  { name: '系统设置', href: '/settings', icon: Settings },
]

export function Navigation() {
  const pathname = usePathname()

  return (
    <nav className="flex flex-col space-y-2">
      {navigation.map((item, index) => {
        const isActive = pathname === item.href
        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',
              isActive
                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'
                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'
            )}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <item.icon className={cn(
              "mr-3 h-5 w-5 transition-all duration-200",
              isActive
                ? "text-primary scale-110"
                : "text-muted-foreground group-hover:text-primary group-hover:scale-105"
            )} />
            <span className="font-medium">{item.name}</span>
            {isActive && (
              <div className="ml-auto h-2 w-2 rounded-full bg-primary animate-pulse"></div>
            )}
          </Link>
        )
      })}
    </nav>
  )
}
