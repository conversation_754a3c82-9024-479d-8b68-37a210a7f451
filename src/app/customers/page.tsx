'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader, PageHeaderActions } from '@/components/layout/page-header'
import { CustomerForm } from '@/components/customers/customer-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { EmptyTableState } from '@/components/ui/empty-state'
import { Loading } from '@/components/ui/loading'
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  Phone,
  Mail,
  Calendar,
  Edit,
  Trash2
} from 'lucide-react'
import { Customer } from '@/types'
import { db } from '@/lib/supabase'
import { formatDate } from '@/lib/utils'

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([])
  const [showForm, setShowForm] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | undefined>()

  useEffect(() => {
    loadCustomers()
  }, [])

  useEffect(() => {
    // Filter customers based on search term
    if (searchTerm.trim() === '') {
      setFilteredCustomers(customers)
    } else {
      const filtered = customers.filter(customer =>
        customer.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone.includes(searchTerm)
      )
      setFilteredCustomers(filtered)
    }
  }, [customers, searchTerm])

  const loadCustomers = async () => {
    try {
      setLoading(true)
      const { data, error } = await db.customers.getAll()
      
      if (error) {
        console.error('Error loading customers:', error)
        // For demo purposes, use mock data if Supabase is not configured
        setCustomers(mockCustomers)
      } else {
        setCustomers(data || [])
      }
    } catch (error) {
      console.error('Error loading customers:', error)
      // Use mock data as fallback
      setCustomers(mockCustomers)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteCustomer = async (customerId: string) => {
    if (confirm('Are you sure you want to delete this customer?')) {
      try {
        const { error } = await db.customers.delete(customerId)
        if (error) {
          console.error('Error deleting customer:', error)
        } else {
          setCustomers(customers.filter(c => c.id !== customerId))
        }
      } catch (error) {
        console.error('Error deleting customer:', error)
      }
    }
  }

  const handleAddCustomer = () => {
    setEditingCustomer(undefined)
    setShowForm(true)
  }

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer)
    setShowForm(true)
  }

  const handleFormSuccess = (customer: Customer) => {
    if (editingCustomer) {
      // Update existing customer
      setCustomers(customers.map(c => c.id === customer.id ? customer : c))
    } else {
      // Add new customer
      setCustomers([customer, ...customers])
    }
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="客户管理"
          description="管理客户数据库和联系信息"
          icon={<Users className="h-6 w-6 text-white" />}
          actions={
            <PageHeaderActions>
              <Button onClick={handleAddCustomer}>
                <Plus className="h-4 w-4 mr-2" />
                添加客户
              </Button>
            </PageHeaderActions>
          }
        />

        <div className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-elegant">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="按姓名、邮箱或电话搜索客户..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Customer Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Customers</p>
                    <p className="text-2xl font-bold">{customers.length}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">New This Month</p>
                    <p className="text-2xl font-bold">12</p>
                  </div>
                  <Calendar className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Customers</p>
                    <p className="text-2xl font-bold">{Math.floor(customers.length * 0.8)}</p>
                  </div>
                  <Badge variant="success" className="h-8 px-3">Active</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Customer List */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle>Customer Directory</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Loading text="Loading customers..." />
              ) : filteredCustomers.length === 0 ? (
                searchTerm ? (
                  <EmptyTableState
                    icon={<Search className="h-8 w-8 text-muted-foreground" />}
                    title="No customers found"
                    description={`No customers match your search for "${searchTerm}"`}
                    action={{
                      label: "Clear search",
                      onClick: () => setSearchTerm('')
                    }}
                  />
                ) : (
                  <EmptyTableState
                    icon={<Users className="h-8 w-8 text-muted-foreground" />}
                    title="No customers yet"
                    description="Start building your customer base by adding your first customer"
                    action={{
                      label: "Add Customer",
                      onClick: handleAddCustomer
                    }}
                  />
                )
              ) : (
                <div className="grid gap-4">
                  {filteredCustomers.map((customer) => (
                    <CustomerCard
                      key={customer.id}
                      customer={customer}
                      onEdit={() => handleEditCustomer(customer)}
                      onDelete={handleDeleteCustomer}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Customer Form Dialog */}
        <CustomerForm
          customer={editingCustomer}
          open={showForm}
          onOpenChange={setShowForm}
          onSuccess={handleFormSuccess}
        />
      </MainLayout>
    </ProtectedRoute>
  )
}

interface CustomerCardProps {
  customer: Customer
  onEdit: () => void
  onDelete: (id: string) => void
}

function CustomerCard({ customer, onEdit, onDelete }: CustomerCardProps) {
  return (
    <div className="p-4 border rounded-lg hover:shadow-elegant transition-all duration-200 bg-card">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center">
            <span className="text-white font-semibold">
              {customer.first_name[0]}{customer.last_name[0]}
            </span>
          </div>
          <div>
            <h3 className="font-semibold text-foreground">
              {customer.first_name} {customer.last_name}
            </h3>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              {customer.email && (
                <div className="flex items-center">
                  <Mail className="h-3 w-3 mr-1" />
                  {customer.email}
                </div>
              )}
              <div className="flex items-center">
                <Phone className="h-3 w-3 mr-1" />
                {customer.phone}
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            Member since {formatDate(customer.created_at)}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onEdit}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(customer.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Mock data for demonstration
const mockCustomers: Customer[] = [
  {
    id: '1',
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone: '******-0101',
    date_of_birth: '1985-03-15',
    address: '123 Main St, Anytown, ST 12345',
    notes: 'Regular customer, prefers short cuts',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    first_name: 'Jane',
    last_name: 'Smith',
    email: '<EMAIL>',
    phone: '******-0102',
    date_of_birth: '1990-07-22',
    address: '456 Oak Ave, Anytown, ST 12345',
    notes: 'Likes modern styling',
    created_at: '2024-02-01T14:30:00Z',
    updated_at: '2024-02-01T14:30:00Z'
  },
  {
    id: '3',
    first_name: 'Michael',
    last_name: 'Johnson',
    email: '<EMAIL>',
    phone: '******-0103',
    date_of_birth: '1988-11-08',
    address: '789 Pine Rd, Anytown, ST 12345',
    notes: 'Beard trimming specialist needed',
    created_at: '2024-02-10T09:15:00Z',
    updated_at: '2024-02-10T09:15:00Z'
  }
]
