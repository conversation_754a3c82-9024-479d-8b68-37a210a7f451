import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Calendar, Users, DollarSign, Scissors, Package, AlertTriangle } from 'lucide-react'

export default function Dashboard() {
  // Mock data - will be replaced with real data from Supabase
  const stats = {
    todayAppointments: 12,
    todayRevenue: 850,
    monthlyRevenue: 15420,
    totalCustomers: 234,
    activeStaff: 4,
    lowStockItems: 3
  }

  return (
    <ProtectedRoute>
      <MainLayout>
      <div className="space-y-6">
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-2">
            <div className="h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant">
              <Calendar className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                仪表板
              </h1>
              <p className="text-muted-foreground text-lg">欢迎使用皇家理发店管理系统</p>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            今天是 {new Date().toLocaleDateString('zh-CN', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 hover:shadow-xl transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-blue-700 dark:text-blue-300">今日预约</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Calendar className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-800 dark:text-blue-200 mb-1">{stats.todayAppointments}</div>
              <p className="text-sm text-blue-600 dark:text-blue-400 flex items-center">
                <span className="text-green-500 mr-1">↗</span>
                比昨天多2个
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-950 dark:to-emerald-900 hover:shadow-xl transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-green-700 dark:text-green-300">今日收入</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <DollarSign className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-800 dark:text-green-200 mb-1">${stats.todayRevenue}</div>
              <p className="text-sm text-green-600 dark:text-green-400 flex items-center">
                <span className="text-green-500 mr-1">↗</span>
                比昨天增长12%
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-950 dark:to-violet-900 hover:shadow-xl transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-purple-700 dark:text-purple-300">月度收入</CardTitle>
              <div className="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <DollarSign className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-800 dark:text-purple-200 mb-1">${stats.monthlyRevenue}</div>
              <p className="text-sm text-purple-600 dark:text-purple-400 flex items-center">
                <span className="text-green-500 mr-1">↗</span>
                比上月增长8%
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-orange-50 to-amber-100 dark:from-orange-950 dark:to-amber-900 hover:shadow-xl transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-orange-700 dark:text-orange-300">客户总数</CardTitle>
              <div className="h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Users className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-orange-800 dark:text-orange-200 mb-1">{stats.totalCustomers}</div>
              <p className="text-sm text-orange-600 dark:text-orange-400 flex items-center">
                <span className="text-green-500 mr-1">↗</span>
                本周新增5位
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-indigo-50 to-blue-100 dark:from-indigo-950 dark:to-blue-900 hover:shadow-xl transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-indigo-700 dark:text-indigo-300">在职员工</CardTitle>
              <div className="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Scissors className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-indigo-800 dark:text-indigo-200 mb-1">{stats.activeStaff}</div>
              <p className="text-sm text-indigo-600 dark:text-indigo-400 flex items-center">
                <span className="text-green-500 mr-1">●</span>
                所有员工在岗
              </p>
            </CardContent>
          </Card>

          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-red-50 to-pink-100 dark:from-red-950 dark:to-pink-900 hover:shadow-xl transition-all duration-300 group">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-red-700 dark:text-red-300">库存不足</CardTitle>
              <div className="h-10 w-10 rounded-full bg-red-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-800 dark:text-red-200 mb-1">{stats.lowStockItems}</div>
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                <span className="text-red-500 mr-1">⚠</span>
                需要补货
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900 dark:to-gray-800 hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-xl bg-gradient-primary flex items-center justify-center">
                  <Package className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold text-foreground">快捷操作</CardTitle>
                  <CardDescription className="text-muted-foreground">常用任务和快捷方式</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group">
                <Calendar className="h-4 w-4 text-primary group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium text-foreground">安排新预约</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group">
                <Users className="h-4 w-4 text-primary group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium text-foreground">添加新客户</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group">
                <Package className="h-4 w-4 text-primary group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium text-foreground">更新库存</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group">
                <Scissors className="h-4 w-4 text-primary group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium text-foreground">查看今日安排</span>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-elegant-lg border-0 bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900 dark:to-gray-800 hover:shadow-xl transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 rounded-xl bg-gradient-to-r from-accent to-primary flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl font-bold text-foreground">最近活动</CardTitle>
                  <CardDescription className="text-muted-foreground">最新更新和变化</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-green-50 dark:bg-green-950/30 border-l-4 border-green-500">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <span className="text-sm text-foreground">张三的预约已完成</span>
                <span className="text-xs text-muted-foreground ml-auto">2分钟前</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/30 border-l-4 border-blue-500">
                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                <span className="text-sm text-foreground">新客户李四已添加</span>
                <span className="text-xs text-muted-foreground ml-auto">5分钟前</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-orange-50 dark:bg-orange-950/30 border-l-4 border-orange-500">
                <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                <span className="text-sm text-foreground">洗发水库存已更新</span>
                <span className="text-xs text-muted-foreground ml-auto">10分钟前</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-purple-50 dark:bg-purple-950/30 border-l-4 border-purple-500">
                <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                <span className="text-sm text-foreground">员工排班已修改</span>
                <span className="text-xs text-muted-foreground ml-auto">15分钟前</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
    </ProtectedRoute>
  )
}
