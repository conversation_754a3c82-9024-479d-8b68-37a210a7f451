{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { \n  Calendar, \n  Users, \n  Scissors, \n  UserCheck, \n  Package, \n  BarChart3, \n  Settings,\n  Home\n} from 'lucide-react'\n\nconst navigation = [\n  { name: '仪表板', href: '/', icon: Home },\n  { name: '预约管理', href: '/appointments', icon: Calendar },\n  { name: '客户管理', href: '/customers', icon: Users },\n  { name: '员工管理', href: '/staff', icon: UserCheck },\n  { name: '服务管理', href: '/services', icon: Scissors },\n  { name: '库存管理', href: '/inventory', icon: Package },\n  { name: '数据分析', href: '/analytics', icon: BarChart3 },\n  { name: '系统设置', href: '/settings', icon: Settings },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"flex flex-col space-y-2\">\n      {navigation.map((item, index) => {\n        const isActive = pathname === item.href\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            className={cn(\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',\n              isActive\n                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'\n                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'\n            )}\n            style={{ animationDelay: `${index * 50}ms` }}\n          >\n            <item.icon className={cn(\n              \"mr-3 h-5 w-5 transition-all duration-200\",\n              isActive\n                ? \"text-primary scale-110\"\n                : \"text-muted-foreground group-hover:text-primary group-hover:scale-105\"\n            )} />\n            <span className=\"font-medium\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary animate-pulse\"></div>\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACrC;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,oMAAA,CAAA,QAAK;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAU,MAAM,gNAAA,CAAA,YAAS;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,wMAAA,CAAA,UAAO;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACpD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACnD;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,WAAW,aAAa,KAAK,IAAI;YACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gBAEH,MAAM,KAAK,IAAI;gBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gHACA,WACI,qEACA;gBAEN,OAAO;oBAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAAC;;kCAE3C,8OAAC,KAAK,IAAI;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,2BACA;;;;;;kCAEN,8OAAC;wBAAK,WAAU;kCAAe,KAAK,IAAI;;;;;;oBACvC,0BACC,8OAAC;wBAAI,WAAU;;;;;;;eAlBZ,KAAK,IAAI;;;;;QAsBpB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-elegant hover:shadow-elegant-lg transform hover:scale-105 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-primary text-white hover:opacity-90\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700\",\n        outline:\n          \"border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-white\",\n        secondary:\n          \"bg-gradient-secondary text-secondary-foreground hover:opacity-90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-elegant\",\n        link: \"text-primary underline-offset-4 hover:underline shadow-none\",\n        success: \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\",\n        warning: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600\",\n        info: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-10 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getInitials } from '@/lib/utils'\n\nexport function Sidebar() {\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const userDisplayName = user?.user_metadata?.full_name ||\n                          user?.email?.split('@')[0] ||\n                          'User'\n\n  const userInitials = getInitials(userDisplayName)\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b bg-gradient-primary\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"relative\">\n            <Crown className=\"h-7 w-7 text-white\" />\n            <Scissors className=\"h-4 w-4 text-white absolute -bottom-1 -right-1\" />\n          </div>\n          <div>\n            <span className=\"text-lg font-bold text-white\">皇家理发店</span>\n            <p className=\"text-xs text-white/80\">专业美发沙龙</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 px-4 py-6 bg-gradient-secondary\">\n        <Navigation />\n      </div>\n\n      {/* User info */}\n      <div className=\"border-t bg-card p-4\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant\">\n            <span className=\"text-sm font-bold text-white\">{userInitials}</span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-foreground truncate\">{userDisplayName}</p>\n            <p className=\"text-xs text-muted-foreground truncate\">{user?.email}</p>\n          </div>\n          <div className=\"h-2 w-2 rounded-full bg-green-500\" title=\"在线\"></div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex space-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={() => {/* TODO: Open profile settings */}}\n          >\n            <User className=\"h-3 w-3 mr-1\" />\n            个人资料\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={handleSignOut}\n          >\n            <LogOut className=\"h-3 w-3 mr-1\" />\n            退出登录\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM,eAAe,aACrB,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAC1B;IAExB,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAA0C,MAAM;;;;;;;;;;;;0CAE/D,8OAAC;gCAAI,WAAU;gCAAoC,OAAM;;;;;;;;;;;;kCAI3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,KAAwC;;kDAEjD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { Sidebar } from './sidebar'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background overflow-hidden\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto bg-gradient-to-br from-background via-secondary/30 to-muted/50\">\n        <div className=\"p-8 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Crown, Scissors } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireAdmin?: boolean\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallback \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"flex flex-col items-center space-y-4\">\n            <div className=\"relative animate-pulse\">\n              <Crown className=\"h-12 w-12 text-primary\" />\n              <Scissors className=\"h-8 w-8 text-accent absolute -bottom-2 -right-2\" />\n            </div>\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-semibold text-foreground mb-2\">Royal Cuts</h2>\n              <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n            </div>\n            <div className=\"flex space-x-1\">\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Show unauthorized if user is not logged in\n  if (!user) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"text-center\">\n            <div className=\"mb-4\">\n              <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              You need to be logged in to access this page.\n            </p>\n            <button\n              onClick={() => router.push('/auth/login')}\n              className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Go to Login\n            </button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Check admin requirement\n  if (requireAdmin) {\n    const isAdmin = user?.user_metadata?.role === 'admin' || \n                    user?.email?.endsWith('@royalcuts.com') ||\n                    user?.app_metadata?.role === 'admin'\n\n    if (!isAdmin) {\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n          <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n            <CardContent className=\"text-center\">\n              <div className=\"mb-4\">\n                <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Admin Access Required</h2>\n              <p className=\"text-muted-foreground mb-4\">\n                You need administrator privileges to access this page.\n              </p>\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Go to Dashboard\n              </button>\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: { requireAdmin?: boolean }\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={options?.requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Hook for checking authentication status\nexport function useRequireAuth(requireAdmin = false) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireAdmin) {\n        const isAdmin = user?.user_metadata?.role === 'admin' || \n                        user?.email?.endsWith('@royalcuts.com') ||\n                        user?.app_metadata?.role === 'admin'\n        \n        if (!isAdmin) {\n          router.push('/')\n          return\n        }\n      }\n    }\n  }, [user, loading, requireAdmin, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,KAAK,EACpB,QAAQ,EACY;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAChG,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5G;IAEA,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,OAAO,0BACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,0BAA0B;IAC1B,IAAI,cAAc;QAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;QAE7C,IAAI,CAAC,SAAS;YACZ,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;QAOX;IACF;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,SACd,SAAiC,EACjC,OAAoC;IAEpC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,8OAAC;YAAe,cAAc,SAAS;sBACrC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,eAAe,KAAK;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,cAAc;gBAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;gBAE7C,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,OAAO;QAAE;QAAM;IAAQ;AACzB", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/page-header.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: React.ReactNode\n  actions?: React.ReactNode\n  breadcrumbs?: Array<{\n    label: string\n    href?: string\n  }>\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon,\n  actions,\n  breadcrumbs,\n  className\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 mb-8\", className)}>\n      {/* Breadcrumbs */}\n      {breadcrumbs && breadcrumbs.length > 0 && (\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n            {breadcrumbs.map((crumb, index) => (\n              <li key={index} className=\"flex items-center\">\n                {index > 0 && (\n                  <svg\n                    className=\"h-4 w-4 mx-2\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n                {crumb.href ? (\n                  <a\n                    href={crumb.href}\n                    className=\"hover:text-foreground transition-colors\"\n                  >\n                    {crumb.label}\n                  </a>\n                ) : (\n                  <span className=\"text-foreground font-medium\">\n                    {crumb.label}\n                  </span>\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      )}\n\n      {/* Header Content */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-4\">\n          {icon && (\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              {icon}\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-muted-foreground text-lg mt-1\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions && (\n          <div className=\"flex items-center space-x-2\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\ninterface PageHeaderActionsProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function PageHeaderActions({ children, className }: PageHeaderActionsProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2\", className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface QuickStatsProps {\n  stats: Array<{\n    label: string\n    value: string | number\n    icon?: React.ReactNode\n    trend?: {\n      value: number\n      isPositive: boolean\n    }\n  }>\n  className?: string\n}\n\nexport function QuickStats({ stats, className }: QuickStatsProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\", className)}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-card rounded-xl p-4 border shadow-elegant hover:shadow-elegant-lg transition-all duration-300\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">\n                {stat.label}\n              </p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {stat.value}\n              </p>\n              {stat.trend && (\n                <p className={cn(\n                  \"text-xs flex items-center mt-1\",\n                  stat.trend.isPositive ? \"text-green-600\" : \"text-red-600\"\n                )}>\n                  <span className=\"mr-1\">\n                    {stat.trend.isPositive ? \"↗\" : \"↘\"}\n                  </span>\n                  {Math.abs(stat.trend.value)}%\n                </p>\n              )}\n            </div>\n            {stat.icon && (\n              <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n                {stat.icon}\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAeO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACX,SAAS,EACO;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAElC,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAU;gBAAO,cAAW;0BAC/B,cAAA,8OAAC;oBAAG,WAAU;8BACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;4BAAe,WAAU;;gCACvB,QAAQ,mBACP,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAId,MAAM,IAAI,iBACT,8OAAC;oCACC,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;yDAGd,8OAAC;oCAAK,WAAU;8CACb,MAAM,KAAK;;;;;;;2BAvBT;;;;;;;;;;;;;;;0BAiCjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,sBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;AAOO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;;;;;;AAGP;AAeO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;kBAC7E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAEC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,KAAK,kBACT,8OAAC;oCAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,kCACA,KAAK,KAAK,CAAC,UAAU,GAAG,mBAAmB;;sDAE3C,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM;;;;;;wCAEhC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK;wCAAE;;;;;;;;;;;;;wBAIjC,KAAK,IAAI,kBACR,8OAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI;;;;;;;;;;;;eAzBX;;;;;;;;;;AAiCf", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA,SAAS,6CACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        purple:\n          \"border-transparent bg-purple-500 text-white hover:bg-purple-600\",\n        pink:\n          \"border-transparent bg-pink-500 text-white hover:bg-pink-600\",\n        indigo:\n          \"border-transparent bg-indigo-500 text-white hover:bg-indigo-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,QACE;YACF,MACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/settings.ts"], "sourcesContent": ["// Settings management for local storage\nexport interface BusinessSettings {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  timezone: string\n  currency: string\n}\n\nexport interface DayHours {\n  open: string\n  close: string\n  closed: boolean\n}\n\nexport interface OperatingHours {\n  [key: string]: DayHours\n}\n\nexport interface Holiday {\n  id: string\n  name: string\n  date: string // YYYY-MM-DD format\n  type: 'fixed' | 'lunar' | 'custom' // 固定日期、农历、自定义\n  recurring: boolean // 是否每年重复\n  closed: boolean // 是否休息\n  hours?: DayHours // 如果不休息，特殊营业时间\n  description?: string\n  source?: 'manual' | 'api' | 'wannianli' | 'china_holiday' | 'tianapi' | 'juhe' // 数据来源\n}\n\nexport interface HolidaySettings {\n  holidays: Holiday[]\n  enableHolidayMode: boolean // 是否启用节假日模式\n}\n\nexport interface NotificationSettings {\n  emailNotifications: boolean\n  smsNotifications: boolean\n  appointmentReminders: boolean\n  lowStockAlerts: boolean\n  dailyReports: boolean\n  weeklyReports: boolean\n}\n\nexport interface AppSettings {\n  business: BusinessSettings\n  operatingHours: OperatingHours\n  holidays: HolidaySettings\n  notifications: NotificationSettings\n  theme: string\n  language: string\n}\n\nconst DEFAULT_SETTINGS: AppSettings = {\n  business: {\n    name: '皇家理发店',\n    address: '北京市朝阳区三里屯街道1号',\n    phone: '+86 138-0013-8000',\n    email: '<EMAIL>',\n    website: 'www.royalcuts.cn',\n    timezone: 'Asia/Shanghai',\n    currency: 'CNY'\n  },\n  operatingHours: {\n    monday: { open: '09:00', close: '18:00', closed: false },\n    tuesday: { open: '09:00', close: '18:00', closed: false },\n    wednesday: { open: '09:00', close: '18:00', closed: false },\n    thursday: { open: '09:00', close: '19:00', closed: false },\n    friday: { open: '09:00', close: '19:00', closed: false },\n    saturday: { open: '08:00', close: '17:00', closed: false },\n    sunday: { open: '10:00', close: '16:00', closed: false }\n  },\n  holidays: {\n    enableHolidayMode: true,\n    holidays: [\n      {\n        id: 'new-year',\n        name: '元旦',\n        date: '2024-01-01',\n        type: 'fixed',\n        recurring: true,\n        closed: true,\n        description: '新年第一天',\n        source: 'manual'\n      },\n      {\n        id: 'spring-festival',\n        name: '春节',\n        date: '2024-02-10',\n        type: 'lunar',\n        recurring: true,\n        closed: true,\n        description: '农历新年',\n        source: 'manual'\n      },\n      {\n        id: 'labor-day',\n        name: '劳动节',\n        date: '2024-05-01',\n        type: 'fixed',\n        recurring: true,\n        closed: true,\n        description: '国际劳动节',\n        source: 'manual'\n      },\n      {\n        id: 'national-day',\n        name: '国庆节',\n        date: '2024-10-01',\n        type: 'fixed',\n        recurring: true,\n        closed: true,\n        description: '中华人民共和国国庆节',\n        source: 'manual'\n      }\n    ]\n  },\n  notifications: {\n    emailNotifications: true,\n    smsNotifications: false,\n    appointmentReminders: true,\n    lowStockAlerts: true,\n    dailyReports: false,\n    weeklyReports: true\n  },\n  theme: 'royal-gold',\n  language: 'zh-CN'\n}\n\nconst SETTINGS_KEY = 'barbershop_settings'\n\nexport class SettingsManager {\n  static getSettings(): AppSettings {\n    if (typeof window === 'undefined') {\n      return DEFAULT_SETTINGS\n    }\n\n    try {\n      const stored = localStorage.getItem(SETTINGS_KEY)\n      if (stored) {\n        const parsed = JSON.parse(stored)\n        // Merge with defaults to ensure all properties exist\n        return {\n          ...DEFAULT_SETTINGS,\n          ...parsed,\n          business: { ...DEFAULT_SETTINGS.business, ...parsed.business },\n          operatingHours: { ...DEFAULT_SETTINGS.operatingHours, ...parsed.operatingHours },\n          holidays: { ...DEFAULT_SETTINGS.holidays, ...parsed.holidays },\n          notifications: { ...DEFAULT_SETTINGS.notifications, ...parsed.notifications }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading settings:', error)\n    }\n\n    return DEFAULT_SETTINGS\n  }\n\n  static saveSettings(settings: AppSettings): void {\n    if (typeof window === 'undefined') {\n      return\n    }\n\n    try {\n      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings))\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      throw new Error('无法保存设置')\n    }\n  }\n\n  static updateBusinessSettings(business: BusinessSettings): void {\n    const settings = this.getSettings()\n    settings.business = business\n    this.saveSettings(settings)\n  }\n\n  static updateOperatingHours(operatingHours: OperatingHours): void {\n    const settings = this.getSettings()\n    settings.operatingHours = operatingHours\n    this.saveSettings(settings)\n  }\n\n  static updateHolidaySettings(holidays: HolidaySettings): void {\n    const settings = this.getSettings()\n    settings.holidays = holidays\n    this.saveSettings(settings)\n  }\n\n  static updateNotificationSettings(notifications: NotificationSettings): void {\n    const settings = this.getSettings()\n    settings.notifications = notifications\n    this.saveSettings(settings)\n  }\n\n  static updateTheme(theme: string): void {\n    const settings = this.getSettings()\n    settings.theme = theme\n    this.saveSettings(settings)\n  }\n\n  static resetToDefaults(): void {\n    this.saveSettings(DEFAULT_SETTINGS)\n  }\n\n  static exportSettings(): string {\n    const settings = this.getSettings()\n    return JSON.stringify(settings, null, 2)\n  }\n\n  static importSettings(settingsJson: string): void {\n    try {\n      const imported = JSON.parse(settingsJson)\n      // Validate the structure\n      if (imported.business && imported.operatingHours && imported.notifications) {\n        // Ensure holidays exist, use default if not\n        if (!imported.holidays) {\n          imported.holidays = DEFAULT_SETTINGS.holidays\n        }\n        this.saveSettings(imported)\n      } else {\n        throw new Error('Invalid settings format')\n      }\n    } catch (error) {\n      console.error('Error importing settings:', error)\n      throw new Error('无效的设置格式')\n    }\n  }\n}\n\n// Utility functions for formatting\nexport function formatOperatingHours(hours: OperatingHours): string {\n  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']\n  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\n  \n  return days.map((day, index) => {\n    const dayHours = hours[day]\n    if (dayHours.closed) {\n      return `${dayNames[index]}: 休息`\n    }\n    return `${dayNames[index]}: ${dayHours.open} - ${dayHours.close}`\n  }).join('\\n')\n}\n\nexport function validateBusinessSettings(business: BusinessSettings): string[] {\n  const errors: string[] = []\n  \n  if (!business.name.trim()) {\n    errors.push('商户名称不能为空')\n  }\n  \n  if (!business.phone.trim()) {\n    errors.push('联系电话不能为空')\n  }\n  \n  if (!business.email.trim()) {\n    errors.push('邮箱地址不能为空')\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(business.email)) {\n    errors.push('邮箱地址格式不正确')\n  }\n  \n  if (!business.address.trim()) {\n    errors.push('商户地址不能为空')\n  }\n  \n  return errors\n}\n\nexport function validateOperatingHours(hours: OperatingHours): string[] {\n  const errors: string[] = []\n  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']\n  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\n  \n  days.forEach((day, index) => {\n    const dayHours = hours[day]\n    if (!dayHours.closed) {\n      if (!dayHours.open || !dayHours.close) {\n        errors.push(`${dayNames[index]}的营业时间不完整`)\n      } else if (dayHours.open >= dayHours.close) {\n        errors.push(`${dayNames[index]}的开始时间必须早于结束时间`)\n      }\n    }\n  })\n  \n  return errors\n}\n\n// 主题配置\nexport interface ThemeConfig {\n  id: string\n  name: string\n  description: string\n  colors: {\n    primary: string\n    primaryForeground: string\n    secondary?: string\n    accent?: string\n    background?: string\n    foreground?: string\n    muted?: string\n    border?: string\n  }\n  preview: {\n    gradient: string\n    textColor: string\n  }\n}\n\nexport const AVAILABLE_THEMES: ThemeConfig[] = [\n  {\n    id: 'royal-gold',\n    name: '皇家金色',\n    description: '经典奢华的金色主题，彰显专业品质',\n    colors: {\n      primary: '45 93% 47%', // 金色\n      primaryForeground: '0 0% 98%',\n      secondary: '45 84% 60%',\n      accent: '45 84% 60%'\n    },\n    preview: {\n      gradient: 'from-amber-600 to-amber-800',\n      textColor: 'text-amber-700'\n    }\n  },\n  {\n    id: 'ocean-blue',\n    name: '海洋蓝',\n    description: '清新的蓝色主题，营造宁静专业氛围',\n    colors: {\n      primary: '217 91% 60%', // 蓝色\n      primaryForeground: '0 0% 98%',\n      secondary: '217 84% 70%',\n      accent: '217 84% 70%'\n    },\n    preview: {\n      gradient: 'from-blue-600 to-blue-800',\n      textColor: 'text-blue-700'\n    }\n  },\n  {\n    id: 'forest-green',\n    name: '森林绿',\n    description: '自然的绿色主题，传达健康活力理念',\n    colors: {\n      primary: '142 76% 36%', // 绿色\n      primaryForeground: '0 0% 98%',\n      secondary: '142 70% 45%',\n      accent: '142 70% 45%'\n    },\n    preview: {\n      gradient: 'from-emerald-600 to-emerald-800',\n      textColor: 'text-emerald-700'\n    }\n  },\n  {\n    id: 'sunset-orange',\n    name: '日落橙',\n    description: '温暖的橙色主题，营造友好亲切氛围',\n    colors: {\n      primary: '24 95% 53%', // 橙色\n      primaryForeground: '0 0% 98%',\n      secondary: '24 90% 60%',\n      accent: '24 90% 60%'\n    },\n    preview: {\n      gradient: 'from-orange-600 to-red-600',\n      textColor: 'text-orange-700'\n    }\n  },\n  {\n    id: 'purple-luxury',\n    name: '紫色奢华',\n    description: '高贵的紫色主题，展现独特品味',\n    colors: {\n      primary: '262 83% 58%', // 紫色\n      primaryForeground: '0 0% 98%',\n      secondary: '262 75% 65%',\n      accent: '262 75% 65%'\n    },\n    preview: {\n      gradient: 'from-purple-600 to-purple-800',\n      textColor: 'text-purple-700'\n    }\n  },\n  {\n    id: 'rose-elegant',\n    name: '玫瑰雅致',\n    description: '优雅的玫瑰色主题，适合高端美容院',\n    colors: {\n      primary: '330 81% 60%', // 玫瑰色\n      primaryForeground: '0 0% 98%',\n      secondary: '330 75% 70%',\n      accent: '330 75% 70%'\n    },\n    preview: {\n      gradient: 'from-rose-600 to-pink-700',\n      textColor: 'text-rose-700'\n    }\n  }\n]\n\n// Theme utilities\nexport function applyTheme(themeId: string): void {\n  if (typeof document === 'undefined') return\n\n  const theme = AVAILABLE_THEMES.find(t => t.id === themeId)\n  if (!theme) return\n\n  const root = document.documentElement\n\n  // 应用主题颜色\n  Object.entries(theme.colors).forEach(([key, value]) => {\n    const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase()\n    root.style.setProperty(`--${cssVar}`, value)\n  })\n}\n\nexport function getThemeConfig(themeId: string): ThemeConfig | undefined {\n  return AVAILABLE_THEMES.find(t => t.id === themeId)\n}\n\n// Initialize theme on load\nexport function initializeTheme(): void {\n  const settings = SettingsManager.getSettings()\n  applyTheme(settings.theme)\n}\n\n// Holiday management utilities\nexport function generateHolidayId(): string {\n  return 'holiday-' + Math.random().toString(36).substr(2, 9)\n}\n\nexport function isHolidayToday(holidays: Holiday[]): Holiday | null {\n  const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD\n  return holidays.find(holiday => {\n    if (!holiday.recurring) {\n      return holiday.date === today\n    }\n\n    // For recurring holidays, check if month and day match\n    const holidayDate = new Date(holiday.date)\n    const todayDate = new Date(today)\n\n    if (holiday.type === 'fixed') {\n      return holidayDate.getMonth() === todayDate.getMonth() &&\n             holidayDate.getDate() === todayDate.getDate()\n    }\n\n    // For lunar holidays, would need lunar calendar conversion\n    // For now, just check exact date\n    return holiday.date === today\n  }) || null\n}\n\nexport function getEffectiveHours(\n  date: string,\n  operatingHours: OperatingHours,\n  holidaySettings: HolidaySettings\n): DayHours | null {\n  if (!holidaySettings.enableHolidayMode) {\n    // If holiday mode is disabled, use regular hours\n    const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' })\n    return operatingHours[dayOfWeek] || null\n  }\n\n  // Check if date is a holiday\n  const holiday = holidaySettings.holidays.find(h => {\n    if (!h.recurring) {\n      return h.date === date\n    }\n\n    const holidayDate = new Date(h.date)\n    const checkDate = new Date(date)\n\n    if (h.type === 'fixed') {\n      return holidayDate.getMonth() === checkDate.getMonth() &&\n             holidayDate.getDate() === checkDate.getDate()\n    }\n\n    return h.date === date\n  })\n\n  if (holiday) {\n    if (holiday.closed) {\n      return { open: '', close: '', closed: true }\n    }\n    if (holiday.hours) {\n      return holiday.hours\n    }\n  }\n\n  // Use regular operating hours\n  const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' })\n  return operatingHours[dayOfWeek] || null\n}\n\nexport function validateHoliday(holiday: Partial<Holiday>): string[] {\n  const errors: string[] = []\n\n  if (!holiday.name?.trim()) {\n    errors.push('节假日名称不能为空')\n  }\n\n  if (!holiday.date) {\n    errors.push('请选择日期')\n  } else {\n    const date = new Date(holiday.date)\n    if (isNaN(date.getTime())) {\n      errors.push('日期格式不正确')\n    }\n  }\n\n  if (!holiday.type) {\n    errors.push('请选择节假日类型')\n  }\n\n  if (!holiday.closed && holiday.hours) {\n    if (!holiday.hours.open || !holiday.hours.close) {\n      errors.push('请设置营业时间')\n    } else if (holiday.hours.open >= holiday.hours.close) {\n      errors.push('开始时间必须早于结束时间')\n    }\n  }\n\n  return errors\n}\n\nexport function formatHolidayDate(date: string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function getHolidayTypeLabel(type: string): string {\n  switch (type) {\n    case 'fixed': return '固定日期'\n    case 'lunar': return '农历日期'\n    case 'custom': return '自定义'\n    default: return type\n  }\n}\n\nexport function getUpcomingHolidays(holidays: Holiday[], days: number = 30): Holiday[] {\n  const today = new Date()\n  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000)\n\n  return holidays.filter(holiday => {\n    const holidayDate = new Date(holiday.date)\n\n    if (!holiday.recurring) {\n      return holidayDate >= today && holidayDate <= futureDate\n    }\n\n    // For recurring holidays, check if they occur within the next 'days' period\n    if (holiday.type === 'fixed') {\n      const thisYear = new Date(today.getFullYear(), holidayDate.getMonth(), holidayDate.getDate())\n      const nextYear = new Date(today.getFullYear() + 1, holidayDate.getMonth(), holidayDate.getDate())\n\n      return (thisYear >= today && thisYear <= futureDate) ||\n             (nextYear >= today && nextYear <= futureDate)\n    }\n\n    return false\n  }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;;;;;;;;AAwDxC,MAAM,mBAAgC;IACpC,UAAU;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA,gBAAgB;QACd,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,SAAS;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACxD,WAAW;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QAC1D,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;IACzD;IACA,UAAU;QACR,mBAAmB;QACnB,UAAU;YACR;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;SACD;IACH;IACA,eAAe;QACb,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,eAAe;IACjB;IACA,OAAO;IACP,UAAU;AACZ;AAEA,MAAM,eAAe;AAEd,MAAM;IACX,OAAO,cAA2B;QAChC,wCAAmC;YACjC,OAAO;QACT;;IAqBF;IAEA,OAAO,aAAa,QAAqB,EAAQ;QAC/C,wCAAmC;YACjC;QACF;;IAQF;IAEA,OAAO,uBAAuB,QAA0B,EAAQ;QAC9D,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,QAAQ,GAAG;QACpB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,qBAAqB,cAA8B,EAAQ;QAChE,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,cAAc,GAAG;QAC1B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,sBAAsB,QAAyB,EAAQ;QAC5D,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,QAAQ,GAAG;QACpB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,2BAA2B,aAAmC,EAAQ;QAC3E,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,aAAa,GAAG;QACzB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,YAAY,KAAa,EAAQ;QACtC,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,KAAK,GAAG;QACjB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,kBAAwB;QAC7B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,iBAAyB;QAC9B,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO,KAAK,SAAS,CAAC,UAAU,MAAM;IACxC;IAEA,OAAO,eAAe,YAAoB,EAAQ;QAChD,IAAI;YACF,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,yBAAyB;YACzB,IAAI,SAAS,QAAQ,IAAI,SAAS,cAAc,IAAI,SAAS,aAAa,EAAE;gBAC1E,4CAA4C;gBAC5C,IAAI,CAAC,SAAS,QAAQ,EAAE;oBACtB,SAAS,QAAQ,GAAG,iBAAiB,QAAQ;gBAC/C;gBACA,IAAI,CAAC,YAAY,CAAC;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,SAAS,qBAAqB,KAAqB;IACxD,MAAM,OAAO;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAC3F,MAAM,WAAW;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE3D,OAAO,KAAK,GAAG,CAAC,CAAC,KAAK;QACpB,MAAM,WAAW,KAAK,CAAC,IAAI;QAC3B,IAAI,SAAS,MAAM,EAAE;YACnB,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;QACjC;QACA,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,SAAS,KAAK,EAAE;IACnE,GAAG,IAAI,CAAC;AACV;AAEO,SAAS,yBAAyB,QAA0B;IACjE,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;QAC1B,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;QAC7D,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAEO,SAAS,uBAAuB,KAAqB;IAC1D,MAAM,SAAmB,EAAE;IAC3B,MAAM,OAAO;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAC3F,MAAM,WAAW;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE3D,KAAK,OAAO,CAAC,CAAC,KAAK;QACjB,MAAM,WAAW,KAAK,CAAC,IAAI;QAC3B,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;gBACrC,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC1C,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;gBAC1C,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;YAC/C;QACF;IACF;IAEA,OAAO;AACT;AAuBO,MAAM,mBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;CACD;AAGM,SAAS,WAAW,OAAe;IACxC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,QAAQ,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAClD,IAAI,CAAC,OAAO;IAEZ,MAAM,OAAO,SAAS,eAAe;IAErC,SAAS;IACT,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAChD,MAAM,SAAS,IAAI,OAAO,CAAC,YAAY,OAAO,WAAW;QACzD,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE;IACxC;AACF;AAEO,SAAS,eAAe,OAAe;IAC5C,OAAO,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AAC7C;AAGO,SAAS;IACd,MAAM,WAAW,gBAAgB,WAAW;IAC5C,WAAW,SAAS,KAAK;AAC3B;AAGO,SAAS;IACd,OAAO,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC3D;AAEO,SAAS,eAAe,QAAmB;IAChD,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa;;IAClE,OAAO,SAAS,IAAI,CAAC,CAAA;QACnB,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,OAAO,QAAQ,IAAI,KAAK;QAC1B;QAEA,uDAAuD;QACvD,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;QACzC,MAAM,YAAY,IAAI,KAAK;QAE3B,IAAI,QAAQ,IAAI,KAAK,SAAS;YAC5B,OAAO,YAAY,QAAQ,OAAO,UAAU,QAAQ,MAC7C,YAAY,OAAO,OAAO,UAAU,OAAO;QACpD;QAEA,2DAA2D;QAC3D,iCAAiC;QACjC,OAAO,QAAQ,IAAI,KAAK;IAC1B,MAAM;AACR;AAEO,SAAS,kBACd,IAAY,EACZ,cAA8B,EAC9B,eAAgC;IAEhC,IAAI,CAAC,gBAAgB,iBAAiB,EAAE;QACtC,iDAAiD;QACjD,MAAM,YAAY,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAY;QACpF,OAAO,cAAc,CAAC,UAAU,IAAI;IACtC;IAEA,6BAA6B;IAC7B,MAAM,UAAU,gBAAgB,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,EAAE,SAAS,EAAE;YAChB,OAAO,EAAE,IAAI,KAAK;QACpB;QAEA,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,MAAM,YAAY,IAAI,KAAK;QAE3B,IAAI,EAAE,IAAI,KAAK,SAAS;YACtB,OAAO,YAAY,QAAQ,OAAO,UAAU,QAAQ,MAC7C,YAAY,OAAO,OAAO,UAAU,OAAO;QACpD;QAEA,OAAO,EAAE,IAAI,KAAK;IACpB;IAEA,IAAI,SAAS;QACX,IAAI,QAAQ,MAAM,EAAE;YAClB,OAAO;gBAAE,MAAM;gBAAI,OAAO;gBAAI,QAAQ;YAAK;QAC7C;QACA,IAAI,QAAQ,KAAK,EAAE;YACjB,OAAO,QAAQ,KAAK;QACtB;IACF;IAEA,8BAA8B;IAC9B,MAAM,YAAY,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAAE,SAAS;IAAY;IACpF,OAAO,cAAc,CAAC,UAAU,IAAI;AACtC;AAEO,SAAS,gBAAgB,OAAyB;IACvD,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,QAAQ,IAAI,EAAE,QAAQ;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC;IACd,OAAO;QACL,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI;QAClC,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,IAAI,CAAC,QAAQ,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,MAAM,IAAI,QAAQ,KAAK,EAAE;QACpC,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,KAAK,EAAE;YAC/C,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,QAAQ,KAAK,CAAC,IAAI,IAAI,QAAQ,KAAK,CAAC,KAAK,EAAE;YACpD,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;AACT;AAEO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YAAS,OAAO;QACrB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAU,OAAO;QACtB;YAAS,OAAO;IAClB;AACF;AAEO,SAAS,oBAAoB,QAAmB,EAAE,OAAe,EAAE;IACxE,MAAM,QAAQ,IAAI;IAClB,MAAM,aAAa,IAAI,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IAEpE,OAAO,SAAS,MAAM,CAAC,CAAA;QACrB,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;QAEzC,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,OAAO,eAAe,SAAS,eAAe;QAChD;QAEA,4EAA4E;QAC5E,IAAI,QAAQ,IAAI,KAAK,SAAS;YAC5B,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,IAAI,YAAY,QAAQ,IAAI,YAAY,OAAO;YAC1F,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,YAAY,QAAQ,IAAI,YAAY,OAAO;YAE9F,OAAO,AAAC,YAAY,SAAS,YAAY,cACjC,YAAY,SAAS,YAAY;QAC3C;QAEA,OAAO;IACT,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;AACzE", "debugId": null}}, {"offset": {"line": 1966, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/holiday-api.ts"], "sourcesContent": ["// 节假日API服务\nexport interface HolidayApiResponse {\n  code: number\n  message: string\n  data: {\n    list: Array<{\n      name: string\n      date: string\n      isOffDay: boolean\n      holiday?: {\n        holiday: boolean\n        name: string\n        wage: number\n        date: string\n        rest: number\n      }\n    }>\n  }\n}\n\nexport interface ChinaHolidayApiResponse {\n  code: number\n  type: {\n    type: number\n    name: string\n    week: number\n  }\n  holiday?: {\n    holiday: boolean\n    name: string\n    wage: number\n    date: string\n    rest: number\n  }\n}\n\nexport interface TimorHolidayResponse {\n  code: number\n  holiday: {\n    '01-01': { name: string, wage: number, date: string, rest: number }\n    [key: string]: { name: string, wage: number, date: string, rest: number }\n  }\n}\n\n// 节假日API配置\nexport const HOLIDAY_API_CONFIGS = {\n  // 免费API - 天行数据（需要申请key）\n  tianapi: {\n    name: '天行数据',\n    url: 'https://apis.tianapi.com/jiejiari/index',\n    requiresKey: true,\n    description: '提供中国法定节假日和调休信息',\n    website: 'https://www.tianapi.com/'\n  },\n  \n  // 免费API - 聚合数据（需要申请key）\n  juhe: {\n    name: '聚合数据',\n    url: 'https://apis.juhe.cn/fapig/calendar/query.php',\n    requiresKey: true,\n    description: '提供节假日查询服务',\n    website: 'https://www.juhe.cn/'\n  },\n  \n  // 免费API - 万年历API\n  wannianli: {\n    name: '万年历API',\n    url: 'https://timor.tech/api/holiday',\n    requiresKey: false,\n    description: '免费的中国节假日API',\n    website: 'https://timor.tech/'\n  },\n  \n  // 免费API - 中国节假日API\n  china_holiday: {\n    name: '中国节假日API',\n    url: 'https://api.apihubs.cn/holiday/get',\n    requiresKey: false,\n    description: '免费的中国法定节假日查询',\n    website: 'https://api.apihubs.cn/'\n  }\n}\n\nexport class HolidayApiService {\n  private static apiKey: string = ''\n  private static selectedApi: keyof typeof HOLIDAY_API_CONFIGS = 'wannianli'\n\n  static setApiKey(key: string) {\n    this.apiKey = key\n  }\n\n  static setSelectedApi(api: keyof typeof HOLIDAY_API_CONFIGS) {\n    this.selectedApi = api\n  }\n\n  static getSelectedApi() {\n    return this.selectedApi\n  }\n\n  static getApiConfig() {\n    return HOLIDAY_API_CONFIGS[this.selectedApi]\n  }\n\n  // 获取指定年份的节假日\n  static async getHolidays(year: number): Promise<any[]> {\n    try {\n      switch (this.selectedApi) {\n        case 'wannianli':\n          return await this.getHolidaysFromWannianli(year)\n        case 'china_holiday':\n          return await this.getHolidaysFromChinaHoliday(year)\n        case 'tianapi':\n          return await this.getHolidaysFromTianapi(year)\n        case 'juhe':\n          return await this.getHolidaysFromJuhe(year)\n        default:\n          throw new Error('不支持的API类型')\n      }\n    } catch (error) {\n      console.error('获取节假日数据失败:', error)\n      throw error\n    }\n  }\n\n  // 万年历API - 免费，无需key\n  private static async getHolidaysFromWannianli(year: number): Promise<any[]> {\n    const response = await fetch(`https://timor.tech/api/holiday/year/${year}`)\n    \n    if (!response.ok) {\n      throw new Error(`API请求失败: ${response.status}`)\n    }\n\n    const data: TimorHolidayResponse = await response.json()\n    \n    if (data.code !== 0) {\n      throw new Error(`API返回错误: ${data.code}`)\n    }\n\n    // 转换数据格式\n    const holidays = []\n    for (const [dateKey, holiday] of Object.entries(data.holiday)) {\n      holidays.push({\n        id: `holiday-${year}-${dateKey}`,\n        name: holiday.name,\n        date: `${year}-${dateKey}`,\n        type: 'fixed' as const,\n        recurring: true,\n        closed: holiday.rest === 1,\n        description: `法定节假日 - ${holiday.name}`,\n        source: 'wannianli'\n      })\n    }\n\n    return holidays\n  }\n\n  // 中国节假日API - 免费，无需key\n  private static async getHolidaysFromChinaHoliday(year: number): Promise<any[]> {\n    const holidays = []\n    \n    // 需要逐月查询\n    for (let month = 1; month <= 12; month++) {\n      try {\n        const monthStr = month.toString().padStart(2, '0')\n        const response = await fetch(`https://api.apihubs.cn/holiday/get?year=${year}&month=${monthStr}`)\n        \n        if (!response.ok) continue\n        \n        const data: ChinaHolidayApiResponse = await response.json()\n        \n        if (data.code === 0 && data.holiday) {\n          holidays.push({\n            id: `holiday-${year}-${monthStr}`,\n            name: data.holiday.name,\n            date: data.holiday.date,\n            type: 'fixed' as const,\n            recurring: true,\n            closed: data.holiday.rest === 1,\n            description: `法定节假日 - ${data.holiday.name}`,\n            source: 'china_holiday'\n          })\n        }\n      } catch (error) {\n        console.warn(`获取${year}年${month}月节假日失败:`, error)\n      }\n    }\n\n    return holidays\n  }\n\n  // 天行数据API - 需要key\n  private static async getHolidaysFromTianapi(year: number): Promise<any[]> {\n    if (!this.apiKey) {\n      throw new Error('天行数据API需要提供API Key')\n    }\n\n    const response = await fetch(`https://apis.tianapi.com/jiejiari/index?key=${this.apiKey}&year=${year}`)\n    \n    if (!response.ok) {\n      throw new Error(`API请求失败: ${response.status}`)\n    }\n\n    const data: HolidayApiResponse = await response.json()\n    \n    if (data.code !== 200) {\n      throw new Error(`API返回错误: ${data.message}`)\n    }\n\n    // 转换数据格式\n    return data.data.list\n      .filter(item => item.holiday)\n      .map(item => ({\n        id: `holiday-${item.date}`,\n        name: item.holiday!.name,\n        date: item.date,\n        type: 'fixed' as const,\n        recurring: true,\n        closed: item.holiday!.rest === 1,\n        description: `法定节假日 - ${item.holiday!.name}`,\n        source: 'tianapi'\n      }))\n  }\n\n  // 聚合数据API - 需要key\n  private static async getHolidaysFromJuhe(year: number): Promise<any[]> {\n    if (!this.apiKey) {\n      throw new Error('聚合数据API需要提供API Key')\n    }\n\n    const response = await fetch(`https://apis.juhe.cn/fapig/calendar/query.php?key=${this.apiKey}&year=${year}`)\n    \n    if (!response.ok) {\n      throw new Error(`API请求失败: ${response.status}`)\n    }\n\n    const data = await response.json()\n    \n    if (data.error_code !== 0) {\n      throw new Error(`API返回错误: ${data.reason}`)\n    }\n\n    // 转换数据格式（需要根据实际API响应调整）\n    return data.result.map((item: any) => ({\n      id: `holiday-${item.date}`,\n      name: item.name,\n      date: item.date,\n      type: 'fixed' as const,\n      recurring: true,\n      closed: item.status === 1,\n      description: `法定节假日 - ${item.name}`,\n      source: 'juhe'\n    }))\n  }\n\n  // 检查API连接状态\n  static async testApiConnection(): Promise<{ success: boolean; message: string }> {\n    try {\n      const currentYear = new Date().getFullYear()\n      await this.getHolidays(currentYear)\n      return { success: true, message: 'API连接正常' }\n    } catch (error) {\n      return { \n        success: false, \n        message: error instanceof Error ? error.message : '未知错误' \n      }\n    }\n  }\n\n  // 获取今日是否为节假日\n  static async isTodayHoliday(): Promise<{ isHoliday: boolean; holidayName?: string }> {\n    try {\n      const today = new Date()\n      const year = today.getFullYear()\n      const dateStr = today.toISOString().split('T')[0]\n      \n      const holidays = await this.getHolidays(year)\n      const todayHoliday = holidays.find(h => h.date === dateStr)\n      \n      return {\n        isHoliday: !!todayHoliday,\n        holidayName: todayHoliday?.name\n      }\n    } catch (error) {\n      console.error('检查今日节假日失败:', error)\n      return { isHoliday: false }\n    }\n  }\n\n  // 获取即将到来的节假日\n  static async getUpcomingHolidays(days: number = 30): Promise<any[]> {\n    try {\n      const today = new Date()\n      const year = today.getFullYear()\n      const nextYear = year + 1\n      \n      // 获取今年和明年的节假日\n      const [thisYearHolidays, nextYearHolidays] = await Promise.all([\n        this.getHolidays(year),\n        this.getHolidays(nextYear)\n      ])\n      \n      const allHolidays = [...thisYearHolidays, ...nextYearHolidays]\n      const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000)\n      \n      return allHolidays\n        .filter(holiday => {\n          const holidayDate = new Date(holiday.date)\n          return holidayDate >= today && holidayDate <= futureDate\n        })\n        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n        .slice(0, 10) // 最多返回10个\n    } catch (error) {\n      console.error('获取即将到来的节假日失败:', error)\n      return []\n    }\n  }\n}\n\n// 节假日缓存管理\nexport class HolidayCache {\n  private static readonly CACHE_KEY = 'holiday_cache'\n  private static readonly CACHE_DURATION = 24 * 60 * 60 * 1000 // 24小时\n\n  static getCachedHolidays(year: number): any[] | null {\n    if (typeof window === 'undefined') return null\n\n    try {\n      const cached = localStorage.getItem(`${this.CACHE_KEY}_${year}`)\n      if (!cached) return null\n\n      const { data, timestamp } = JSON.parse(cached)\n      const now = Date.now()\n\n      if (now - timestamp > this.CACHE_DURATION) {\n        this.clearCache(year)\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.error('读取节假日缓存失败:', error)\n      return null\n    }\n  }\n\n  static setCachedHolidays(year: number, holidays: any[]): void {\n    if (typeof window === 'undefined') return\n\n    try {\n      const cacheData = {\n        data: holidays,\n        timestamp: Date.now()\n      }\n      localStorage.setItem(`${this.CACHE_KEY}_${year}`, JSON.stringify(cacheData))\n    } catch (error) {\n      console.error('保存节假日缓存失败:', error)\n    }\n  }\n\n  static clearCache(year?: number): void {\n    if (typeof window === 'undefined') return\n\n    try {\n      if (year) {\n        localStorage.removeItem(`${this.CACHE_KEY}_${year}`)\n      } else {\n        // 清除所有节假日缓存\n        const keys = Object.keys(localStorage)\n        keys.forEach(key => {\n          if (key.startsWith(this.CACHE_KEY)) {\n            localStorage.removeItem(key)\n          }\n        })\n      }\n    } catch (error) {\n      console.error('清除节假日缓存失败:', error)\n    }\n  }\n}\n\n// 带缓存的节假日服务\nexport class CachedHolidayService {\n  static async getHolidays(year: number): Promise<any[]> {\n    // 先尝试从缓存获取\n    const cached = HolidayCache.getCachedHolidays(year)\n    if (cached) {\n      return cached\n    }\n\n    // 缓存未命中，从API获取\n    try {\n      const holidays = await HolidayApiService.getHolidays(year)\n      HolidayCache.setCachedHolidays(year, holidays)\n      return holidays\n    } catch (error) {\n      console.error('获取节假日失败，返回空数组:', error)\n      return []\n    }\n  }\n\n  static async refreshHolidays(year: number): Promise<any[]> {\n    HolidayCache.clearCache(year)\n    return await this.getHolidays(year)\n  }\n}\n"], "names": [], "mappings": "AAAA,WAAW;;;;;;;AA6CJ,MAAM,sBAAsB;IACjC,wBAAwB;IACxB,SAAS;QACP,MAAM;QACN,KAAK;QACL,aAAa;QACb,aAAa;QACb,SAAS;IACX;IAEA,wBAAwB;IACxB,MAAM;QACJ,MAAM;QACN,KAAK;QACL,aAAa;QACb,aAAa;QACb,SAAS;IACX;IAEA,iBAAiB;IACjB,WAAW;QACT,MAAM;QACN,KAAK;QACL,aAAa;QACb,aAAa;QACb,SAAS;IACX;IAEA,mBAAmB;IACnB,eAAe;QACb,MAAM;QACN,KAAK;QACL,aAAa;QACb,aAAa;QACb,SAAS;IACX;AACF;AAEO,MAAM;IACX,OAAe,SAAiB,GAAE;IAClC,OAAe,cAAgD,YAAW;IAE1E,OAAO,UAAU,GAAW,EAAE;QAC5B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,OAAO,eAAe,GAAqC,EAAE;QAC3D,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA,OAAO,iBAAiB;QACtB,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,OAAO,eAAe;QACpB,OAAO,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC;IAC9C;IAEA,aAAa;IACb,aAAa,YAAY,IAAY,EAAkB;QACrD,IAAI;YACF,OAAQ,IAAI,CAAC,WAAW;gBACtB,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC;gBAC7C,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,2BAA2B,CAAC;gBAChD,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC;gBAC3C,KAAK;oBACH,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBACxC;oBACE,MAAM,IAAI,MAAM;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,aAAqB,yBAAyB,IAAY,EAAkB;QAC1E,MAAM,WAAW,MAAM,MAAM,CAAC,oCAAoC,EAAE,MAAM;QAE1E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;QAC/C;QAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;QAEtD,IAAI,KAAK,IAAI,KAAK,GAAG;YACnB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;QACzC;QAEA,SAAS;QACT,MAAM,WAAW,EAAE;QACnB,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,OAAO,OAAO,CAAC,KAAK,OAAO,EAAG;YAC7D,SAAS,IAAI,CAAC;gBACZ,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,SAAS;gBAChC,MAAM,QAAQ,IAAI;gBAClB,MAAM,GAAG,KAAK,CAAC,EAAE,SAAS;gBAC1B,MAAM;gBACN,WAAW;gBACX,QAAQ,QAAQ,IAAI,KAAK;gBACzB,aAAa,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE;gBACtC,QAAQ;YACV;QACF;QAEA,OAAO;IACT;IAEA,sBAAsB;IACtB,aAAqB,4BAA4B,IAAY,EAAkB;QAC7E,MAAM,WAAW,EAAE;QAEnB,SAAS;QACT,IAAK,IAAI,QAAQ,GAAG,SAAS,IAAI,QAAS;YACxC,IAAI;gBACF,MAAM,WAAW,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;gBAC9C,MAAM,WAAW,MAAM,MAAM,CAAC,wCAAwC,EAAE,KAAK,OAAO,EAAE,UAAU;gBAEhG,IAAI,CAAC,SAAS,EAAE,EAAE;gBAElB,MAAM,OAAgC,MAAM,SAAS,IAAI;gBAEzD,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,EAAE;oBACnC,SAAS,IAAI,CAAC;wBACZ,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,UAAU;wBACjC,MAAM,KAAK,OAAO,CAAC,IAAI;wBACvB,MAAM,KAAK,OAAO,CAAC,IAAI;wBACvB,MAAM;wBACN,WAAW;wBACX,QAAQ,KAAK,OAAO,CAAC,IAAI,KAAK;wBAC9B,aAAa,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE;wBAC3C,QAAQ;oBACV;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,MAAM,OAAO,CAAC,EAAE;YAC5C;QACF;QAEA,OAAO;IACT;IAEA,kBAAkB;IAClB,aAAqB,uBAAuB,IAAY,EAAkB;QACxE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,CAAC,4CAA4C,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM;QAEtG,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;QAC/C;QAEA,MAAM,OAA2B,MAAM,SAAS,IAAI;QAEpD,IAAI,KAAK,IAAI,KAAK,KAAK;YACrB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,KAAK,OAAO,EAAE;QAC5C;QAEA,SAAS;QACT,OAAO,KAAK,IAAI,CAAC,IAAI,CAClB,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,EAC3B,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;gBAC1B,MAAM,KAAK,OAAO,CAAE,IAAI;gBACxB,MAAM,KAAK,IAAI;gBACf,MAAM;gBACN,WAAW;gBACX,QAAQ,KAAK,OAAO,CAAE,IAAI,KAAK;gBAC/B,aAAa,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAE,IAAI,EAAE;gBAC5C,QAAQ;YACV,CAAC;IACL;IAEA,kBAAkB;IAClB,aAAqB,oBAAoB,IAAY,EAAkB;QACrE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,CAAC,kDAAkD,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM;QAE5G,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE;QAC/C;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,UAAU,KAAK,GAAG;YACzB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,KAAK,MAAM,EAAE;QAC3C;QAEA,wBAAwB;QACxB,OAAO,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;gBACrC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;gBAC1B,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM;gBACN,WAAW;gBACX,QAAQ,KAAK,MAAM,KAAK;gBACxB,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;gBACnC,QAAQ;YACV,CAAC;IACH;IAEA,YAAY;IACZ,aAAa,oBAAoE;QAC/E,IAAI;YACF,MAAM,cAAc,IAAI,OAAO,WAAW;YAC1C,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAU;QAC7C,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA,aAAa;IACb,aAAa,iBAAwE;QACnF,IAAI;YACF,MAAM,QAAQ,IAAI;YAClB,MAAM,OAAO,MAAM,WAAW;YAC9B,MAAM,UAAU,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEjD,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;YACxC,MAAM,eAAe,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;YAEnD,OAAO;gBACL,WAAW,CAAC,CAAC;gBACb,aAAa,cAAc;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,OAAO;gBAAE,WAAW;YAAM;QAC5B;IACF;IAEA,aAAa;IACb,aAAa,oBAAoB,OAAe,EAAE,EAAkB;QAClE,IAAI;YACF,MAAM,QAAQ,IAAI;YAClB,MAAM,OAAO,MAAM,WAAW;YAC9B,MAAM,WAAW,OAAO;YAExB,cAAc;YACd,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7D,IAAI,CAAC,WAAW,CAAC;gBACjB,IAAI,CAAC,WAAW,CAAC;aAClB;YAED,MAAM,cAAc;mBAAI;mBAAqB;aAAiB;YAC9D,MAAM,aAAa,IAAI,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;YAEpE,OAAO,YACJ,MAAM,CAAC,CAAA;gBACN,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;gBACzC,OAAO,eAAe,SAAS,eAAe;YAChD,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG,IAAI,UAAU;;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM;IACX,OAAwB,YAAY,gBAAe;IACnD,OAAwB,iBAAiB,KAAK,KAAK,KAAK,KAAK,OAAO;KAAR;IAE5D,OAAO,kBAAkB,IAAY,EAAgB;QACnD,wCAAmC,OAAO;;IAmB5C;IAEA,OAAO,kBAAkB,IAAY,EAAE,QAAe,EAAQ;QAC5D,wCAAmC;;IAWrC;IAEA,OAAO,WAAW,IAAa,EAAQ;QACrC,wCAAmC;;IAiBrC;AACF;AAGO,MAAM;IACX,aAAa,YAAY,IAAY,EAAkB;QACrD,WAAW;QACX,MAAM,SAAS,aAAa,iBAAiB,CAAC;QAC9C,IAAI,QAAQ;YACV,OAAO;QACT;QAEA,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,kBAAkB,WAAW,CAAC;YACrD,aAAa,iBAAiB,CAAC,MAAM;YACrC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO,EAAE;QACX;IACF;IAEA,aAAa,gBAAgB,IAAY,EAAkB;QACzD,aAAa,UAAU,CAAC;QACxB,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;IAChC;AACF", "debugId": null}}, {"offset": {"line": 2385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/settings/holiday-api-config.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { \n  Settings, \n  Wifi, \n  WifiOff, \n  RefreshCw, \n  ExternalLink,\n  Key,\n  CheckCircle,\n  XCircle,\n  AlertCircle\n} from 'lucide-react'\nimport { \n  HolidayApiService, \n  CachedHolidayService,\n  HolidayCache,\n  HOLIDAY_API_CONFIGS \n} from '@/lib/holiday-api'\n\ninterface HolidayApiConfigProps {\n  onHolidaysUpdate?: (holidays: any[]) => void\n}\n\nexport function HolidayApiConfig({ onHolidaysUpdate }: HolidayApiConfigProps) {\n  const [selectedApi, setSelectedApi] = useState<keyof typeof HOLIDAY_API_CONFIGS>('wannianli')\n  const [apiKey, setApiKey] = useState('')\n  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle')\n  const [connectionMessage, setConnectionMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [lastSync, setLastSync] = useState<string>('')\n\n  useEffect(() => {\n    // 加载保存的API配置\n    const savedApi = localStorage.getItem('holiday_api_selected')\n    const savedKey = localStorage.getItem('holiday_api_key')\n    const savedLastSync = localStorage.getItem('holiday_last_sync')\n\n    if (savedApi && savedApi in HOLIDAY_API_CONFIGS) {\n      setSelectedApi(savedApi as keyof typeof HOLIDAY_API_CONFIGS)\n      HolidayApiService.setSelectedApi(savedApi as keyof typeof HOLIDAY_API_CONFIGS)\n    }\n\n    if (savedKey) {\n      setApiKey(savedKey)\n      HolidayApiService.setApiKey(savedKey)\n    }\n\n    if (savedLastSync) {\n      setLastSync(savedLastSync)\n    }\n  }, [])\n\n  const handleApiChange = (api: keyof typeof HOLIDAY_API_CONFIGS) => {\n    setSelectedApi(api)\n    HolidayApiService.setSelectedApi(api)\n    localStorage.setItem('holiday_api_selected', api)\n    setConnectionStatus('idle')\n  }\n\n  const handleApiKeyChange = (key: string) => {\n    setApiKey(key)\n    HolidayApiService.setApiKey(key)\n    localStorage.setItem('holiday_api_key', key)\n    setConnectionStatus('idle')\n  }\n\n  const testConnection = async () => {\n    setConnectionStatus('testing')\n    setConnectionMessage('正在测试API连接...')\n\n    try {\n      const result = await HolidayApiService.testApiConnection()\n      if (result.success) {\n        setConnectionStatus('success')\n        setConnectionMessage(result.message)\n      } else {\n        setConnectionStatus('error')\n        setConnectionMessage(result.message)\n      }\n    } catch (error) {\n      setConnectionStatus('error')\n      setConnectionMessage(error instanceof Error ? error.message : '连接测试失败')\n    }\n  }\n\n  const syncHolidays = async () => {\n    setIsLoading(true)\n    try {\n      const currentYear = new Date().getFullYear()\n      const nextYear = currentYear + 1\n\n      // 获取今年和明年的节假日\n      const [thisYearHolidays, nextYearHolidays] = await Promise.all([\n        CachedHolidayService.refreshHolidays(currentYear),\n        CachedHolidayService.refreshHolidays(nextYear)\n      ])\n\n      const allHolidays = [...thisYearHolidays, ...nextYearHolidays]\n      \n      // 更新最后同步时间\n      const now = new Date().toLocaleString('zh-CN')\n      setLastSync(now)\n      localStorage.setItem('holiday_last_sync', now)\n\n      // 通知父组件更新节假日\n      if (onHolidaysUpdate) {\n        onHolidaysUpdate(allHolidays)\n      }\n\n      setConnectionStatus('success')\n      setConnectionMessage(`成功同步 ${allHolidays.length} 个节假日`)\n    } catch (error) {\n      setConnectionStatus('error')\n      setConnectionMessage(error instanceof Error ? error.message : '同步失败')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const clearCache = () => {\n    HolidayCache.clearCache()\n    setLastSync('')\n    localStorage.removeItem('holiday_last_sync')\n    setConnectionMessage('缓存已清除')\n  }\n\n  const currentApiConfig = HOLIDAY_API_CONFIGS[selectedApi]\n\n  const getStatusIcon = () => {\n    switch (connectionStatus) {\n      case 'testing':\n        return <RefreshCw className=\"h-4 w-4 animate-spin\" />\n      case 'success':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case 'error':\n        return <XCircle className=\"h-4 w-4 text-red-500\" />\n      default:\n        return <AlertCircle className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getStatusColor = () => {\n    switch (connectionStatus) {\n      case 'success':\n        return 'text-green-600 dark:text-green-400'\n      case 'error':\n        return 'text-red-600 dark:text-red-400'\n      case 'testing':\n        return 'text-blue-600 dark:text-blue-400'\n      default:\n        return 'text-gray-600 dark:text-gray-400'\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* API选择和配置 */}\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Settings className=\"h-5 w-5 mr-2\" />\n            节假日API配置\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* API选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">选择节假日API</label>\n            <Select value={selectedApi} onValueChange={handleApiChange}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {Object.entries(HOLIDAY_API_CONFIGS).map(([key, config]) => (\n                  <SelectItem key={key} value={key}>\n                    <div className=\"flex items-center space-x-2\">\n                      <span>{config.name}</span>\n                      {!config.requiresKey && (\n                        <Badge variant=\"secondary\" className=\"text-xs\">免费</Badge>\n                      )}\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            <p className=\"text-xs text-muted-foreground\">\n              {currentApiConfig.description}\n            </p>\n          </div>\n\n          {/* API Key输入 */}\n          {currentApiConfig.requiresKey && (\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium flex items-center\">\n                <Key className=\"h-4 w-4 mr-1\" />\n                API Key\n              </label>\n              <Input\n                type=\"password\"\n                value={apiKey}\n                onChange={(e) => handleApiKeyChange(e.target.value)}\n                placeholder=\"请输入API Key\"\n              />\n              <div className=\"flex items-center space-x-2 text-xs text-muted-foreground\">\n                <ExternalLink className=\"h-3 w-3\" />\n                <a \n                  href={currentApiConfig.website} \n                  target=\"_blank\" \n                  rel=\"noopener noreferrer\"\n                  className=\"hover:underline\"\n                >\n                  前往 {currentApiConfig.name} 申请API Key\n                </a>\n              </div>\n            </div>\n          )}\n\n          {/* 连接状态 */}\n          <div className=\"flex items-center justify-between p-3 rounded-lg border bg-muted/30\">\n            <div className=\"flex items-center space-x-2\">\n              {getStatusIcon()}\n              <span className={`text-sm font-medium ${getStatusColor()}`}>\n                {connectionStatus === 'idle' ? '未测试' :\n                 connectionStatus === 'testing' ? '测试中...' :\n                 connectionStatus === 'success' ? '连接正常' : '连接失败'}\n              </span>\n            </div>\n            <Button variant=\"outline\" size=\"sm\" onClick={testConnection} disabled={connectionStatus === 'testing'}>\n              测试连接\n            </Button>\n          </div>\n\n          {connectionMessage && (\n            <div className={`text-sm ${getStatusColor()}`}>\n              {connectionMessage}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* 节假日同步 */}\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <RefreshCw className=\"h-5 w-5 mr-2\" />\n            节假日数据同步\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"font-medium\">自动获取节假日</p>\n              <p className=\"text-sm text-muted-foreground\">\n                从API自动获取最新的法定节假日信息\n              </p>\n            </div>\n            <Button \n              onClick={syncHolidays} \n              disabled={isLoading || connectionStatus === 'error'}\n            >\n              {isLoading ? (\n                <>\n                  <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                  同步中...\n                </>\n              ) : (\n                <>\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\n                  立即同步\n                </>\n              )}\n            </Button>\n          </div>\n\n          {lastSync && (\n            <div className=\"text-sm text-muted-foreground\">\n              最后同步时间: {lastSync}\n            </div>\n          )}\n\n          <div className=\"flex items-center justify-between pt-2 border-t\">\n            <div>\n              <p className=\"text-sm font-medium\">清除缓存</p>\n              <p className=\"text-xs text-muted-foreground\">\n                清除本地缓存的节假日数据\n              </p>\n            </div>\n            <Button variant=\"outline\" size=\"sm\" onClick={clearCache}>\n              清除缓存\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* API说明 */}\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <AlertCircle className=\"h-5 w-5 mr-2\" />\n            API说明\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3 text-sm\">\n            <div>\n              <h4 className=\"font-medium\">推荐API</h4>\n              <ul className=\"mt-1 space-y-1 text-muted-foreground\">\n                <li>• <strong>万年历API</strong>: 免费，无需注册，数据准确</li>\n                <li>• <strong>中国节假日API</strong>: 免费，响应快速</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-medium\">付费API</h4>\n              <ul className=\"mt-1 space-y-1 text-muted-foreground\">\n                <li>• <strong>天行数据</strong>: 需要注册获取API Key，数据丰富</li>\n                <li>• <strong>聚合数据</strong>: 需要注册获取API Key，服务稳定</li>\n              </ul>\n            </div>\n            <div className=\"p-2 bg-blue-50 dark:bg-blue-950/30 rounded-lg\">\n              <p className=\"text-blue-700 dark:text-blue-300 text-xs\">\n                💡 建议优先使用免费API，如需更稳定的服务可考虑付费API\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAnBA;;;;;;;;;;AA8BO,SAAS,iBAAiB,EAAE,gBAAgB,EAAyB;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IACjF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IACnG,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAE3C,IAAI,YAAY,YAAY,4HAAA,CAAA,sBAAmB,EAAE;YAC/C,eAAe;YACf,4HAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;QACnC;QAEA,IAAI,UAAU;YACZ,UAAU;YACV,4HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC;QAC9B;QAEA,IAAI,eAAe;YACjB,YAAY;QACd;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,eAAe;QACf,4HAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;QACjC,aAAa,OAAO,CAAC,wBAAwB;QAC7C,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,UAAU;QACV,4HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC;QAC5B,aAAa,OAAO,CAAC,mBAAmB;QACxC,oBAAoB;IACtB;IAEA,MAAM,iBAAiB;QACrB,oBAAoB;QACpB,qBAAqB;QAErB,IAAI;YACF,MAAM,SAAS,MAAM,4HAAA,CAAA,oBAAiB,CAAC,iBAAiB;YACxD,IAAI,OAAO,OAAO,EAAE;gBAClB,oBAAoB;gBACpB,qBAAqB,OAAO,OAAO;YACrC,OAAO;gBACL,oBAAoB;gBACpB,qBAAqB,OAAO,OAAO;YACrC;QACF,EAAE,OAAO,OAAO;YACd,oBAAoB;YACpB,qBAAqB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAChE;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM,cAAc,IAAI,OAAO,WAAW;YAC1C,MAAM,WAAW,cAAc;YAE/B,cAAc;YACd,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7D,4HAAA,CAAA,uBAAoB,CAAC,eAAe,CAAC;gBACrC,4HAAA,CAAA,uBAAoB,CAAC,eAAe,CAAC;aACtC;YAED,MAAM,cAAc;mBAAI;mBAAqB;aAAiB;YAE9D,WAAW;YACX,MAAM,MAAM,IAAI,OAAO,cAAc,CAAC;YACtC,YAAY;YACZ,aAAa,OAAO,CAAC,qBAAqB;YAE1C,aAAa;YACb,IAAI,kBAAkB;gBACpB,iBAAiB;YACnB;YAEA,oBAAoB;YACpB,qBAAqB,CAAC,KAAK,EAAE,YAAY,MAAM,CAAC,KAAK,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,oBAAoB;YACpB,qBAAqB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAChE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,4HAAA,CAAA,eAAY,CAAC,UAAU;QACvB,YAAY;QACZ,aAAa,UAAU,CAAC;QACxB,qBAAqB;IACvB;IAEA,MAAM,mBAAmB,4HAAA,CAAA,sBAAmB,CAAC,YAAY;IAEzD,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAa,eAAe;;0DACzC,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;0DACX,OAAO,OAAO,CAAC,4HAAA,CAAA,sBAAmB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,iBACrD,8OAAC,kIAAA,CAAA,aAAU;wDAAW,OAAO;kEAC3B,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAM,OAAO,IAAI;;;;;;gEACjB,CAAC,OAAO,WAAW,kBAClB,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAAU;;;;;;;;;;;;uDAJpC;;;;;;;;;;;;;;;;kDAWvB,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,WAAW;;;;;;;;;;;;4BAKhC,iBAAiB,WAAW,kBAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,aAAY;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;gDACC,MAAM,iBAAiB,OAAO;gDAC9B,QAAO;gDACP,KAAI;gDACJ,WAAU;;oDACX;oDACK,iBAAiB,IAAI;oDAAC;;;;;;;;;;;;;;;;;;;0CAOlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ;0DACD,8OAAC;gDAAK,WAAW,CAAC,oBAAoB,EAAE,kBAAkB;0DACvD,qBAAqB,SAAS,QAC9B,qBAAqB,YAAY,WACjC,qBAAqB,YAAY,SAAS;;;;;;;;;;;;kDAG/C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;wCAAgB,UAAU,qBAAqB;kDAAW;;;;;;;;;;;;4BAKxG,mCACC,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,kBAAkB;0CAC1C;;;;;;;;;;;;;;;;;;0BAOT,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI1C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,aAAa,qBAAqB;kDAE3C,0BACC;;8DACE,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAA8B;;yEAIrD;;8DACE,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;4BAO7C,0BACC,8OAAC;gCAAI,WAAU;;oCAAgC;oCACpC;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/D,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI5C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;;wDAAG;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAC7B,8OAAC;;wDAAG;sEAAE,8OAAC;sEAAO;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAGnC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;;wDAAG;sEAAE,8OAAC;sEAAO;;;;;;wDAAa;;;;;;;8DAC3B,8OAAC;;wDAAG;sEAAE,8OAAC;sEAAO;;;;;;wDAAa;;;;;;;;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStE", "debugId": null}}, {"offset": {"line": 3145, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/settings/holiday-manager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport {\n  Calendar,\n  Plus,\n  Edit,\n  Trash2,\n  Clock,\n  AlertCircle,\n  Download,\n  RefreshCw,\n  Settings as SettingsIcon\n} from 'lucide-react'\nimport { \n  type Holiday, \n  type HolidaySettings,\n  type DayHours,\n  generateHolidayId,\n  validateHoliday,\n  formatHolidayDate,\n  getHolidayTypeLabel,\n  getUpcomingHolidays\n} from '@/lib/settings'\nimport { CachedHolidayService } from '@/lib/holiday-api'\nimport { HolidayApiConfig } from './holiday-api-config'\n\ninterface HolidayManagerProps {\n  holidaySettings: HolidaySettings\n  onUpdate: (settings: HolidaySettings) => void\n}\n\nexport function HolidayManager({ holidaySettings, onUpdate }: HolidayManagerProps) {\n  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)\n  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null)\n  const [errors, setErrors] = useState<string[]>([])\n  const [showApiConfig, setShowApiConfig] = useState(false)\n  const [isImporting, setIsImporting] = useState(false)\n\n  const [formData, setFormData] = useState<Partial<Holiday>>({\n    name: '',\n    date: '',\n    type: 'fixed',\n    recurring: true,\n    closed: true,\n    description: ''\n  })\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      date: '',\n      type: 'fixed',\n      recurring: true,\n      closed: true,\n      description: ''\n    })\n    setErrors([])\n  }\n\n  const handleAddHoliday = () => {\n    const validationErrors = validateHoliday(formData)\n    if (validationErrors.length > 0) {\n      setErrors(validationErrors)\n      return\n    }\n\n    const newHoliday: Holiday = {\n      id: generateHolidayId(),\n      name: formData.name!,\n      date: formData.date!,\n      type: formData.type as Holiday['type'],\n      recurring: formData.recurring!,\n      closed: formData.closed!,\n      description: formData.description,\n      hours: formData.hours\n    }\n\n    const updatedSettings = {\n      ...holidaySettings,\n      holidays: [...holidaySettings.holidays, newHoliday]\n    }\n\n    onUpdate(updatedSettings)\n    setIsAddDialogOpen(false)\n    resetForm()\n  }\n\n  const handleEditHoliday = (holiday: Holiday) => {\n    setEditingHoliday(holiday)\n    setFormData(holiday)\n    setIsAddDialogOpen(true)\n  }\n\n  const handleUpdateHoliday = () => {\n    const validationErrors = validateHoliday(formData)\n    if (validationErrors.length > 0) {\n      setErrors(validationErrors)\n      return\n    }\n\n    const updatedHoliday: Holiday = {\n      ...editingHoliday!,\n      ...formData\n    } as Holiday\n\n    const updatedSettings = {\n      ...holidaySettings,\n      holidays: holidaySettings.holidays.map(h => \n        h.id === editingHoliday!.id ? updatedHoliday : h\n      )\n    }\n\n    onUpdate(updatedSettings)\n    setIsAddDialogOpen(false)\n    setEditingHoliday(null)\n    resetForm()\n  }\n\n  const handleDeleteHoliday = (holidayId: string) => {\n    if (confirm('确定要删除这个节假日吗？')) {\n      const updatedSettings = {\n        ...holidaySettings,\n        holidays: holidaySettings.holidays.filter(h => h.id !== holidayId)\n      }\n      onUpdate(updatedSettings)\n    }\n  }\n\n  const handleToggleHolidayMode = () => {\n    onUpdate({\n      ...holidaySettings,\n      enableHolidayMode: !holidaySettings.enableHolidayMode\n    })\n  }\n\n  const handleImportFromApi = async (apiHolidays: any[]) => {\n    setIsImporting(true)\n    try {\n      // 将API节假日转换为系统格式\n      const importedHolidays = apiHolidays.map(apiHoliday => ({\n        id: apiHoliday.id || generateHolidayId(),\n        name: apiHoliday.name,\n        date: apiHoliday.date,\n        type: apiHoliday.type || 'fixed',\n        recurring: apiHoliday.recurring !== false,\n        closed: apiHoliday.closed !== false,\n        description: apiHoliday.description || `从API导入 - ${apiHoliday.name}`,\n        source: apiHoliday.source || 'api'\n      }))\n\n      // 合并现有节假日和导入的节假日，避免重复\n      const existingHolidays = holidaySettings.holidays.filter(h => !h.source || h.source !== 'api')\n      const mergedHolidays = [...existingHolidays, ...importedHolidays]\n\n      const updatedSettings = {\n        ...holidaySettings,\n        holidays: mergedHolidays\n      }\n\n      onUpdate(updatedSettings)\n      setShowApiConfig(false)\n    } catch (error) {\n      console.error('导入节假日失败:', error)\n    } finally {\n      setIsImporting(false)\n    }\n  }\n\n  const handleQuickImport = async () => {\n    setIsImporting(true)\n    try {\n      const currentYear = new Date().getFullYear()\n      const nextYear = currentYear + 1\n\n      // 获取今年和明年的节假日\n      const [thisYearHolidays, nextYearHolidays] = await Promise.all([\n        CachedHolidayService.getHolidays(currentYear),\n        CachedHolidayService.getHolidays(nextYear)\n      ])\n\n      const allApiHolidays = [...thisYearHolidays, ...nextYearHolidays]\n      await handleImportFromApi(allApiHolidays)\n    } catch (error) {\n      console.error('快速导入失败:', error)\n      alert('导入失败，请检查API配置')\n    } finally {\n      setIsImporting(false)\n    }\n  }\n\n  const upcomingHolidays = getUpcomingHolidays(holidaySettings.holidays)\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Holiday Mode Toggle */}\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <span className=\"flex items-center\">\n              <Calendar className=\"h-5 w-5 mr-2\" />\n              节假日模式\n            </span>\n            <Button\n              variant={holidaySettings.enableHolidayMode ? \"default\" : \"outline\"}\n              onClick={handleToggleHolidayMode}\n            >\n              {holidaySettings.enableHolidayMode ? '已启用' : '已禁用'}\n            </Button>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-sm text-muted-foreground\">\n            启用节假日模式后，系统将在节假日期间自动调整营业时间或休息。\n          </p>\n        </CardContent>\n      </Card>\n\n      {/* Upcoming Holidays */}\n      {holidaySettings.enableHolidayMode && upcomingHolidays.length > 0 && (\n        <Card className=\"shadow-elegant\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <AlertCircle className=\"h-5 w-5 mr-2\" />\n              即将到来的节假日\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              {upcomingHolidays.slice(0, 3).map((holiday) => (\n                <div key={holiday.id} className=\"flex items-center justify-between p-2 rounded-lg bg-muted/30\">\n                  <div>\n                    <span className=\"font-medium\">{holiday.name}</span>\n                    <span className=\"text-sm text-muted-foreground ml-2\">\n                      {formatHolidayDate(holiday.date)}\n                    </span>\n                  </div>\n                  <Badge variant={holiday.closed ? \"destructive\" : \"secondary\"}>\n                    {holiday.closed ? '休息' : '营业'}\n                  </Badge>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Holiday List */}\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <span>节假日管理</span>\n            <div className=\"flex items-center space-x-2\">\n              <Button variant=\"outline\" onClick={handleQuickImport} disabled={isImporting}>\n                {isImporting ? (\n                  <>\n                    <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                    导入中...\n                  </>\n                ) : (\n                  <>\n                    <Download className=\"h-4 w-4 mr-2\" />\n                    从API导入\n                  </>\n                )}\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowApiConfig(true)}>\n                <SettingsIcon className=\"h-4 w-4 mr-2\" />\n                API设置\n              </Button>\n              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>\n                <DialogTrigger asChild>\n                  <Button onClick={resetForm}>\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    手动添加\n                  </Button>\n                </DialogTrigger>\n                <DialogContent className=\"max-w-md\">\n                  <DialogHeader>\n                  <DialogTitle>\n                    {editingHoliday ? '编辑节假日' : '添加节假日'}\n                  </DialogTitle>\n                </DialogHeader>\n                \n                {/* Error Display */}\n                {errors.length > 0 && (\n                  <div className=\"p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n                    <ul className=\"text-sm text-red-600 dark:text-red-400\">\n                      {errors.map((error, index) => (\n                        <li key={index}>• {error}</li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <label className=\"text-sm font-medium\">节假日名称</label>\n                    <Input\n                      value={formData.name || ''}\n                      onChange={(e) => setFormData({...formData, name: e.target.value})}\n                      placeholder=\"例如：春节、国庆节\"\n                    />\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <label className=\"text-sm font-medium\">日期</label>\n                    <Input\n                      type=\"date\"\n                      value={formData.date || ''}\n                      onChange={(e) => setFormData({...formData, date: e.target.value})}\n                    />\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <label className=\"text-sm font-medium\">类型</label>\n                    <Select \n                      value={formData.type || 'fixed'} \n                      onValueChange={(value) => setFormData({...formData, type: value as Holiday['type']})}\n                    >\n                      <SelectTrigger>\n                        <SelectValue />\n                      </SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"fixed\">固定日期</SelectItem>\n                        <SelectItem value=\"lunar\">农历日期</SelectItem>\n                        <SelectItem value=\"custom\">自定义</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.recurring || false}\n                      onChange={(e) => setFormData({...formData, recurring: e.target.checked})}\n                      className=\"rounded\"\n                    />\n                    <label className=\"text-sm\">每年重复</label>\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.closed || false}\n                      onChange={(e) => setFormData({...formData, closed: e.target.checked})}\n                      className=\"rounded\"\n                    />\n                    <label className=\"text-sm\">休息日</label>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <label className=\"text-sm font-medium\">描述（可选）</label>\n                    <Input\n                      value={formData.description || ''}\n                      onChange={(e) => setFormData({...formData, description: e.target.value})}\n                      placeholder=\"节假日描述\"\n                    />\n                  </div>\n\n                  <div className=\"flex justify-end space-x-2\">\n                    <Button \n                      variant=\"outline\" \n                      onClick={() => {\n                        setIsAddDialogOpen(false)\n                        setEditingHoliday(null)\n                        resetForm()\n                      }}\n                    >\n                      取消\n                    </Button>\n                    <Button onClick={editingHoliday ? handleUpdateHoliday : handleAddHoliday}>\n                      {editingHoliday ? '更新' : '添加'}\n                    </Button>\n                  </div>\n                </div>\n                  </DialogContent>\n              </Dialog>\n            </div>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {holidaySettings.holidays.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <Calendar className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>还没有设置任何节假日</p>\n              <p className=\"text-sm\">点击\"添加节假日\"开始设置</p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {holidaySettings.holidays.map((holiday) => (\n                <div key={holiday.id} className=\"flex items-center justify-between p-3 rounded-lg border\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3\">\n                      <h4 className=\"font-medium\">{holiday.name}</h4>\n                      <Badge variant=\"outline\">\n                        {getHolidayTypeLabel(holiday.type)}\n                      </Badge>\n                      {holiday.recurring && (\n                        <Badge variant=\"secondary\">每年重复</Badge>\n                      )}\n                      <Badge variant={holiday.closed ? \"destructive\" : \"default\"}>\n                        {holiday.closed ? '休息' : '营业'}\n                      </Badge>\n                      {holiday.source && holiday.source !== 'manual' && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {holiday.source === 'api' ? 'API' :\n                           holiday.source === 'wannianli' ? '万年历' :\n                           holiday.source === 'china_holiday' ? '中国节假日' :\n                           holiday.source === 'tianapi' ? '天行数据' :\n                           holiday.source === 'juhe' ? '聚合数据' : 'API'}\n                        </Badge>\n                      )}\n                    </div>\n                    <div className=\"flex items-center space-x-4 mt-1 text-sm text-muted-foreground\">\n                      <span>{formatHolidayDate(holiday.date)}</span>\n                      {holiday.description && (\n                        <span>• {holiday.description}</span>\n                      )}\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => handleEditHoliday(holiday)}\n                    >\n                      <Edit className=\"h-4 w-4\" />\n                    </Button>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => handleDeleteHoliday(holiday.id)}\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* API配置对话框 */}\n      <Dialog open={showApiConfig} onOpenChange={setShowApiConfig}>\n        <DialogContent className=\"max-w-4xl max-h-[80vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle>节假日API配置</DialogTitle>\n          </DialogHeader>\n          <HolidayApiConfig onHolidaysUpdate={handleImportFromApi} />\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAUA;AACA;AA/BA;;;;;;;;;;;;;AAsCO,SAAS,eAAe,EAAE,eAAe,EAAE,QAAQ,EAAuB;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,MAAM;QACN,MAAM;QACN,MAAM;QACN,WAAW;QACX,QAAQ;QACR,aAAa;IACf;IAEA,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,MAAM;YACN,MAAM;YACN,WAAW;YACX,QAAQ;YACR,aAAa;QACf;QACA,UAAU,EAAE;IACd;IAEA,MAAM,mBAAmB;QACvB,MAAM,mBAAmB,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACzC,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,UAAU;YACV;QACF;QAEA,MAAM,aAAsB;YAC1B,IAAI,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;YACpB,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;YACnB,WAAW,SAAS,SAAS;YAC7B,QAAQ,SAAS,MAAM;YACvB,aAAa,SAAS,WAAW;YACjC,OAAO,SAAS,KAAK;QACvB;QAEA,MAAM,kBAAkB;YACtB,GAAG,eAAe;YAClB,UAAU;mBAAI,gBAAgB,QAAQ;gBAAE;aAAW;QACrD;QAEA,SAAS;QACT,mBAAmB;QACnB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,YAAY;QACZ,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,MAAM,mBAAmB,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACzC,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,UAAU;YACV;QACF;QAEA,MAAM,iBAA0B;YAC9B,GAAG,cAAc;YACjB,GAAG,QAAQ;QACb;QAEA,MAAM,kBAAkB;YACtB,GAAG,eAAe;YAClB,UAAU,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAA,IACrC,EAAE,EAAE,KAAK,eAAgB,EAAE,GAAG,iBAAiB;QAEnD;QAEA,SAAS;QACT,mBAAmB;QACnB,kBAAkB;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,QAAQ,iBAAiB;YAC3B,MAAM,kBAAkB;gBACtB,GAAG,eAAe;gBAClB,UAAU,gBAAgB,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1D;YACA,SAAS;QACX;IACF;IAEA,MAAM,0BAA0B;QAC9B,SAAS;YACP,GAAG,eAAe;YAClB,mBAAmB,CAAC,gBAAgB,iBAAiB;QACvD;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,eAAe;QACf,IAAI;YACF,iBAAiB;YACjB,MAAM,mBAAmB,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;oBACtD,IAAI,WAAW,EAAE,IAAI,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;oBACrC,MAAM,WAAW,IAAI;oBACrB,MAAM,WAAW,IAAI;oBACrB,MAAM,WAAW,IAAI,IAAI;oBACzB,WAAW,WAAW,SAAS,KAAK;oBACpC,QAAQ,WAAW,MAAM,KAAK;oBAC9B,aAAa,WAAW,WAAW,IAAI,CAAC,SAAS,EAAE,WAAW,IAAI,EAAE;oBACpE,QAAQ,WAAW,MAAM,IAAI;gBAC/B,CAAC;YAED,sBAAsB;YACtB,MAAM,mBAAmB,gBAAgB,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,KAAK;YACxF,MAAM,iBAAiB;mBAAI;mBAAqB;aAAiB;YAEjE,MAAM,kBAAkB;gBACtB,GAAG,eAAe;gBAClB,UAAU;YACZ;YAEA,SAAS;YACT,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;QAC5B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;QACf,IAAI;YACF,MAAM,cAAc,IAAI,OAAO,WAAW;YAC1C,MAAM,WAAW,cAAc;YAE/B,cAAc;YACd,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7D,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;gBACjC,4HAAA,CAAA,uBAAoB,CAAC,WAAW,CAAC;aAClC;YAED,MAAM,iBAAiB;mBAAI;mBAAqB;aAAiB;YACjE,MAAM,oBAAoB;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,gBAAgB,QAAQ;IAErE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;oCAAK,WAAU;;sDACd,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,gBAAgB,iBAAiB,GAAG,YAAY;oCACzD,SAAS;8CAER,gBAAgB,iBAAiB,GAAG,QAAQ;;;;;;;;;;;;;;;;;kCAInD,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;YAOhD,gBAAgB,iBAAiB,IAAI,iBAAiB,MAAM,GAAG,mBAC9D,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAI5C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACjC,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAe,QAAQ,IAAI;;;;;;8DAC3C,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,IAAI;;;;;;;;;;;;sDAGnC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,QAAQ,MAAM,GAAG,gBAAgB;sDAC9C,QAAQ,MAAM,GAAG,OAAO;;;;;;;mCARnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAkB9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;4CAAmB,UAAU;sDAC7D,4BACC;;kEACE,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAA8B;;6EAIrD;;kEACE,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;sDAK3C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,iBAAiB;;8DACxD,8OAAC,0MAAA,CAAA,WAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAG3C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAM;4CAAiB,cAAc;;8DAC3C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,OAAO;8DACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAS;;0EACf,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAIrC,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;;sEACvB,8OAAC,kIAAA,CAAA,eAAY;sEACb,cAAA,8OAAC,kIAAA,CAAA,cAAW;0EACT,iBAAiB,UAAU;;;;;;;;;;;wDAK/B,OAAO,MAAM,GAAG,mBACf,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAG,WAAU;0EACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;;4EAAe;4EAAG;;uEAAV;;;;;;;;;;;;;;;sEAMjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAAsB;;;;;;sFACvC,8OAAC,iIAAA,CAAA,QAAK;4EACJ,OAAO,SAAS,IAAI,IAAI;4EACxB,UAAU,CAAC,IAAM,YAAY;oFAAC,GAAG,QAAQ;oFAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gFAAA;4EAC/D,aAAY;;;;;;;;;;;;8EAIhB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAAsB;;;;;;sFACvC,8OAAC,iIAAA,CAAA,QAAK;4EACJ,MAAK;4EACL,OAAO,SAAS,IAAI,IAAI;4EACxB,UAAU,CAAC,IAAM,YAAY;oFAAC,GAAG,QAAQ;oFAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gFAAA;;;;;;;;;;;;8EAInE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAAsB;;;;;;sFACvC,8OAAC,kIAAA,CAAA,SAAM;4EACL,OAAO,SAAS,IAAI,IAAI;4EACxB,eAAe,CAAC,QAAU,YAAY;oFAAC,GAAG,QAAQ;oFAAE,MAAM;gFAAwB;;8FAElF,8OAAC,kIAAA,CAAA,gBAAa;8FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8FAEd,8OAAC,kIAAA,CAAA,gBAAa;;sGACZ,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAQ;;;;;;sGAC1B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAQ;;;;;;sGAC1B,8OAAC,kIAAA,CAAA,aAAU;4FAAC,OAAM;sGAAS;;;;;;;;;;;;;;;;;;;;;;;;8EAKjC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,SAAS,SAAS,SAAS,IAAI;4EAC/B,UAAU,CAAC,IAAM,YAAY;oFAAC,GAAG,QAAQ;oFAAE,WAAW,EAAE,MAAM,CAAC,OAAO;gFAAA;4EACtE,WAAU;;;;;;sFAEZ,8OAAC;4EAAM,WAAU;sFAAU;;;;;;;;;;;;8EAG7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,SAAS,SAAS,MAAM,IAAI;4EAC5B,UAAU,CAAC,IAAM,YAAY;oFAAC,GAAG,QAAQ;oFAAE,QAAQ,EAAE,MAAM,CAAC,OAAO;gFAAA;4EACnE,WAAU;;;;;;sFAEZ,8OAAC;4EAAM,WAAU;sFAAU;;;;;;;;;;;;8EAG7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAM,WAAU;sFAAsB;;;;;;sFACvC,8OAAC,iIAAA,CAAA,QAAK;4EACJ,OAAO,SAAS,WAAW,IAAI;4EAC/B,UAAU,CAAC,IAAM,YAAY;oFAAC,GAAG,QAAQ;oFAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gFAAA;4EACtE,aAAY;;;;;;;;;;;;8EAIhB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,SAAS;gFACP,mBAAmB;gFACnB,kBAAkB;gFAClB;4EACF;sFACD;;;;;;sFAGD,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAS,iBAAiB,sBAAsB;sFACrD,iBAAiB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvC,8OAAC,gIAAA,CAAA,cAAW;kCACT,gBAAgB,QAAQ,CAAC,MAAM,KAAK,kBACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;iDAGzB,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC7B,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAe,QAAQ,IAAI;;;;;;sEACzC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEACZ,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,IAAI;;;;;;wDAElC,QAAQ,SAAS,kBAChB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;sEAE7B,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAS,QAAQ,MAAM,GAAG,gBAAgB;sEAC9C,QAAQ,MAAM,GAAG,OAAO;;;;;;wDAE1B,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,0BACpC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,QAAQ,MAAM,KAAK,QAAQ,QAC3B,QAAQ,MAAM,KAAK,cAAc,QACjC,QAAQ,MAAM,KAAK,kBAAkB,UACrC,QAAQ,MAAM,KAAK,YAAY,SAC/B,QAAQ,MAAM,KAAK,SAAS,SAAS;;;;;;;;;;;;8DAI5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,IAAI;;;;;;wDACpC,QAAQ,WAAW,kBAClB,8OAAC;;gEAAK;gEAAG,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;sDAIlC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,kBAAkB;8DAEjC,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,oBAAoB,QAAQ,EAAE;8DAE7C,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;mCA3Cd,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAsD9B,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAe,cAAc;0BACzC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC,0JAAA,CAAA,mBAAgB;4BAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 4136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { PageHeader } from '@/components/layout/page-header'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Settings, \n  Building, \n  Clock, \n  DollarSign, \n  Bell, \n  Shield, \n  Palette,\n  Globe,\n  Mail,\n  Phone,\n  MapPin,\n  Save,\n  User,\n  Key,\n  Database,\n  Smartphone,\n  Download,\n  Upload,\n  RotateCcw,\n  Calendar\n} from 'lucide-react'\nimport { useAuth } from '@/contexts/auth-context'\nimport {\n  SettingsManager,\n  validateBusinessSettings,\n  validateOperatingHours,\n  applyTheme,\n  getThemeConfig,\n  AVAILABLE_THEMES,\n  type BusinessSettings,\n  type OperatingHours,\n  type NotificationSettings,\n  type HolidaySettings\n} from '@/lib/settings'\nimport { HolidayManager } from '@/components/settings/holiday-manager'\n\nexport default function SettingsPage() {\n  const { user } = useAuth()\n  const [loading, setLoading] = useState(false)\n  const [activeTab, setActiveTab] = useState('business')\n  const [errors, setErrors] = useState<string[]>([])\n\n  // Settings state\n  const [businessSettings, setBusinessSettings] = useState<BusinessSettings>({\n    name: '皇家理发店',\n    address: '北京市朝阳区三里屯街道1号',\n    phone: '+86 138-0013-8000',\n    email: '<EMAIL>',\n    website: 'www.royalcuts.cn',\n    timezone: 'Asia/Shanghai',\n    currency: 'CNY'\n  })\n\n  const [operatingHours, setOperatingHours] = useState<OperatingHours>({\n    monday: { open: '09:00', close: '18:00', closed: false },\n    tuesday: { open: '09:00', close: '18:00', closed: false },\n    wednesday: { open: '09:00', close: '18:00', closed: false },\n    thursday: { open: '09:00', close: '19:00', closed: false },\n    friday: { open: '09:00', close: '19:00', closed: false },\n    saturday: { open: '08:00', close: '17:00', closed: false },\n    sunday: { open: '10:00', close: '16:00', closed: false }\n  })\n\n  const [notifications, setNotifications] = useState<NotificationSettings>({\n    emailNotifications: true,\n    smsNotifications: false,\n    appointmentReminders: true,\n    lowStockAlerts: true,\n    dailyReports: false,\n    weeklyReports: true\n  })\n\n  const [holidaySettings, setHolidaySettings] = useState<HolidaySettings>({\n    enableHolidayMode: true,\n    holidays: []\n  })\n\n  const [selectedTheme, setSelectedTheme] = useState('royal-gold')\n\n  // Load settings on component mount\n  useEffect(() => {\n    const settings = SettingsManager.getSettings()\n    setBusinessSettings(settings.business)\n    setOperatingHours(settings.operatingHours)\n    setHolidaySettings(settings.holidays)\n    setNotifications(settings.notifications)\n    setSelectedTheme(settings.theme)\n  }, [])\n\n  // 分别保存不同模块的设置\n  const handleSaveBusinessSettings = async () => {\n    setLoading(true)\n    setErrors([])\n\n    try {\n      const businessErrors = validateBusinessSettings(businessSettings)\n      if (businessErrors.length > 0) {\n        setErrors(businessErrors)\n        setLoading(false)\n        return\n      }\n\n      SettingsManager.updateBusinessSettings(businessSettings)\n      await new Promise(resolve => setTimeout(resolve, 500))\n      alert('商户信息保存成功！')\n    } catch (error) {\n      console.error('Error saving business settings:', error)\n      alert('保存商户信息时出错，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveOperatingHours = async () => {\n    setLoading(true)\n    setErrors([])\n\n    try {\n      const hoursErrors = validateOperatingHours(operatingHours)\n      if (hoursErrors.length > 0) {\n        setErrors(hoursErrors)\n        setLoading(false)\n        return\n      }\n\n      SettingsManager.updateOperatingHours(operatingHours)\n      await new Promise(resolve => setTimeout(resolve, 500))\n      alert('营业时间保存成功！')\n    } catch (error) {\n      console.error('Error saving operating hours:', error)\n      alert('保存营业时间时出错，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveHolidaySettings = async () => {\n    setLoading(true)\n    try {\n      SettingsManager.updateHolidaySettings(holidaySettings)\n      await new Promise(resolve => setTimeout(resolve, 500))\n      alert('节假日设置保存成功！')\n    } catch (error) {\n      console.error('Error saving holiday settings:', error)\n      alert('保存节假日设置时出错，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveNotifications = async () => {\n    setLoading(true)\n    try {\n      SettingsManager.updateNotificationSettings(notifications)\n      await new Promise(resolve => setTimeout(resolve, 500))\n      alert('通知设置保存成功！')\n    } catch (error) {\n      console.error('Error saving notifications:', error)\n      alert('保存通知设置时出错，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveTheme = async () => {\n    setLoading(true)\n    try {\n      SettingsManager.updateTheme(selectedTheme)\n      applyTheme(selectedTheme)\n      await new Promise(resolve => setTimeout(resolve, 500))\n      alert('主题设置保存成功！')\n    } catch (error) {\n      console.error('Error saving theme:', error)\n      alert('保存主题设置时出错，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleThemeChange = (theme: string) => {\n    setSelectedTheme(theme)\n    applyTheme(theme)\n  }\n\n  const handleResetSettings = () => {\n    if (confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {\n      SettingsManager.resetToDefaults()\n      const settings = SettingsManager.getSettings()\n      setBusinessSettings(settings.business)\n      setOperatingHours(settings.operatingHours)\n      setHolidaySettings(settings.holidays)\n      setNotifications(settings.notifications)\n      setSelectedTheme(settings.theme)\n      applyTheme(settings.theme)\n      alert('设置已重置为默认值')\n    }\n  }\n\n  const handleExportSettings = () => {\n    try {\n      const settingsJson = SettingsManager.exportSettings()\n      const blob = new Blob([settingsJson], { type: 'application/json' })\n      const url = URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = 'barbershop-settings.json'\n      document.body.appendChild(a)\n      a.click()\n      document.body.removeChild(a)\n      URL.revokeObjectURL(url)\n    } catch (error) {\n      alert('导出设置失败')\n    }\n  }\n\n  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      try {\n        const content = e.target?.result as string\n        SettingsManager.importSettings(content)\n        const settings = SettingsManager.getSettings()\n        setBusinessSettings(settings.business)\n        setOperatingHours(settings.operatingHours)\n        setHolidaySettings(settings.holidays)\n        setNotifications(settings.notifications)\n        setSelectedTheme(settings.theme)\n        applyTheme(settings.theme)\n        alert('设置导入成功！')\n      } catch (error) {\n        alert('导入设置失败：文件格式不正确')\n      }\n    }\n    reader.readAsText(file)\n    // Reset input\n    event.target.value = ''\n  }\n\n  const tabs = [\n    { id: 'business', label: '商户信息', icon: Building },\n    { id: 'hours', label: '营业时间', icon: Clock },\n    { id: 'holidays', label: '节假日设置', icon: Calendar },\n    { id: 'notifications', label: '通知设置', icon: Bell },\n    { id: 'appearance', label: '外观设置', icon: Palette },\n    { id: 'security', label: '安全设置', icon: Shield },\n    { id: 'integrations', label: '系统集成', icon: Database }\n  ]\n\n  const renderBusinessSettings = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Building className=\"h-5 w-5 mr-2\" />\n            商户信息\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">商户名称</label>\n              <Input\n                value={businessSettings.name}\n                onChange={(e) => setBusinessSettings({...businessSettings, name: e.target.value})}\n                placeholder=\"皇家理发店\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">联系电话</label>\n              <div className=\"relative\">\n                <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.phone}\n                  onChange={(e) => setBusinessSettings({...businessSettings, phone: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"+86 138-0013-8000\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">邮箱地址</label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.email}\n                  onChange={(e) => setBusinessSettings({...businessSettings, email: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">网站地址</label>\n              <div className=\"relative\">\n                <Globe className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.website}\n                  onChange={(e) => setBusinessSettings({...businessSettings, website: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"www.royalcuts.cn\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">商户地址</label>\n            <div className=\"relative\">\n              <MapPin className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\n              <textarea\n                value={businessSettings.address}\n                onChange={(e) => setBusinessSettings({...businessSettings, address: e.target.value})}\n                className=\"w-full pl-10 pt-2 pb-2 pr-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                rows={2}\n                placeholder=\"北京市朝阳区三里屯街道1号\"\n              />\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">时区设置</label>\n              <Select value={businessSettings.timezone} onValueChange={(value) => setBusinessSettings({...businessSettings, timezone: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"Asia/Shanghai\">北京时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"Asia/Hong_Kong\">香港时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"Asia/Taipei\">台北时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"America/New_York\">美国东部时间 (ET)</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">货币设置</label>\n              <Select value={businessSettings.currency} onValueChange={(value) => setBusinessSettings({...businessSettings, currency: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"CNY\">CNY - 人民币</SelectItem>\n                  <SelectItem value=\"HKD\">HKD - 港币</SelectItem>\n                  <SelectItem value=\"TWD\">TWD - 新台币</SelectItem>\n                  <SelectItem value=\"USD\">USD - 美元</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* 保存按钮 */}\n          <div className=\"flex justify-end pt-4 border-t\">\n            <Button onClick={handleSaveBusinessSettings} disabled={loading}>\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>保存中...</span>\n                </div>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  保存商户信息\n                </>\n              )}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderOperatingHours = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Clock className=\"h-5 w-5 mr-2\" />\n            营业时间\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {Object.entries(operatingHours).map(([day, hours]) => (\n              <div key={day} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"font-medium capitalize w-20\">\n                    {day === 'monday' ? '周一' :\n                     day === 'tuesday' ? '周二' :\n                     day === 'wednesday' ? '周三' :\n                     day === 'thursday' ? '周四' :\n                     day === 'friday' ? '周五' :\n                     day === 'saturday' ? '周六' :\n                     day === 'sunday' ? '周日' : day}\n                  </span>\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={!hours.closed}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, closed: !e.target.checked }\n                      })}\n                      className=\"rounded\"\n                    />\n                    <span className=\"text-sm\">营业</span>\n                  </label>\n                </div>\n                {!hours.closed && (\n                  <div className=\"flex items-center space-x-2\">\n                    <Input\n                      type=\"time\"\n                      value={hours.open}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, open: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                    <span className=\"text-muted-foreground\">至</span>\n                    <Input\n                      type=\"time\"\n                      value={hours.close}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, close: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                  </div>\n                )}\n                {hours.closed && (\n                  <Badge variant=\"secondary\">休息</Badge>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* 保存按钮 */}\n          <div className=\"flex justify-end pt-4 border-t\">\n            <Button onClick={handleSaveOperatingHours} disabled={loading}>\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>保存中...</span>\n                </div>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  保存营业时间\n                </>\n              )}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderNotifications = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Bell className=\"h-5 w-5 mr-2\" />\n            通知偏好设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[\n              { key: 'emailNotifications', label: '邮件通知', description: '通过邮件接收通知' },\n              { key: 'smsNotifications', label: '短信通知', description: '通过短信接收通知' },\n              { key: 'appointmentReminders', label: '预约提醒', description: '向客户发送预约提醒' },\n              { key: 'lowStockAlerts', label: '库存不足提醒', description: '库存不足时接收通知' },\n              { key: 'dailyReports', label: '日报', description: '接收每日业务摘要' },\n              { key: 'weeklyReports', label: '周报', description: '接收每周分析报告' }\n            ].map((setting) => (\n              <div key={setting.key} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div>\n                  <p className=\"font-medium\">{setting.label}</p>\n                  <p className=\"text-sm text-muted-foreground\">{setting.description}</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notifications[setting.key as keyof typeof notifications]}\n                    onChange={(e) => setNotifications({\n                      ...notifications,\n                      [setting.key]: e.target.checked\n                    })}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n            ))}\n          </div>\n\n          {/* 保存按钮 */}\n          <div className=\"flex justify-end pt-4 border-t\">\n            <Button onClick={handleSaveNotifications} disabled={loading}>\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>保存中...</span>\n                </div>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  保存通知设置\n                </>\n              )}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderAppearance = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Palette className=\"h-5 w-5 mr-2\" />\n            外观设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-6\">\n            {/* 当前主题预览 */}\n            <div className=\"p-4 border rounded-lg bg-muted/30\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <div>\n                  <p className=\"font-medium\">当前主题</p>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {getThemeConfig(selectedTheme)?.name || '未知主题'}\n                  </p>\n                </div>\n                <div className={`h-12 w-24 bg-gradient-to-r ${getThemeConfig(selectedTheme)?.preview.gradient} rounded-lg`}></div>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">\n                {getThemeConfig(selectedTheme)?.description}\n              </p>\n            </div>\n\n            {/* 主题选择 */}\n            <div>\n              <p className=\"font-medium mb-4\">选择主题配色</p>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {AVAILABLE_THEMES.map((theme) => (\n                  <div\n                    key={theme.id}\n                    className={`p-4 border rounded-lg cursor-pointer hover:bg-accent transition-all ${\n                      selectedTheme === theme.id ? 'ring-2 ring-primary bg-accent' : ''\n                    }`}\n                    onClick={() => handleThemeChange(theme.id)}\n                  >\n                    <div className={`h-12 bg-gradient-to-r ${theme.preview.gradient} rounded-lg mb-3`}></div>\n                    <div className=\"space-y-1\">\n                      <div className=\"flex items-center justify-between\">\n                        <p className=\"font-medium text-sm\">{theme.name}</p>\n                        {selectedTheme === theme.id && (\n                          <Badge variant=\"default\" className=\"text-xs\">当前</Badge>\n                        )}\n                      </div>\n                      <p className=\"text-xs text-muted-foreground line-clamp-2\">\n                        {theme.description}\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* 主题详情 */}\n            {selectedTheme && (\n              <div className=\"p-4 border rounded-lg\">\n                <p className=\"font-medium mb-3\">主题详情</p>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <p className=\"text-muted-foreground\">主题名称</p>\n                    <p className=\"font-medium\">{getThemeConfig(selectedTheme)?.name}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-muted-foreground\">主题ID</p>\n                    <p className=\"font-mono text-xs\">{selectedTheme}</p>\n                  </div>\n                  <div className=\"col-span-2\">\n                    <p className=\"text-muted-foreground\">描述</p>\n                    <p>{getThemeConfig(selectedTheme)?.description}</p>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* 保存按钮 */}\n          <div className=\"flex justify-end pt-4 border-t\">\n            <Button onClick={handleSaveTheme} disabled={loading}>\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                  <span>保存中...</span>\n                </div>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4 mr-2\" />\n                  保存主题设置\n                </>\n              )}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderSecurity = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Shield className=\"h-5 w-5 mr-2\" />\n            安全设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">双重身份验证</p>\n                <p className=\"text-sm text-muted-foreground\">为您的账户添加额外的安全保护</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Smartphone className=\"h-4 w-4 mr-2\" />\n                启用双重验证\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">修改密码</p>\n                <p className=\"text-sm text-muted-foreground\">更新您的账户密码</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Key className=\"h-4 w-4 mr-2\" />\n                修改密码\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">活跃会话</p>\n                <p className=\"text-sm text-muted-foreground\">管理您的活跃登录会话</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                查看会话\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderIntegrations = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Database className=\"h-5 w-5 mr-2\" />\n            系统集成\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center\">\n                  <Database className=\"h-5 w-5 text-green-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">Supabase 数据库</p>\n                  <p className=\"text-sm text-muted-foreground\">已连接并同步</p>\n                </div>\n              </div>\n              <Badge variant=\"success\">已连接</Badge>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                  <Mail className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">邮件服务</p>\n                  <p className=\"text-sm text-muted-foreground\">发送自动化邮件</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">连接</Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n                  <Smartphone className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">短信服务</p>\n                  <p className=\"text-sm text-muted-foreground\">发送短信通知</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">连接</Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderHolidays = () => (\n    <HolidayManager\n      holidaySettings={holidaySettings}\n      onUpdate={(newSettings) => {\n        setHolidaySettings(newSettings)\n        // 自动保存节假日设置\n        SettingsManager.updateHolidaySettings(newSettings)\n      }}\n    />\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'business': return renderBusinessSettings()\n      case 'hours': return renderOperatingHours()\n      case 'holidays': return renderHolidays()\n      case 'notifications': return renderNotifications()\n      case 'appearance': return renderAppearance()\n      case 'security': return renderSecurity()\n      case 'integrations': return renderIntegrations()\n      default: return renderBusinessSettings()\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n        <PageHeader\n          title=\"系统设置\"\n          description=\"配置理发店管理系统\"\n          icon={<Settings className=\"h-6 w-6 text-white\" />}\n        />\n\n        {/* Error Display */}\n        {errors.length > 0 && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n            <div className=\"flex items-start space-x-2\">\n              <div className=\"h-4 w-4 text-red-500 mt-0.5\">⚠️</div>\n              <div>\n                <h4 className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n                  设置验证错误\n                </h4>\n                <ul className=\"text-sm text-red-600 dark:text-red-400 mt-1\">\n                  {errors.map((error, index) => (\n                    <li key={index}>• {error}</li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-col lg:flex-row gap-6\">\n          {/* Settings Navigation */}\n          <div className=\"lg:w-64\">\n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-4\">\n                <nav className=\"space-y-1\">\n                  {tabs.map((tab) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                        activeTab === tab.id\n                          ? 'bg-primary text-primary-foreground'\n                          : 'hover:bg-accent hover:text-accent-foreground'\n                      }`}\n                    >\n                      <tab.icon className=\"h-4 w-4\" />\n                      <span className=\"text-sm font-medium\">{tab.label}</span>\n                    </button>\n                  ))}\n                </nav>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Settings Content */}\n          <div className=\"flex-1\">\n            {renderTabContent()}\n            \n            {/* Global Action Buttons */}\n            <div className=\"mt-6 flex flex-col sm:flex-row gap-3 justify-center\">\n              <Button variant=\"outline\" onClick={handleExportSettings}>\n                <Download className=\"h-4 w-4 mr-2\" />\n                导出所有设置\n              </Button>\n              <div className=\"relative\">\n                <input\n                  type=\"file\"\n                  accept=\".json\"\n                  onChange={handleImportSettings}\n                  className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n                />\n                <Button variant=\"outline\">\n                  <Upload className=\"h-4 w-4 mr-2\" />\n                  导入设置\n                </Button>\n              </div>\n              <Button variant=\"outline\" onClick={handleResetSettings}>\n                <RotateCcw className=\"h-4 w-4 mr-2\" />\n                重置所有设置\n              </Button>\n            </div>\n          </div>\n        </div>\n      </MainLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AACA;AAYA;AApDA;;;;;;;;;;;;;;;AAsDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjD,iBAAiB;IACjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,SAAS;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACxD,WAAW;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QAC1D,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;IACzD;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QACvE,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACtE,mBAAmB;QACnB,UAAU,EAAE;IACd;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,sHAAA,CAAA,kBAAe,CAAC,WAAW;QAC5C,oBAAoB,SAAS,QAAQ;QACrC,kBAAkB,SAAS,cAAc;QACzC,mBAAmB,SAAS,QAAQ;QACpC,iBAAiB,SAAS,aAAa;QACvC,iBAAiB,SAAS,KAAK;IACjC,GAAG,EAAE;IAEL,cAAc;IACd,MAAM,6BAA6B;QACjC,WAAW;QACX,UAAU,EAAE;QAEZ,IAAI;YACF,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,UAAU;gBACV,WAAW;gBACX;YACF;YAEA,sHAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC;YACvC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B;QAC/B,WAAW;QACX,UAAU,EAAE;QAEZ,IAAI;YACF,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE;YAC3C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,UAAU;gBACV,WAAW;gBACX;YACF;YAEA,sHAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC;YACrC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,4BAA4B;QAChC,WAAW;QACX,IAAI;YACF,sHAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC;YACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B;QAC9B,WAAW;QACX,IAAI;YACF,sHAAA,CAAA,kBAAe,CAAC,0BAA0B,CAAC;YAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,sHAAA,CAAA,kBAAe,CAAC,WAAW,CAAC;YAC5B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;IACb;IAEA,MAAM,sBAAsB;QAC1B,IAAI,QAAQ,4BAA4B;YACtC,sHAAA,CAAA,kBAAe,CAAC,eAAe;YAC/B,MAAM,WAAW,sHAAA,CAAA,kBAAe,CAAC,WAAW;YAC5C,oBAAoB,SAAS,QAAQ;YACrC,kBAAkB,SAAS,cAAc;YACzC,mBAAmB,SAAS,QAAQ;YACpC,iBAAiB,SAAS,aAAa;YACvC,iBAAiB,SAAS,KAAK;YAC/B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK;YACzB,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,eAAe,sHAAA,CAAA,kBAAe,CAAC,cAAc;YACnD,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAa,EAAE;gBAAE,MAAM;YAAmB;YACjE,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,UAAU,EAAE,MAAM,EAAE;gBAC1B,sHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;gBAC/B,MAAM,WAAW,sHAAA,CAAA,kBAAe,CAAC,WAAW;gBAC5C,oBAAoB,SAAS,QAAQ;gBACrC,kBAAkB,SAAS,cAAc;gBACzC,mBAAmB,SAAS,QAAQ;gBACpC,iBAAiB,SAAS,aAAa;gBACvC,iBAAiB,SAAS,KAAK;gBAC/B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK;gBACzB,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QACA,OAAO,UAAU,CAAC;QAClB,cAAc;QACd,MAAM,MAAM,CAAC,KAAK,GAAG;IACvB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAQ,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAChD;YAAE,IAAI;YAAS,OAAO;YAAQ,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC1C;YAAE,IAAI;YAAY,OAAO;YAAS,MAAM,0MAAA,CAAA,WAAQ;QAAC;QACjD;YAAE,IAAI;YAAiB,OAAO;YAAQ,MAAM,kMAAA,CAAA,OAAI;QAAC;QACjD;YAAE,IAAI;YAAc,OAAO;YAAQ,MAAM,wMAAA,CAAA,UAAO;QAAC;QACjD;YAAE,IAAI;YAAY,OAAO;YAAQ,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC9C;YAAE,IAAI;YAAgB,OAAO;YAAQ,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACrD;IAED,MAAM,yBAAyB,kBAC7B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,iBAAiB,IAAI;gDAC5B,UAAU,CAAC,IAAM,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC/E,aAAY;;;;;;;;;;;;kDAGhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAIlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,OAAO;wDAC/B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAClF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,OAAO,iBAAiB,OAAO;gDAC/B,UAAU,CAAC,IAAM,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAClF,WAAU;gDACV,MAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;0EAClC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAiB;;;;;;0EACnC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAc;;;;;;0EAChC,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;kDAI3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAA4B,UAAU;8CACpD,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;6DAGR;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWjD,MAAM,uBAAuB,kBAC3B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAItC,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC/C,8OAAC;wCAAc,WAAU;;0DACvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,QAAQ,WAAW,OACnB,QAAQ,YAAY,OACpB,QAAQ,cAAc,OACtB,QAAQ,aAAa,OACrB,QAAQ,WAAW,OACnB,QAAQ,aAAa,OACrB,QAAQ,WAAW,OAAO;;;;;;kEAE7B,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,SAAS,CAAC,MAAM,MAAM;gEACtB,UAAU,CAAC,IAAM,kBAAkB;wEACjC,GAAG,cAAc;wEACjB,CAAC,IAAI,EAAE;4EAAE,GAAG,KAAK;4EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,OAAO;wEAAC;oEAC/C;gEACA,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;4CAG7B,CAAC,MAAM,MAAM,kBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,MAAM,IAAI;wDACjB,UAAU,CAAC,IAAM,kBAAkB;gEACjC,GAAG,cAAc;gEACjB,CAAC,IAAI,EAAE;oEAAE,GAAG,KAAK;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC1C;wDACA,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,MAAM,KAAK;wDAClB,UAAU,CAAC,IAAM,kBAAkB;gEACjC,GAAG,cAAc;gEACjB,CAAC,IAAI,EAAE;oEAAE,GAAG,KAAK;oEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC3C;wDACA,WAAU;;;;;;;;;;;;4CAIf,MAAM,MAAM,kBACX,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;;uCAhDrB;;;;;;;;;;0CAuDd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAA0B,UAAU;8CAClD,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;6DAGR;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWjD,MAAM,sBAAsB,kBAC1B,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrC,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,KAAK;wCAAsB,OAAO;wCAAQ,aAAa;oCAAW;oCACpE;wCAAE,KAAK;wCAAoB,OAAO;wCAAQ,aAAa;oCAAW;oCAClE;wCAAE,KAAK;wCAAwB,OAAO;wCAAQ,aAAa;oCAAY;oCACvE;wCAAE,KAAK;wCAAkB,OAAO;wCAAU,aAAa;oCAAY;oCACnE;wCAAE,KAAK;wCAAgB,OAAO;wCAAM,aAAa;oCAAW;oCAC5D;wCAAE,KAAK;wCAAiB,OAAO;wCAAM,aAAa;oCAAW;iCAC9D,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC;wCAAsB,WAAU;;0DAC/B,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAe,QAAQ,KAAK;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAiC,QAAQ,WAAW;;;;;;;;;;;;0DAEnE,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,aAAa,CAAC,QAAQ,GAAG,CAA+B;wDACjE,UAAU,CAAC,IAAM,iBAAiB;gEAChC,GAAG,aAAa;gEAChB,CAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;4DACjC;wDACA,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAfT,QAAQ,GAAG;;;;;;;;;;0CAsBzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAyB,UAAU;8CACjD,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;6DAGR;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWjD,MAAM,mBAAmB,kBACvB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIxC,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,QAAQ;;;;;;;;;;;;kEAG5C,8OAAC;wDAAI,WAAW,CAAC,2BAA2B,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,QAAQ,SAAS,WAAW,CAAC;;;;;;;;;;;;0DAE5G,8OAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;;;;;;;;;;;;kDAKpC,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,8OAAC;gDAAI,WAAU;0DACZ,sHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,sBACrB,8OAAC;wDAEC,WAAW,CAAC,oEAAoE,EAC9E,kBAAkB,MAAM,EAAE,GAAG,kCAAkC,IAC/D;wDACF,SAAS,IAAM,kBAAkB,MAAM,EAAE;;0EAEzC,8OAAC;gEAAI,WAAW,CAAC,sBAAsB,EAAE,MAAM,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;;;;;;0EACjF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAuB,MAAM,IAAI;;;;;;4EAC7C,kBAAkB,MAAM,EAAE,kBACzB,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAAU;;;;;;;;;;;;kFAGjD,8OAAC;wEAAE,WAAU;kFACV,MAAM,WAAW;;;;;;;;;;;;;uDAfjB,MAAM,EAAE;;;;;;;;;;;;;;;;oCAwBpB,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAmB;;;;;;0DAChC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;;;;;;;;;;;;kEAE7D,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;;;;;;;kEAEpC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;0EAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAiB,UAAU;8CACzC,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;6DAGR;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWjD,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIvC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,8MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUhD,MAAM,qBAAqB,kBACzB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;0CAI7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;0CAIxC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQhD,MAAM,iBAAiB,kBACrB,8OAAC,oJAAA,CAAA,iBAAc;YACb,iBAAiB;YACjB,UAAU,CAAC;gBACT,mBAAmB;gBACnB,YAAY;gBACZ,sHAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC;YACxC;;;;;;IAIJ,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC,gJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;;8BACT,8OAAC,8IAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;oBACZ,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;gBAI3B,OAAO,MAAM,GAAG,mBACf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC;wCAAG,WAAU;kDACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;;oDAAe;oDAAG;;+CAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;gDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,uCACA,gDACJ;;kEAEF,8OAAC,IAAI,IAAI;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAuB,IAAI,KAAK;;;;;;;+CAT3C,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBvB,8OAAC;4BAAI,WAAU;;gCACZ;8CAGD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;8DAEZ,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAIvC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD", "debugId": null}}]}