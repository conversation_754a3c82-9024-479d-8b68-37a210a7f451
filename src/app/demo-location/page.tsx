'use client'

import { useState } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader } from '@/components/layout/page-header'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AddressInput } from '@/components/ui/address-input'
import { Button } from '@/components/ui/button'
import { MapPin, Navigation, Info } from 'lucide-react'
import { GeolocationService, type GeolocationResult } from '@/lib/geolocation'

export default function DemoLocationPage() {
  const [address, setAddress] = useState('')
  const [locationResult, setLocationResult] = useState<GeolocationResult | null>(null)
  const [isTestingLocation, setIsTestingLocation] = useState(false)

  const handleTestLocation = async () => {
    setIsTestingLocation(true)
    try {
      const result = await GeolocationService.getCurrentLocationAddress()
      setLocationResult(result)
      setAddress(result.address.formatted_address)
    } catch (error) {
      console.error('定位测试失败:', error)
    } finally {
      setIsTestingLocation(false)
    }
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="地理定位演示"
          description="测试商户地址定位功能"
          icon={<Navigation className="h-6 w-6 text-white" />}
        />

        <div className="space-y-6">
          {/* 功能介绍 */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Info className="h-5 w-5 mr-2" />
                功能说明
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <p>
                  <strong>地理定位功能</strong>可以帮助您快速获取当前位置作为商户地址，无需手动输入。
                </p>
                <div className="bg-blue-50 dark:bg-blue-950/30 p-3 rounded-lg">
                  <p className="font-medium text-blue-700 dark:text-blue-300 mb-2">使用步骤：</p>
                  <ol className="list-decimal list-inside space-y-1 text-blue-600 dark:text-blue-400">
                    <li>点击地址输入框旁边的"获取位置"按钮</li>
                    <li>浏览器会请求定位权限，请点击"允许"</li>
                    <li>系统会自动获取您的当前位置并转换为地址</li>
                    <li>获取到的地址会自动填入输入框</li>
                  </ol>
                </div>
                <div className="bg-amber-50 dark:bg-amber-950/30 p-3 rounded-lg">
                  <p className="font-medium text-amber-700 dark:text-amber-300 mb-2">注意事项：</p>
                  <ul className="list-disc list-inside space-y-1 text-amber-600 dark:text-amber-400">
                    <li>首次使用需要授权浏览器定位权限</li>
                    <li>定位精度取决于设备GPS和网络状况</li>
                    <li>在室内或信号较弱的地方可能影响定位精度</li>
                    <li>如果定位失败，可以手动输入地址</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 地址输入演示 */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                地址输入演示
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">商户地址</label>
                <AddressInput
                  value={address}
                  onChange={setAddress}
                  placeholder="请输入地址或点击获取位置"
                />
              </div>

              <div className="flex space-x-2">
                <Button
                  onClick={handleTestLocation}
                  disabled={isTestingLocation}
                  variant="outline"
                >
                  {isTestingLocation ? (
                    <>
                      <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                      测试定位中...
                    </>
                  ) : (
                    <>
                      <Navigation className="h-4 w-4 mr-2" />
                      测试定位功能
                    </>
                  )}
                </Button>
                <Button
                  onClick={() => {
                    setAddress('')
                    setLocationResult(null)
                  }}
                  variant="outline"
                >
                  清空地址
                </Button>
              </div>

              {/* 当前地址显示 */}
              {address && (
                <div className="p-3 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg">
                  <p className="text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                    当前地址：
                  </p>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    {address}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 定位结果详情 */}
          {locationResult && (
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle>定位结果详情</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">坐标信息</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">纬度：</span>
                        <span>{locationResult.coordinates.latitude.toFixed(6)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">经度：</span>
                        <span>{locationResult.coordinates.longitude.toFixed(6)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">精度：</span>
                        <span>{Math.round(locationResult.coordinates.accuracy)}米</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-medium">地址信息</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">国家：</span>
                        <span>{locationResult.address.country}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">省份：</span>
                        <span>{locationResult.address.province}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">城市：</span>
                        <span>{locationResult.address.city}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">区域：</span>
                        <span>{locationResult.address.district}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">街道：</span>
                        <span>{locationResult.address.street}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 浏览器支持检查 */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle>浏览器兼容性</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <span>地理定位API支持</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    GeolocationService.isSupported() 
                      ? 'bg-green-100 text-green-700 dark:bg-green-950/30 dark:text-green-300'
                      : 'bg-red-100 text-red-700 dark:bg-red-950/30 dark:text-red-300'
                  }`}>
                    {GeolocationService.isSupported() ? '支持' : '不支持'}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">
                  现代浏览器（Chrome、Firefox、Safari、Edge）都支持地理定位功能
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
