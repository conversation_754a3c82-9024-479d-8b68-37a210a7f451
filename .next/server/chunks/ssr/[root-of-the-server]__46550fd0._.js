module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createSupabaseClient": (()=>createSupabaseClient),
    "db": (()=>db),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-ssr] (ecmascript) <module evaluation>");
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "your_supabase_project_url") || 'https://demo.supabase.co';
const supabaseAnonKey = ("TURBOPACK compile-time value", "your_supabase_anon_key") || 'demo-key';
// Only create Supabase client if proper credentials are provided
const isSupabaseConfigured = supabaseUrl && supabaseAnonKey && supabaseUrl !== 'your_supabase_project_url' && supabaseUrl !== 'https://demo.supabase.co' && supabaseAnonKey !== 'your_supabase_anon_key' && supabaseAnonKey !== 'demo-key' && supabaseUrl.startsWith('https://') && supabaseUrl.includes('.supabase.co');
const supabase = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
function createSupabaseClient() {
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
}
// Database helper functions
const createDbHelpers = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        // Return mock functions when Supabase is not configured
        return {
            customers: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    }),
                search: (query)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    })
            },
            staff: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getActive: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            services: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getActive: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            appointments: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getByDate: (date)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getByStaff: (staffId, date)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            inventory: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getLowStock: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            transactions: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getByDateRange: (startDate, endDate)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    })
            }
        };
    }
    "TURBOPACK unreachable";
};
const db = createDbHelpers();
}}),
"[project]/src/contexts/auth-context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "useIsAdmin": (()=>useIsAdmin),
    "useUserRole": (()=>useUserRole)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [session, setSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Check if Supabase is configured
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"]) {
            // For demo purposes, create a mock user when Supabase is not configured
            const mockUser = {
                id: 'demo-user',
                email: '<EMAIL>',
                user_metadata: {
                    role: 'admin',
                    full_name: 'Demo Admin'
                },
                app_metadata: {
                    role: 'admin'
                }
            };
            setUser(mockUser);
            setSession({
                user: mockUser
            });
            setLoading(false);
            return;
        }
        // Get initial session
        const getInitialSession = async ()=>{
            const { data: { session }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
            if (error) {
                console.error('Error getting session:', error);
            } else {
                setSession(session);
                setUser(session?.user ?? null);
            }
            setLoading(false);
        };
        getInitialSession();
        // Listen for auth changes
        const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange(async (event, session)=>{
            console.log('Auth state changed:', event, session);
            setSession(session);
            setUser(session?.user ?? null);
            setLoading(false);
        });
        return ()=>subscription.unsubscribe();
    }, []);
    const signIn = async (email, password)=>{
        try {
            setLoading(true);
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"]) {
                // Demo mode - accept any credentials
                const mockUser = {
                    id: 'demo-user',
                    email: email,
                    user_metadata: {
                        role: 'admin',
                        full_name: 'Demo Admin'
                    },
                    app_metadata: {
                        role: 'admin'
                    }
                };
                setUser(mockUser);
                setSession({
                    user: mockUser
                });
                return {
                    error: null
                };
            }
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
                email,
                password
            });
            if (error) {
                console.error('Sign in error:', error);
                return {
                    error
                };
            }
            return {
                error: null
            };
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                error
            };
        } finally{
            setLoading(false);
        }
    };
    const signUp = async (email, password, userData)=>{
        try {
            setLoading(true);
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signUp({
                email,
                password,
                options: {
                    data: userData
                }
            });
            if (error) {
                console.error('Sign up error:', error);
                return {
                    error
                };
            }
            return {
                error: null
            };
        } catch (error) {
            console.error('Sign up error:', error);
            return {
                error
            };
        } finally{
            setLoading(false);
        }
    };
    const signOut = async ()=>{
        try {
            setLoading(true);
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"]) {
                // Demo mode - just clear the user
                setUser(null);
                setSession(null);
                return;
            }
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
            if (error) {
                console.error('Sign out error:', error);
            }
        } catch (error) {
            console.error('Sign out error:', error);
        } finally{
            setLoading(false);
        }
    };
    const resetPassword = async (email)=>{
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.resetPasswordForEmail(email, {
                redirectTo: `${window.location.origin}/auth/reset-password`
            });
            if (error) {
                console.error('Reset password error:', error);
                return {
                    error
                };
            }
            return {
                error: null
            };
        } catch (error) {
            console.error('Reset password error:', error);
            return {
                error
            };
        }
    };
    const value = {
        user,
        session,
        loading,
        signIn,
        signUp,
        signOut,
        resetPassword
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/auth-context.tsx",
        lineNumber: 181,
        columnNumber: 5
    }, this);
}
function useAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
function useIsAdmin() {
    const { user } = useAuth();
    // Check if user has admin role in metadata or email domain
    const isAdmin = user?.user_metadata?.role === 'admin' || user?.email?.endsWith('@royalcuts.com') || user?.app_metadata?.role === 'admin';
    return isAdmin;
}
function useUserRole() {
    const { user } = useAuth();
    return user?.user_metadata?.role || user?.app_metadata?.role || 'user';
}
}}),
"[project]/src/lib/settings.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Settings management for local storage
__turbopack_context__.s({
    "AVAILABLE_THEMES": (()=>AVAILABLE_THEMES),
    "SettingsManager": (()=>SettingsManager),
    "applyTheme": (()=>applyTheme),
    "formatHolidayDate": (()=>formatHolidayDate),
    "formatOperatingHours": (()=>formatOperatingHours),
    "generateHolidayId": (()=>generateHolidayId),
    "getEffectiveHours": (()=>getEffectiveHours),
    "getHolidayTypeLabel": (()=>getHolidayTypeLabel),
    "getThemeConfig": (()=>getThemeConfig),
    "getUpcomingHolidays": (()=>getUpcomingHolidays),
    "initializeTheme": (()=>initializeTheme),
    "isHolidayToday": (()=>isHolidayToday),
    "resetTheme": (()=>resetTheme),
    "validateBusinessSettings": (()=>validateBusinessSettings),
    "validateHoliday": (()=>validateHoliday),
    "validateOperatingHours": (()=>validateOperatingHours)
});
const DEFAULT_SETTINGS = {
    business: {
        name: '皇家理发店',
        address: '北京市朝阳区三里屯街道1号',
        phone: '+86 138-0013-8000',
        email: '<EMAIL>',
        website: 'www.royalcuts.cn',
        timezone: 'Asia/Shanghai',
        currency: 'CNY'
    },
    operatingHours: {
        monday: {
            open: '09:00',
            close: '18:00',
            closed: false
        },
        tuesday: {
            open: '09:00',
            close: '18:00',
            closed: false
        },
        wednesday: {
            open: '09:00',
            close: '18:00',
            closed: false
        },
        thursday: {
            open: '09:00',
            close: '19:00',
            closed: false
        },
        friday: {
            open: '09:00',
            close: '19:00',
            closed: false
        },
        saturday: {
            open: '08:00',
            close: '17:00',
            closed: false
        },
        sunday: {
            open: '10:00',
            close: '16:00',
            closed: false
        }
    },
    holidays: {
        enableHolidayMode: true,
        holidays: [
            {
                id: 'new-year',
                name: '元旦',
                date: '2024-01-01',
                type: 'fixed',
                recurring: true,
                closed: true,
                description: '新年第一天',
                source: 'manual'
            },
            {
                id: 'spring-festival',
                name: '春节',
                date: '2024-02-10',
                type: 'lunar',
                recurring: true,
                closed: true,
                description: '农历新年',
                source: 'manual'
            },
            {
                id: 'labor-day',
                name: '劳动节',
                date: '2024-05-01',
                type: 'fixed',
                recurring: true,
                closed: true,
                description: '国际劳动节',
                source: 'manual'
            },
            {
                id: 'national-day',
                name: '国庆节',
                date: '2024-10-01',
                type: 'fixed',
                recurring: true,
                closed: true,
                description: '中华人民共和国国庆节',
                source: 'manual'
            }
        ]
    },
    notifications: {
        emailNotifications: true,
        smsNotifications: false,
        appointmentReminders: true,
        lowStockAlerts: true,
        dailyReports: false,
        weeklyReports: true
    },
    theme: 'royal-gold',
    language: 'zh-CN'
};
const SETTINGS_KEY = 'barbershop_settings';
class SettingsManager {
    static getSettings() {
        if ("TURBOPACK compile-time truthy", 1) {
            return DEFAULT_SETTINGS;
        }
        "TURBOPACK unreachable";
    }
    static saveSettings(settings) {
        if ("TURBOPACK compile-time truthy", 1) {
            return;
        }
        "TURBOPACK unreachable";
    }
    static updateBusinessSettings(business) {
        const settings = this.getSettings();
        settings.business = business;
        this.saveSettings(settings);
    }
    static updateOperatingHours(operatingHours) {
        const settings = this.getSettings();
        settings.operatingHours = operatingHours;
        this.saveSettings(settings);
    }
    static updateHolidaySettings(holidays) {
        const settings = this.getSettings();
        settings.holidays = holidays;
        this.saveSettings(settings);
    }
    static updateNotificationSettings(notifications) {
        const settings = this.getSettings();
        settings.notifications = notifications;
        this.saveSettings(settings);
    }
    static updateTheme(theme) {
        const settings = this.getSettings();
        settings.theme = theme;
        this.saveSettings(settings);
    }
    static resetToDefaults() {
        this.saveSettings(DEFAULT_SETTINGS);
    }
    static exportSettings() {
        const settings = this.getSettings();
        return JSON.stringify(settings, null, 2);
    }
    static importSettings(settingsJson) {
        try {
            const imported = JSON.parse(settingsJson);
            // Validate the structure
            if (imported.business && imported.operatingHours && imported.notifications) {
                // Ensure holidays exist, use default if not
                if (!imported.holidays) {
                    imported.holidays = DEFAULT_SETTINGS.holidays;
                }
                this.saveSettings(imported);
            } else {
                throw new Error('Invalid settings format');
            }
        } catch (error) {
            console.error('Error importing settings:', error);
            throw new Error('无效的设置格式');
        }
    }
}
function formatOperatingHours(hours) {
    const days = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday'
    ];
    const dayNames = [
        '周一',
        '周二',
        '周三',
        '周四',
        '周五',
        '周六',
        '周日'
    ];
    return days.map((day, index)=>{
        const dayHours = hours[day];
        if (dayHours.closed) {
            return `${dayNames[index]}: 休息`;
        }
        return `${dayNames[index]}: ${dayHours.open} - ${dayHours.close}`;
    }).join('\n');
}
function validateBusinessSettings(business) {
    const errors = [];
    if (!business.name.trim()) {
        errors.push('商户名称不能为空');
    }
    if (!business.phone.trim()) {
        errors.push('联系电话不能为空');
    }
    if (!business.email.trim()) {
        errors.push('邮箱地址不能为空');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(business.email)) {
        errors.push('邮箱地址格式不正确');
    }
    if (!business.address.trim()) {
        errors.push('商户地址不能为空');
    }
    return errors;
}
function validateOperatingHours(hours) {
    const errors = [];
    const days = [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday'
    ];
    const dayNames = [
        '周一',
        '周二',
        '周三',
        '周四',
        '周五',
        '周六',
        '周日'
    ];
    days.forEach((day, index)=>{
        const dayHours = hours[day];
        if (!dayHours.closed) {
            if (!dayHours.open || !dayHours.close) {
                errors.push(`${dayNames[index]}的营业时间不完整`);
            } else if (dayHours.open >= dayHours.close) {
                errors.push(`${dayNames[index]}的开始时间必须早于结束时间`);
            }
        }
    });
    return errors;
}
const AVAILABLE_THEMES = [
    {
        id: 'royal-gold',
        name: '皇家金色',
        description: '经典奢华的金色主题，彰显专业品质',
        colors: {
            primary: '45 93% 47%',
            primaryForeground: '0 0% 98%',
            secondary: '45 84% 60%',
            accent: '45 84% 60%',
            background: '0 0% 100%',
            foreground: '0 0% 3.9%',
            muted: '0 0% 96.1%',
            border: '0 0% 89.8%'
        },
        preview: {
            gradient: 'from-amber-600 to-amber-800',
            textColor: 'text-amber-700'
        }
    },
    {
        id: 'ocean-blue',
        name: '海洋蓝',
        description: '清新的蓝色主题，营造宁静专业氛围',
        colors: {
            primary: '217 91% 60%',
            primaryForeground: '0 0% 98%',
            secondary: '217 84% 70%',
            accent: '217 84% 70%',
            background: '0 0% 100%',
            foreground: '0 0% 3.9%',
            muted: '0 0% 96.1%',
            border: '0 0% 89.8%'
        },
        preview: {
            gradient: 'from-blue-600 to-blue-800',
            textColor: 'text-blue-700'
        }
    },
    {
        id: 'forest-green',
        name: '森林绿',
        description: '自然的绿色主题，传达健康活力理念',
        colors: {
            primary: '142 76% 36%',
            primaryForeground: '0 0% 98%',
            secondary: '142 70% 45%',
            accent: '142 70% 45%',
            background: '0 0% 100%',
            foreground: '0 0% 3.9%',
            muted: '0 0% 96.1%',
            border: '0 0% 89.8%'
        },
        preview: {
            gradient: 'from-emerald-600 to-emerald-800',
            textColor: 'text-emerald-700'
        }
    },
    {
        id: 'sunset-orange',
        name: '日落橙',
        description: '温暖的橙色主题，营造友好亲切氛围',
        colors: {
            primary: '24 95% 53%',
            primaryForeground: '0 0% 98%',
            secondary: '24 90% 60%',
            accent: '24 90% 60%',
            background: '0 0% 100%',
            foreground: '0 0% 3.9%',
            muted: '0 0% 96.1%',
            border: '0 0% 89.8%'
        },
        preview: {
            gradient: 'from-orange-600 to-red-600',
            textColor: 'text-orange-700'
        }
    },
    {
        id: 'purple-luxury',
        name: '紫色奢华',
        description: '高贵的紫色主题，展现独特品味',
        colors: {
            primary: '262 83% 58%',
            primaryForeground: '0 0% 98%',
            secondary: '262 75% 65%',
            accent: '262 75% 65%',
            background: '0 0% 100%',
            foreground: '0 0% 3.9%',
            muted: '0 0% 96.1%',
            border: '0 0% 89.8%'
        },
        preview: {
            gradient: 'from-purple-600 to-purple-800',
            textColor: 'text-purple-700'
        }
    },
    {
        id: 'rose-elegant',
        name: '玫瑰雅致',
        description: '优雅的玫瑰色主题，适合高端美容院',
        colors: {
            primary: '330 81% 60%',
            primaryForeground: '0 0% 98%',
            secondary: '330 75% 70%',
            accent: '330 75% 70%',
            background: '0 0% 100%',
            foreground: '0 0% 3.9%',
            muted: '0 0% 96.1%',
            border: '0 0% 89.8%'
        },
        preview: {
            gradient: 'from-rose-600 to-pink-700',
            textColor: 'text-rose-700'
        }
    }
];
function applyTheme(themeId) {
    if (typeof document === 'undefined') return;
    const theme = AVAILABLE_THEMES.find((t)=>t.id === themeId);
    if (!theme) return;
    const root = document.documentElement;
    // 应用主题颜色
    Object.entries(theme.colors).forEach(([key, value])=>{
        const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
        root.style.setProperty(`--${cssVar}`, value);
    });
    // 确保所有必要的CSS变量都被设置
    const essentialVars = {
        '--background': theme.colors.background || '0 0% 100%',
        '--foreground': theme.colors.foreground || '0 0% 3.9%',
        '--muted': theme.colors.muted || '0 0% 96.1%',
        '--muted-foreground': '0 0% 45.1%',
        '--popover': theme.colors.background || '0 0% 100%',
        '--popover-foreground': theme.colors.foreground || '0 0% 3.9%',
        '--card': theme.colors.background || '0 0% 100%',
        '--card-foreground': theme.colors.foreground || '0 0% 3.9%',
        '--border': theme.colors.border || '0 0% 89.8%',
        '--input': theme.colors.border || '0 0% 89.8%',
        '--ring': theme.colors.primary || '0 0% 3.9%',
        '--radius': '0.5rem'
    };
    Object.entries(essentialVars).forEach(([key, value])=>{
        root.style.setProperty(key, value);
    });
    // 强制重新渲染
    root.style.display = 'none';
    root.offsetHeight // 触发重排
    ;
    root.style.display = '';
}
function getThemeConfig(themeId) {
    return AVAILABLE_THEMES.find((t)=>t.id === themeId);
}
function initializeTheme() {
    if (typeof document === 'undefined') return;
    const settings = SettingsManager.getSettings();
    const theme = settings.theme || 'royal-gold';
    applyTheme(theme);
}
function resetTheme() {
    if (typeof document === 'undefined') return;
    const root = document.documentElement;
    // 重置所有主题相关的CSS变量
    const defaultVars = {
        '--background': '0 0% 100%',
        '--foreground': '0 0% 3.9%',
        '--card': '0 0% 100%',
        '--card-foreground': '0 0% 3.9%',
        '--popover': '0 0% 100%',
        '--popover-foreground': '0 0% 3.9%',
        '--primary': '0 0% 9%',
        '--primary-foreground': '0 0% 98%',
        '--secondary': '0 0% 96.1%',
        '--secondary-foreground': '0 0% 9%',
        '--muted': '0 0% 96.1%',
        '--muted-foreground': '0 0% 45.1%',
        '--accent': '0 0% 96.1%',
        '--accent-foreground': '0 0% 9%',
        '--destructive': '0 84.2% 60.2%',
        '--destructive-foreground': '0 0% 98%',
        '--border': '0 0% 89.8%',
        '--input': '0 0% 89.8%',
        '--ring': '0 0% 3.9%',
        '--radius': '0.5rem'
    };
    Object.entries(defaultVars).forEach(([key, value])=>{
        root.style.setProperty(key, value);
    });
}
function generateHolidayId() {
    return 'holiday-' + Math.random().toString(36).substr(2, 9);
}
function isHolidayToday(holidays) {
    const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD
    ;
    return holidays.find((holiday)=>{
        if (!holiday.recurring) {
            return holiday.date === today;
        }
        // For recurring holidays, check if month and day match
        const holidayDate = new Date(holiday.date);
        const todayDate = new Date(today);
        if (holiday.type === 'fixed') {
            return holidayDate.getMonth() === todayDate.getMonth() && holidayDate.getDate() === todayDate.getDate();
        }
        // For lunar holidays, would need lunar calendar conversion
        // For now, just check exact date
        return holiday.date === today;
    }) || null;
}
function getEffectiveHours(date, operatingHours, holidaySettings) {
    if (!holidaySettings.enableHolidayMode) {
        // If holiday mode is disabled, use regular hours
        const dayOfWeek = new Date(date).toLocaleDateString('en-US', {
            weekday: 'lowercase'
        });
        return operatingHours[dayOfWeek] || null;
    }
    // Check if date is a holiday
    const holiday = holidaySettings.holidays.find((h)=>{
        if (!h.recurring) {
            return h.date === date;
        }
        const holidayDate = new Date(h.date);
        const checkDate = new Date(date);
        if (h.type === 'fixed') {
            return holidayDate.getMonth() === checkDate.getMonth() && holidayDate.getDate() === checkDate.getDate();
        }
        return h.date === date;
    });
    if (holiday) {
        if (holiday.closed) {
            return {
                open: '',
                close: '',
                closed: true
            };
        }
        if (holiday.hours) {
            return holiday.hours;
        }
    }
    // Use regular operating hours
    const dayOfWeek = new Date(date).toLocaleDateString('en-US', {
        weekday: 'lowercase'
    });
    return operatingHours[dayOfWeek] || null;
}
function validateHoliday(holiday) {
    const errors = [];
    if (!holiday.name?.trim()) {
        errors.push('节假日名称不能为空');
    }
    if (!holiday.date) {
        errors.push('请选择日期');
    } else {
        const date = new Date(holiday.date);
        if (isNaN(date.getTime())) {
            errors.push('日期格式不正确');
        }
    }
    if (!holiday.type) {
        errors.push('请选择节假日类型');
    }
    if (!holiday.closed && holiday.hours) {
        if (!holiday.hours.open || !holiday.hours.close) {
            errors.push('请设置营业时间');
        } else if (holiday.hours.open >= holiday.hours.close) {
            errors.push('开始时间必须早于结束时间');
        }
    }
    return errors;
}
function formatHolidayDate(date) {
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function getHolidayTypeLabel(type) {
    switch(type){
        case 'fixed':
            return '固定日期';
        case 'lunar':
            return '农历日期';
        case 'custom':
            return '自定义';
        default:
            return type;
    }
}
function getUpcomingHolidays(holidays, days = 30) {
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    return holidays.filter((holiday)=>{
        const holidayDate = new Date(holiday.date);
        if (!holiday.recurring) {
            return holidayDate >= today && holidayDate <= futureDate;
        }
        // For recurring holidays, check if they occur within the next 'days' period
        if (holiday.type === 'fixed') {
            const thisYear = new Date(today.getFullYear(), holidayDate.getMonth(), holidayDate.getDate());
            const nextYear = new Date(today.getFullYear() + 1, holidayDate.getMonth(), holidayDate.getDate());
            return thisYear >= today && thisYear <= futureDate || nextYear >= today && nextYear <= futureDate;
        }
        return false;
    }).sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime());
}
}}),
"[project]/src/components/theme-initializer.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeInitializer": (()=>ThemeInitializer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$settings$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/settings.ts [app-ssr] (ecmascript)");
'use client';
;
;
function ThemeInitializer() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Initialize theme on client side
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$settings$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initializeTheme"])();
    }, []);
    // This component doesn't render anything
    return null;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__46550fd0._.js.map