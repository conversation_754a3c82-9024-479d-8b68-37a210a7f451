{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'\n\n// Only create Supabase client if proper credentials are provided\nconst isSupabaseConfigured =\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseUrl !== 'https://demo.supabase.co' &&\n  supabaseAnonKey !== 'your_supabase_anon_key' &&\n  supabaseAnonKey !== 'demo-key'\n\nexport const supabase = isSupabaseConfigured\n  ? createClient(supabaseUrl, supabaseAnonKey)\n  : null\n\nexport function createSupabaseClient() {\n  if (!isSupabaseConfigured) return null\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Database helper functions\nconst createDbHelpers = () => {\n  if (!supabase) {\n    // Return mock functions when Supabase is not configured\n    return {\n      customers: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') }),\n        search: (query: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') })\n      },\n      staff: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getActive: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      services: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getActive: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      appointments: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getByDate: (date: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getByStaff: (staffId: string, date?: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      inventory: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getLowStock: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      transactions: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getByDateRange: (startDate: string, endDate: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') })\n      }\n    }\n  }\n\n  return {\n    // Customers\n    customers: {\n      getAll: () => supabase.from('customers').select('*').order('created_at', { ascending: false }),\n      getById: (id: string) => supabase.from('customers').select('*').eq('id', id).single(),\n      create: (data: any) => supabase.from('customers').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('customers').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('customers').delete().eq('id', id),\n      search: (query: string) => supabase\n        .from('customers')\n        .select('*')\n        .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%,phone.ilike.%${query}%`)\n        .order('created_at', { ascending: false })\n    },\n\n    // Staff\n    staff: {\n      getAll: () => supabase.from('staff').select('*').order('created_at', { ascending: false }),\n      getById: (id: string) => supabase.from('staff').select('*').eq('id', id).single(),\n      getActive: () => supabase.from('staff').select('*').eq('is_active', true).order('first_name'),\n      create: (data: any) => supabase.from('staff').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('staff').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('staff').delete().eq('id', id),\n    },\n\n    // Services\n    services: {\n      getAll: () => supabase.from('services').select('*').order('name'),\n      getById: (id: string) => supabase.from('services').select('*').eq('id', id).single(),\n      getActive: () => supabase.from('services').select('*').eq('is_active', true).order('name'),\n      create: (data: any) => supabase.from('services').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('services').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('services').delete().eq('id', id),\n    },\n\n    // Appointments\n    appointments: {\n      getAll: () => supabase\n        .from('appointments')\n        .select(`\n          *,\n          customer:customers(*),\n          staff:staff(*),\n          service:services(*)\n        `)\n        .order('appointment_date', { ascending: false }),\n      getById: (id: string) => supabase\n        .from('appointments')\n        .select(`\n          *,\n          customer:customers(*),\n          staff:staff(*),\n          service:services(*)\n        `)\n        .eq('id', id)\n        .single(),\n      getByDate: (date: string) => supabase\n        .from('appointments')\n        .select(`\n          *,\n          customer:customers(*),\n          staff:staff(*),\n          service:services(*)\n        `)\n        .eq('appointment_date', date)\n        .order('start_time'),\n      getByStaff: (staffId: string, date?: string) => {\n        let query = supabase\n          .from('appointments')\n          .select(`\n            *,\n            customer:customers(*),\n            staff:staff(*),\n            service:services(*)\n          `)\n          .eq('staff_id', staffId)\n\n        if (date) {\n          query = query.eq('appointment_date', date)\n        }\n\n        return query.order('appointment_date', { ascending: false })\n      },\n      create: (data: any) => supabase.from('appointments').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('appointments').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('appointments').delete().eq('id', id),\n    },\n\n    // Inventory\n    inventory: {\n      getAll: () => supabase.from('inventory').select('*').order('name'),\n      getById: (id: string) => supabase.from('inventory').select('*').eq('id', id).single(),\n      getLowStock: () => supabase\n        .from('inventory')\n        .select('*')\n        .filter('current_stock', 'lte', 'min_stock_level')\n        .order('name'),\n      create: (data: any) => supabase.from('inventory').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('inventory').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('inventory').delete().eq('id', id),\n    },\n\n    // Transactions\n    transactions: {\n      getAll: () => supabase.from('transactions').select('*').order('transaction_date', { ascending: false }),\n      getById: (id: string) => supabase.from('transactions').select('*').eq('id', id).single(),\n      getByDateRange: (startDate: string, endDate: string) => supabase\n        .from('transactions')\n        .select('*')\n        .gte('transaction_date', startDate)\n        .lte('transaction_date', endDate)\n        .order('transaction_date', { ascending: false }),\n      create: (data: any) => supabase.from('transactions').insert(data).select().single(),\n    }\n  }\n}\n\nexport const db = createDbHelpers()\n"], "names": [], "mappings": ";;;;;AACA;;;AAEA,MAAM,cAAc,iEAAwC;AAC5D,MAAM,kBAAkB,8DAA6C;AAErE,iEAAiE;AACjE,MAAM,uBACJ,gBAAgB,+BAChB,gBAAgB,8BAChB,oBAAoB,4BACpB,oBAAoB;AAEf,MAAM,WAAW,6EAEpB;AAEG,SAAS;IACd,wCAA2B,OAAO;;AAEpC;AAEA,4BAA4B;AAC5B,MAAM,kBAAkB;IACtB,wCAAe;QACb,wDAAwD;QACxD,OAAO;YACL,WAAW;gBACT,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;gBACtF,QAAQ,CAAC,QAAkB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;YACvG;YACA,OAAO;gBACL,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,WAAW,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC3F,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,UAAU;gBACR,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,WAAW,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC3F,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,cAAc;gBACZ,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,WAAW,CAAC,OAAiB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACvG,YAAY,CAAC,SAAiB,OAAkB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC1H,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,WAAW;gBACT,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,aAAa,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7F,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,cAAc;gBACZ,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,gBAAgB,CAAC,WAAmB,UAAoB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAClI,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;YACnG;QACF;IACF;;AAqHF;AAEO,MAAM,KAAK", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/contexts/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, userData?: any) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  resetPassword: (email: string) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if Supabase is configured\n    if (!supabase) {\n      // For demo purposes, create a mock user when Supa<PERSON> is not configured\n      const mockUser = {\n        id: 'demo-user',\n        email: '<EMAIL>',\n        user_metadata: { role: 'admin', full_name: 'Demo Admin' },\n        app_metadata: { role: 'admin' }\n      } as any\n\n      setUser(mockUser)\n      setSession({ user: mockUser } as any)\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session }, error } = await supabase.auth.getSession()\n      if (error) {\n        console.error('Error getting session:', error)\n      } else {\n        setSession(session)\n        setUser(session?.user ?? null)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session)\n        setSession(session)\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n\n      if (!supabase) {\n        // Demo mode - accept any credentials\n        const mockUser = {\n          id: 'demo-user',\n          email: email,\n          user_metadata: { role: 'admin', full_name: 'Demo Admin' },\n          app_metadata: { role: 'admin' }\n        } as any\n\n        setUser(mockUser)\n        setSession({ user: mockUser } as any)\n        return { error: null }\n      }\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) {\n        console.error('Sign in error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Sign in error:', error)\n      return { error }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string, userData?: any) => {\n    try {\n      setLoading(true)\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: userData,\n        },\n      })\n\n      if (error) {\n        console.error('Sign up error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Sign up error:', error)\n      return { error }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n\n      if (!supabase) {\n        // Demo mode - just clear the user\n        setUser(null)\n        setSession(null)\n        return\n      }\n\n      const { error } = await supabase.auth.signOut()\n      if (error) {\n        console.error('Sign out error:', error)\n      }\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) {\n        console.error('Reset password error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Reset password error:', error)\n      return { error }\n    }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Helper hook to check if user is admin\nexport function useIsAdmin() {\n  const { user } = useAuth()\n  \n  // Check if user has admin role in metadata or email domain\n  const isAdmin = user?.user_metadata?.role === 'admin' || \n                  user?.email?.endsWith('@royalcuts.com') ||\n                  user?.app_metadata?.role === 'admin'\n  \n  return isAdmin\n}\n\n// Helper hook to get user role\nexport function useUserRole() {\n  const { user } = useAuth()\n  \n  return user?.user_metadata?.role || \n         user?.app_metadata?.role || \n         'user'\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YACb,wEAAwE;YACxE,MAAM,WAAW;gBACf,IAAI;gBACJ,OAAO;gBACP,eAAe;oBAAE,MAAM;oBAAS,WAAW;gBAAa;gBACxD,cAAc;oBAAE,MAAM;gBAAQ;YAChC;YAEA,QAAQ;YACR,WAAW;gBAAE,MAAM;YAAS;YAC5B,WAAW;YACX;QACF;QAEA,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YACnE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,OAAO;gBACL,WAAW;gBACX,QAAQ,SAAS,QAAQ;YAC3B;YACA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO;YAC1C,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,qCAAqC;gBACrC,MAAM,WAAW;oBACf,IAAI;oBACJ,OAAO;oBACP,eAAe;wBAAE,MAAM;wBAAS,WAAW;oBAAa;oBACxD,cAAc;wBAAE,MAAM;oBAAQ;gBAChC;gBAEA,QAAQ;gBACR,WAAW;oBAAE,MAAM;gBAAS;gBAC5B,OAAO;oBAAE,OAAO;gBAAK;YACvB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE;YAAM;QACjB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE;YAAM;QACjB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,kCAAkC;gBAClC,QAAQ;gBACR,WAAW;gBACX;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mBAAmB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,2DAA2D;IAC3D,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;IAE7C,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,OAAO,MAAM,eAAe,QACrB,MAAM,cAAc,QACpB;AACT", "debugId": null}}]}