'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/loading'
import { 
  Scissors, 
  DollarSign, 
  Clock, 
  FileText, 
  Palette, 
  Sparkles, 
  Heart,
  Tag
} from 'lucide-react'
import { Service, ServiceFormData } from '@/types'
import { db } from '@/lib/supabase'

const serviceSchema = z.object({
  name: z.string().min(1, 'Service name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  duration: z.number().min(1, 'Duration must be at least 1 minute').max(480, 'Duration too long'),
  price: z.number().min(0, 'Price must be positive'),
  category: z.enum(['haircut', 'styling', 'treatment', 'coloring', 'other'], {
    required_error: 'Please select a category'
  })
})

interface ServiceFormProps {
  service?: Service
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: (service: Service) => void
}

const categoryOptions = [
  { value: 'haircut', label: 'Haircut', icon: Scissors },
  { value: 'styling', label: 'Styling', icon: Sparkles },
  { value: 'coloring', label: 'Coloring', icon: Palette },
  { value: 'treatment', label: 'Treatment', icon: Heart },
  { value: 'other', label: 'Other', icon: Tag }
]

export function ServiceForm({ service, open, onOpenChange, onSuccess }: ServiceFormProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!service

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<ServiceFormData>({
    resolver: zodResolver(serviceSchema),
    defaultValues: service ? {
      name: service.name,
      description: service.description || '',
      duration: service.duration,
      price: service.price,
      category: service.category
    } : {
      name: '',
      description: '',
      duration: 30,
      price: 0,
      category: 'haircut'
    }
  })

  const selectedCategory = watch('category')

  const onSubmit = async (data: ServiceFormData) => {
    try {
      setLoading(true)
      
      const serviceData = {
        ...data,
        description: data.description || undefined
      }

      let result
      if (isEditing) {
        result = await db.services.update(service.id, serviceData)
      } else {
        result = await db.services.create(serviceData)
      }

      if (result.error) {
        console.error('Error saving service:', result.error)
        alert('Error saving service. Please try again.')
        return
      }

      onSuccess(result.data)
      onOpenChange(false)
      reset()
    } catch (error) {
      console.error('Error saving service:', error)
      alert('Error saving service. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    onOpenChange(false)
    reset()
  }

  const getCategoryIcon = (category: string) => {
    const option = categoryOptions.find(opt => opt.value === category)
    return option ? <option.icon className="h-4 w-4" /> : <Scissors className="h-4 w-4" />
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Scissors className="h-5 w-5" />
            <span>{isEditing ? 'Edit Service' : 'Add New Service'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update service information and pricing'
              : 'Add a new service to your catalog'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Scissors className="h-4 w-4 mr-2" />
                Service Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Service Name *
                </label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Classic Haircut"
                  error={!!errors.name}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="category" className="text-sm font-medium">
                  Category *
                </label>
                <Select
                  value={selectedCategory}
                  onValueChange={(value) => setValue('category', value as any)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center space-x-2">
                          <option.icon className="h-4 w-4" />
                          <span>{option.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-red-500">{errors.category.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium">
                  Description
                </label>
                <div className="relative">
                  <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <textarea
                    id="description"
                    {...register('description')}
                    placeholder="Brief description of the service..."
                    className="w-full pl-10 pt-2 pb-2 pr-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pricing and Duration */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Pricing & Duration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="price" className="text-sm font-medium">
                    Price *
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      {...register('price', { valueAsNumber: true })}
                      placeholder="25.00"
                      className="pl-10"
                      error={!!errors.price}
                    />
                  </div>
                  {errors.price && (
                    <p className="text-sm text-red-500">{errors.price.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="duration" className="text-sm font-medium">
                    Duration (minutes) *
                  </label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="duration"
                      type="number"
                      {...register('duration', { valueAsNumber: true })}
                      placeholder="30"
                      className="pl-10"
                      error={!!errors.duration}
                    />
                  </div>
                  {errors.duration && (
                    <p className="text-sm text-red-500">{errors.duration.message}</p>
                  )}
                </div>
              </div>

              {/* Duration Presets */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Quick Duration Presets</label>
                <div className="flex flex-wrap gap-2">
                  {[15, 30, 45, 60, 90, 120].map((minutes) => (
                    <Button
                      key={minutes}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setValue('duration', minutes)}
                    >
                      {minutes}m
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Preview */}
          <Card className="bg-muted/30">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Service Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg bg-card">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold">
                        {watch('name') || 'Service Name'}
                      </h3>
                    </div>
                    <div className="flex items-center space-x-1 w-fit mb-2">
                      {getCategoryIcon(selectedCategory)}
                      <span className="text-sm capitalize">{selectedCategory}</span>
                    </div>
                    {watch('description') && (
                      <p className="text-sm text-muted-foreground">
                        {watch('description')}
                      </p>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold">
                      ${(watch('price') || 0).toFixed(2)}
                    </p>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      {watch('duration') || 0}m
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <LoadingSpinner size="sm" />
                  <span>{isEditing ? 'Updating...' : 'Creating...'}</span>
                </div>
              ) : (
                isEditing ? 'Update Service' : 'Create Service'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
