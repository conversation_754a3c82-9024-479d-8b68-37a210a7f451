'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/loading'
import { 
  Calendar, 
  Clock, 
  User, 
  Scissors, 
  DollarSign, 
  FileText,
  AlertCircle
} from 'lucide-react'
import { Appointment, AppointmentFormData, Customer, Staff, Service } from '@/types'
import { db } from '@/lib/supabase'
import { formatCurrency } from '@/lib/utils'

const appointmentSchema = z.object({
  customer_id: z.string().min(1, 'Please select a customer'),
  staff_id: z.string().min(1, 'Please select a staff member'),
  service_id: z.string().min(1, 'Please select a service'),
  appointment_date: z.string().min(1, 'Appointment date is required'),
  start_time: z.string().min(1, 'Start time is required'),
  notes: z.string().optional()
})

interface AppointmentFormProps {
  appointment?: Appointment
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: (appointment: Appointment) => void
}

export function AppointmentForm({ appointment, open, onOpenChange, onSuccess }: AppointmentFormProps) {
  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [staff, setStaff] = useState<Staff[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [conflicts, setConflicts] = useState<string[]>([])
  const isEditing = !!appointment

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: appointment ? {
      customer_id: appointment.customer_id,
      staff_id: appointment.staff_id,
      service_id: appointment.service_id,
      appointment_date: appointment.appointment_date,
      start_time: appointment.start_time,
      notes: appointment.notes || ''
    } : {
      customer_id: '',
      staff_id: '',
      service_id: '',
      appointment_date: '',
      start_time: '',
      notes: ''
    }
  })

  const watchedValues = watch()

  useEffect(() => {
    if (open) {
      loadFormData()
    }
  }, [open])

  useEffect(() => {
    // Update selected service when service_id changes
    const service = services.find(s => s.id === watchedValues.service_id)
    setSelectedService(service || null)
  }, [watchedValues.service_id, services])

  useEffect(() => {
    // Check for conflicts when date, time, or staff changes
    if (watchedValues.appointment_date && watchedValues.start_time && watchedValues.staff_id && selectedService) {
      checkConflicts()
    }
  }, [watchedValues.appointment_date, watchedValues.start_time, watchedValues.staff_id, selectedService])

  const loadFormData = async () => {
    try {
      // Load customers, staff, and services
      const [customersResult, staffResult, servicesResult] = await Promise.all([
        db.customers.getAll(),
        db.staff.getActive(),
        db.services.getActive()
      ])

      // Use mock data if database calls fail
      setCustomers(customersResult.data || mockCustomers)
      setStaff(staffResult.data || mockStaff)
      setServices(servicesResult.data || mockServices)
    } catch (error) {
      console.error('Error loading form data:', error)
      setCustomers(mockCustomers)
      setStaff(mockStaff)
      setServices(mockServices)
    }
  }

  const checkConflicts = async () => {
    if (!selectedService) return

    try {
      const startTime = new Date(`${watchedValues.appointment_date}T${watchedValues.start_time}`)
      const endTime = new Date(startTime.getTime() + selectedService.duration * 60000)
      
      // Check staff availability (simplified - in real app would check database)
      const conflictMessages: string[] = []
      
      // Mock conflict detection
      if (watchedValues.start_time === '12:00') {
        conflictMessages.push('Staff member has lunch break at this time')
      }
      
      setConflicts(conflictMessages)
    } catch (error) {
      console.error('Error checking conflicts:', error)
    }
  }

  const calculateEndTime = (startTime: string, duration: number) => {
    const start = new Date(`2000-01-01T${startTime}`)
    const end = new Date(start.getTime() + duration * 60000)
    return end.toTimeString().slice(0, 5)
  }

  const onSubmit = async (data: AppointmentFormData) => {
    try {
      setLoading(true)
      
      if (!selectedService) {
        alert('Please select a service')
        return
      }

      const endTime = calculateEndTime(data.start_time, selectedService.duration)
      
      const appointmentData = {
        ...data,
        end_time: endTime,
        total_amount: selectedService.price,
        status: 'scheduled' as const,
        notes: data.notes || undefined
      }

      let result
      if (isEditing) {
        result = await db.appointments.update(appointment.id, appointmentData)
      } else {
        result = await db.appointments.create(appointmentData)
      }

      if (result.error) {
        console.error('Error saving appointment:', result.error)
        alert('Error saving appointment. Please try again.')
        return
      }

      onSuccess(result.data)
      onOpenChange(false)
      reset()
    } catch (error) {
      console.error('Error saving appointment:', error)
      alert('Error saving appointment. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    onOpenChange(false)
    reset()
    setConflicts([])
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>{isEditing ? 'Edit Appointment' : 'Book New Appointment'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update appointment details and schedule'
              : 'Schedule a new appointment for a customer'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Customer and Staff Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <User className="h-4 w-4 mr-2" />
                Customer & Staff
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="customer_id" className="text-sm font-medium">
                    Customer *
                  </label>
                  <Select
                    value={watchedValues.customer_id}
                    onValueChange={(value) => setValue('customer_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.first_name} {customer.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.customer_id && (
                    <p className="text-sm text-red-500">{errors.customer_id.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="staff_id" className="text-sm font-medium">
                    Staff Member *
                  </label>
                  <Select
                    value={watchedValues.staff_id}
                    onValueChange={(value) => setValue('staff_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select staff" />
                    </SelectTrigger>
                    <SelectContent>
                      {staff.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          {member.first_name} {member.last_name} ({member.role})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.staff_id && (
                    <p className="text-sm text-red-500">{errors.staff_id.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service and Schedule */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Scissors className="h-4 w-4 mr-2" />
                Service & Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="service_id" className="text-sm font-medium">
                  Service *
                </label>
                <Select
                  value={watchedValues.service_id}
                  onValueChange={(value) => setValue('service_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select service" />
                  </SelectTrigger>
                  <SelectContent>
                    {services.map((service) => (
                      <SelectItem key={service.id} value={service.id}>
                        <div className="flex justify-between items-center w-full">
                          <span>{service.name}</span>
                          <span className="text-muted-foreground ml-2">
                            {formatCurrency(service.price)} • {service.duration}m
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.service_id && (
                  <p className="text-sm text-red-500">{errors.service_id.message}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="appointment_date" className="text-sm font-medium">
                    Date *
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="appointment_date"
                      type="date"
                      {...register('appointment_date')}
                      className="pl-10"
                      min={new Date().toISOString().split('T')[0]}
                      error={!!errors.appointment_date}
                    />
                  </div>
                  {errors.appointment_date && (
                    <p className="text-sm text-red-500">{errors.appointment_date.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="start_time" className="text-sm font-medium">
                    Start Time *
                  </label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="start_time"
                      type="time"
                      {...register('start_time')}
                      className="pl-10"
                      error={!!errors.start_time}
                    />
                  </div>
                  {errors.start_time && (
                    <p className="text-sm text-red-500">{errors.start_time.message}</p>
                  )}
                </div>
              </div>

              {/* Service Summary */}
              {selectedService && (
                <div className="p-4 bg-muted/30 rounded-lg">
                  <h4 className="font-medium mb-2">Appointment Summary</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Service:</span>
                      <p className="font-medium">{selectedService.name}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <p className="font-medium">{selectedService.duration} minutes</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Price:</span>
                      <p className="font-medium">{formatCurrency(selectedService.price)}</p>
                    </div>
                    {watchedValues.start_time && (
                      <div>
                        <span className="text-muted-foreground">End Time:</span>
                        <p className="font-medium">
                          {calculateEndTime(watchedValues.start_time, selectedService.duration)}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Conflicts Warning */}
              {conflicts.length > 0 && (
                <div className="p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-red-700 dark:text-red-300">
                        Scheduling Conflicts
                      </h4>
                      <ul className="text-sm text-red-600 dark:text-red-400 mt-1">
                        {conflicts.map((conflict, index) => (
                          <li key={index}>• {conflict}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Additional Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <label htmlFor="notes" className="text-sm font-medium">
                  Notes
                </label>
                <textarea
                  id="notes"
                  {...register('notes')}
                  placeholder="Any special requests or notes for this appointment..."
                  className="w-full p-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={loading || conflicts.length > 0}
            >
              {loading ? (
                <div className="flex items-center space-x-2">
                  <LoadingSpinner size="sm" />
                  <span>{isEditing ? 'Updating...' : 'Booking...'}</span>
                </div>
              ) : (
                isEditing ? 'Update Appointment' : 'Book Appointment'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

// Mock data for demonstration
const mockCustomers: Customer[] = [
  { id: '1', first_name: 'John', last_name: 'Doe', email: '<EMAIL>', phone: '******-0101', created_at: '', updated_at: '' },
  { id: '2', first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>', phone: '******-0102', created_at: '', updated_at: '' }
]

const mockStaff: Staff[] = [
  { id: '1', first_name: 'Maria', last_name: 'Garcia', email: '<EMAIL>', phone: '******-0202', role: 'barber', hire_date: '2021-03-10', is_active: true, created_at: '', updated_at: '' },
  { id: '2', first_name: 'James', last_name: 'Thompson', email: '<EMAIL>', phone: '******-0203', role: 'stylist', hire_date: '2021-06-20', is_active: true, created_at: '', updated_at: '' }
]

const mockServices: Service[] = [
  { id: '1', name: 'Classic Haircut', duration: 30, price: 25.00, category: 'haircut', is_active: true, created_at: '', updated_at: '' },
  { id: '2', name: 'Women\'s Cut & Style', duration: 60, price: 45.00, category: 'styling', is_active: true, created_at: '', updated_at: '' }
]
