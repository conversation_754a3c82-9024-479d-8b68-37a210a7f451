@import "tailwindcss";

:root {
  --background: hsl(0 0% 98%);
  --foreground: hsl(0 0% 10%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 10%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 10%);
  --primary: hsl(45 93% 47%);
  --primary-foreground: hsl(0 0% 98%);
  --secondary: hsl(0 0% 96%);
  --secondary-foreground: hsl(0 0% 9%);
  --muted: hsl(0 0% 96%);
  --muted-foreground: hsl(0 0% 45%);
  --accent: hsl(45 84% 60%);
  --accent-foreground: hsl(0 0% 9%);
  --destructive: hsl(0 84% 60%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(0 0% 90%);
  --input: hsl(0 0% 100%);
  --ring: hsl(45 93% 47%);
  --radius: 0.75rem;
  --success: hsl(142 76% 36%);
  --success-foreground: hsl(0 0% 98%);
  --warning: hsl(38 92% 50%);
  --warning-foreground: hsl(0 0% 98%);
  --info: hsl(217 91% 60%);
  --info-foreground: hsl(0 0% 98%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: hsl(0 0% 10%);
    --foreground: hsl(0 0% 95%);
    --card: hsl(0 0% 16%);
    --card-foreground: hsl(0 0% 95%);
    --popover: hsl(0 0% 16%);
    --popover-foreground: hsl(0 0% 95%);
    --primary: hsl(45 93% 47%);
    --primary-foreground: hsl(0 0% 10%);
    --secondary: hsl(0 0% 23%);
    --secondary-foreground: hsl(0 0% 95%);
    --muted: hsl(0 0% 23%);
    --muted-foreground: hsl(0 0% 63%);
    --accent: hsl(45 84% 60%);
    --accent-foreground: hsl(0 0% 10%);
    --destructive: hsl(0 84% 60%);
    --destructive-foreground: hsl(0 0% 98%);
    --border: hsl(0 0% 25%);
    --input: hsl(0 0% 16%);
    --ring: hsl(45 93% 47%);
    --success: hsl(142 76% 36%);
    --success-foreground: hsl(0 0% 98%);
    --warning: hsl(38 92% 50%);
    --warning-foreground: hsl(0 0% 98%);
    --info: hsl(217 91% 60%);
    --info-foreground: hsl(0 0% 98%);
  }
}

body {
  background: linear-gradient(135deg, var(--background) 0%, var(--secondary) 100%);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-height: 100vh;
}

* {
  border-color: var(--border);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

/* Custom shadows */
.shadow-elegant {
  box-shadow: 0 4px 6px -1px rgba(139, 90, 60, 0.1), 0 2px 4px -1px rgba(139, 90, 60, 0.06);
}

.shadow-elegant-lg {
  box-shadow: 0 10px 15px -3px rgba(139, 90, 60, 0.1), 0 4px 6px -2px rgba(139, 90, 60, 0.05);
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--secondary) 0%, var(--muted) 100%);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
