{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport function createSupabaseClient() {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Database helper functions\nexport const db = {\n  // Customers\n  customers: {\n    getAll: () => supabase.from('customers').select('*').order('created_at', { ascending: false }),\n    getById: (id: string) => supabase.from('customers').select('*').eq('id', id).single(),\n    create: (data: any) => supabase.from('customers').insert(data).select().single(),\n    update: (id: string, data: any) => supabase.from('customers').update(data).eq('id', id).select().single(),\n    delete: (id: string) => supabase.from('customers').delete().eq('id', id),\n    search: (query: string) => supabase\n      .from('customers')\n      .select('*')\n      .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%,phone.ilike.%${query}%`)\n      .order('created_at', { ascending: false })\n  },\n\n  // Staff\n  staff: {\n    getAll: () => supabase.from('staff').select('*').order('created_at', { ascending: false }),\n    getById: (id: string) => supabase.from('staff').select('*').eq('id', id).single(),\n    getActive: () => supabase.from('staff').select('*').eq('is_active', true).order('first_name'),\n    create: (data: any) => supabase.from('staff').insert(data).select().single(),\n    update: (id: string, data: any) => supabase.from('staff').update(data).eq('id', id).select().single(),\n    delete: (id: string) => supabase.from('staff').delete().eq('id', id),\n  },\n\n  // Services\n  services: {\n    getAll: () => supabase.from('services').select('*').order('name'),\n    getById: (id: string) => supabase.from('services').select('*').eq('id', id).single(),\n    getActive: () => supabase.from('services').select('*').eq('is_active', true).order('name'),\n    create: (data: any) => supabase.from('services').insert(data).select().single(),\n    update: (id: string, data: any) => supabase.from('services').update(data).eq('id', id).select().single(),\n    delete: (id: string) => supabase.from('services').delete().eq('id', id),\n  },\n\n  // Appointments\n  appointments: {\n    getAll: () => supabase\n      .from('appointments')\n      .select(`\n        *,\n        customer:customers(*),\n        staff:staff(*),\n        service:services(*)\n      `)\n      .order('appointment_date', { ascending: false }),\n    getById: (id: string) => supabase\n      .from('appointments')\n      .select(`\n        *,\n        customer:customers(*),\n        staff:staff(*),\n        service:services(*)\n      `)\n      .eq('id', id)\n      .single(),\n    getByDate: (date: string) => supabase\n      .from('appointments')\n      .select(`\n        *,\n        customer:customers(*),\n        staff:staff(*),\n        service:services(*)\n      `)\n      .eq('appointment_date', date)\n      .order('start_time'),\n    getByStaff: (staffId: string, date?: string) => {\n      let query = supabase\n        .from('appointments')\n        .select(`\n          *,\n          customer:customers(*),\n          staff:staff(*),\n          service:services(*)\n        `)\n        .eq('staff_id', staffId)\n      \n      if (date) {\n        query = query.eq('appointment_date', date)\n      }\n      \n      return query.order('appointment_date', { ascending: false })\n    },\n    create: (data: any) => supabase.from('appointments').insert(data).select().single(),\n    update: (id: string, data: any) => supabase.from('appointments').update(data).eq('id', id).select().single(),\n    delete: (id: string) => supabase.from('appointments').delete().eq('id', id),\n  },\n\n  // Inventory\n  inventory: {\n    getAll: () => supabase.from('inventory').select('*').order('name'),\n    getById: (id: string) => supabase.from('inventory').select('*').eq('id', id).single(),\n    getLowStock: () => supabase\n      .from('inventory')\n      .select('*')\n      .filter('current_stock', 'lte', 'min_stock_level')\n      .order('name'),\n    create: (data: any) => supabase.from('inventory').insert(data).select().single(),\n    update: (id: string, data: any) => supabase.from('inventory').update(data).eq('id', id).select().single(),\n    delete: (id: string) => supabase.from('inventory').delete().eq('id', id),\n  },\n\n  // Transactions\n  transactions: {\n    getAll: () => supabase.from('transactions').select('*').order('transaction_date', { ascending: false }),\n    getById: (id: string) => supabase.from('transactions').select('*').eq('id', id).single(),\n    getByDateRange: (startDate: string, endDate: string) => supabase\n      .from('transactions')\n      .select('*')\n      .gte('transaction_date', startDate)\n      .lte('transaction_date', endDate)\n      .order('transaction_date', { ascending: false }),\n    create: (data: any) => supabase.from('transactions').insert(data).select().single(),\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAE3C,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,KAAK;IAChB,YAAY;IACZ,WAAW;QACT,QAAQ,IAAM,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,KAAK,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;QAC5F,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QACnF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM;QAC9E,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,MAAM,GAAG,MAAM;QACvG,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,MAAM;QACrE,QAAQ,CAAC,QAAkB,SACxB,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,CAAC,kBAAkB,EAAE,MAAM,mBAAmB,EAAE,MAAM,eAAe,EAAE,MAAM,eAAe,EAAE,MAAM,CAAC,CAAC,EACzG,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;IAC5C;IAEA,QAAQ;IACR,OAAO;QACL,QAAQ,IAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;QACxF,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAC/E,WAAW,IAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,EAAE,CAAC,aAAa,MAAM,KAAK,CAAC;QAChF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM;QAC1E,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,MAAM,GAAG,MAAM;QACnG,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM;IACnE;IAEA,WAAW;IACX,UAAU;QACR,QAAQ,IAAM,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,KAAK,KAAK,CAAC;QAC1D,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QAClF,WAAW,IAAM,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,aAAa,MAAM,KAAK,CAAC;QACnF,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM;QAC7E,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,MAAM,GAAG,MAAM;QACtG,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,YAAY,MAAM,GAAG,EAAE,CAAC,MAAM;IACtE;IAEA,eAAe;IACf,cAAc;QACZ,QAAQ,IAAM,SACX,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;MAKT,CAAC,EACA,KAAK,CAAC,oBAAoB;gBAAE,WAAW;YAAM;QAChD,SAAS,CAAC,KAAe,SACtB,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;MAKT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QACT,WAAW,CAAC,OAAiB,SAC1B,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;MAKT,CAAC,EACA,EAAE,CAAC,oBAAoB,MACvB,KAAK,CAAC;QACT,YAAY,CAAC,SAAiB;YAC5B,IAAI,QAAQ,SACT,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;QAKT,CAAC,EACA,EAAE,CAAC,YAAY;YAElB,IAAI,MAAM;gBACR,QAAQ,MAAM,EAAE,CAAC,oBAAoB;YACvC;YAEA,OAAO,MAAM,KAAK,CAAC,oBAAoB;gBAAE,WAAW;YAAM;QAC5D;QACA,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM;QACjF,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,MAAM,GAAG,MAAM;QAC1G,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,gBAAgB,MAAM,GAAG,EAAE,CAAC,MAAM;IAC1E;IAEA,YAAY;IACZ,WAAW;QACT,QAAQ,IAAM,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,KAAK,KAAK,CAAC;QAC3D,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QACnF,aAAa,IAAM,SAChB,IAAI,CAAC,aACL,MAAM,CAAC,KACP,MAAM,CAAC,iBAAiB,OAAO,mBAC/B,KAAK,CAAC;QACT,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM;QAC9E,QAAQ,CAAC,IAAY,OAAc,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,MAAM,GAAG,MAAM;QACvG,QAAQ,CAAC,KAAe,SAAS,IAAI,CAAC,aAAa,MAAM,GAAG,EAAE,CAAC,MAAM;IACvE;IAEA,eAAe;IACf,cAAc;QACZ,QAAQ,IAAM,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,KAAK,KAAK,CAAC,oBAAoB;gBAAE,WAAW;YAAM;QACrG,SAAS,CAAC,KAAe,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI,MAAM;QACtF,gBAAgB,CAAC,WAAmB,UAAoB,SACrD,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,GAAG,CAAC,oBAAoB,WACxB,GAAG,CAAC,oBAAoB,SACxB,KAAK,CAAC,oBAAoB;gBAAE,WAAW;YAAM;QAChD,QAAQ,CAAC,OAAc,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM;IACnF;AACF", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/contexts/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, userData?: any) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  resetPassword: (email: string) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session }, error } = await supabase.auth.getSession()\n      if (error) {\n        console.error('Error getting session:', error)\n      } else {\n        setSession(session)\n        setUser(session?.user ?? null)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session)\n        setSession(session)\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n      \n      if (error) {\n        console.error('Sign in error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Sign in error:', error)\n      return { error }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string, userData?: any) => {\n    try {\n      setLoading(true)\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: userData,\n        },\n      })\n\n      if (error) {\n        console.error('Sign up error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Sign up error:', error)\n      return { error }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) {\n        console.error('Sign out error:', error)\n      }\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) {\n        console.error('Reset password error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Reset password error:', error)\n      return { error }\n    }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Helper hook to check if user is admin\nexport function useIsAdmin() {\n  const { user } = useAuth()\n  \n  // Check if user has admin role in metadata or email domain\n  const isAdmin = user?.user_metadata?.role === 'admin' || \n                  user?.email?.endsWith('@royalcuts.com') ||\n                  user?.app_metadata?.role === 'admin'\n  \n  return isAdmin\n}\n\n// Helper hook to get user role\nexport function useUserRole() {\n  const { user } = useAuth()\n  \n  return user?.user_metadata?.role || \n         user?.app_metadata?.role || \n         'user'\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;oBACnE,IAAI,OAAO;wBACT,QAAQ,KAAK,CAAC,0BAA0B;oBAC1C,OAAO;wBACL,WAAW;wBACX,QAAQ,SAAS,QAAQ;oBAC3B;oBACA,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO;oBAC1C,WAAW;oBACX,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE;YAAM;QACjB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE;YAAM;QACjB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mBAAmB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA/HgB;KAAA;AAiIT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,2DAA2D;IAC3D,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;IAE7C,OAAO;AACT;IATgB;;QACG;;;AAWZ,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,OAAO,MAAM,eAAe,QACrB,MAAM,cAAc,QACpB;AACT;IANgB;;QACG", "debugId": null}}]}