'use client'

import { Navigation } from './navigation'
import { Scissors } from 'lucide-react'

export function Sidebar() {
  return (
    <div className="flex h-full w-64 flex-col bg-card border-r">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b">
        <div className="flex items-center space-x-2">
          <Scissors className="h-6 w-6 text-primary" />
          <span className="text-lg font-semibold">BarberShop Pro</span>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 px-3 py-4">
        <Navigation />
      </div>

      {/* User info */}
      <div className="border-t p-4">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
            <span className="text-sm font-medium text-primary-foreground">A</span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">Admin User</p>
            <p className="text-xs text-muted-foreground truncate"><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  )
}
