'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/loading'
import { 
  Package, 
  DollarSign, 
  Hash, 
  FileText, 
  Building,
  Calendar,
  Droplets,
  Scissors,
  Sparkles,
  Tag
} from 'lucide-react'
import { InventoryItem, InventoryFormData } from '@/types'
import { db } from '@/lib/supabase'

const inventorySchema = z.object({
  name: z.string().min(1, 'Item name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  category: z.enum(['shampoo', 'conditioner', 'styling', 'tools', 'supplies', 'other'], {
    required_error: 'Please select a category'
  }),
  brand: z.string().optional(),
  sku: z.string().optional(),
  current_stock: z.number().min(0, 'Stock cannot be negative'),
  min_stock_level: z.number().min(0, 'Minimum level cannot be negative'),
  unit_cost: z.number().min(0, 'Cost must be positive'),
  supplier: z.string().optional()
})

interface InventoryFormProps {
  item?: InventoryItem
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: (item: InventoryItem) => void
}

const categoryOptions = [
  { value: 'shampoo', label: 'Shampoo', icon: Droplets },
  { value: 'conditioner', label: 'Conditioner', icon: Droplets },
  { value: 'styling', label: 'Styling Products', icon: Sparkles },
  { value: 'tools', label: 'Tools & Equipment', icon: Scissors },
  { value: 'supplies', label: 'Supplies', icon: Package },
  { value: 'other', label: 'Other', icon: Tag }
]

export function InventoryForm({ item, open, onOpenChange, onSuccess }: InventoryFormProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!item

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<InventoryFormData>({
    resolver: zodResolver(inventorySchema),
    defaultValues: item ? {
      name: item.name,
      description: item.description || '',
      category: item.category,
      brand: item.brand || '',
      sku: item.sku || '',
      current_stock: item.current_stock,
      min_stock_level: item.min_stock_level,
      unit_cost: item.unit_cost,
      supplier: item.supplier || ''
    } : {
      name: '',
      description: '',
      category: 'supplies',
      brand: '',
      sku: '',
      current_stock: 0,
      min_stock_level: 0,
      unit_cost: 0,
      supplier: ''
    }
  })

  const selectedCategory = watch('category')
  const currentStock = watch('current_stock')
  const unitCost = watch('unit_cost')

  const onSubmit = async (data: InventoryFormData) => {
    try {
      setLoading(true)
      
      const inventoryData = {
        ...data,
        description: data.description || undefined,
        brand: data.brand || undefined,
        sku: data.sku || undefined,
        supplier: data.supplier || undefined,
        last_restocked: isEditing ? item.last_restocked : new Date().toISOString().split('T')[0]
      }

      let result
      if (isEditing) {
        result = await db.inventory.update(item.id, inventoryData)
      } else {
        result = await db.inventory.create(inventoryData)
      }

      if (result.error) {
        console.error('Error saving inventory item:', result.error)
        alert('Error saving inventory item. Please try again.')
        return
      }

      onSuccess(result.data)
      onOpenChange(false)
      reset()
    } catch (error) {
      console.error('Error saving inventory item:', error)
      alert('Error saving inventory item. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    onOpenChange(false)
    reset()
  }

  const getCategoryIcon = (category: string) => {
    const option = categoryOptions.find(opt => opt.value === category)
    return option ? <option.icon className="h-4 w-4" /> : <Package className="h-4 w-4" />
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>{isEditing ? 'Edit Inventory Item' : 'Add New Inventory Item'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update inventory item information and stock levels'
              : 'Add a new item to your inventory'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Package className="h-4 w-4 mr-2" />
                Item Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="name" className="text-sm font-medium">
                  Item Name *
                </label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Professional Shampoo"
                  error={!!errors.name}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="category" className="text-sm font-medium">
                    Category *
                  </label>
                  <Select
                    value={selectedCategory}
                    onValueChange={(value) => setValue('category', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <option.icon className="h-4 w-4" />
                            <span>{option.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category && (
                    <p className="text-sm text-red-500">{errors.category.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="brand" className="text-sm font-medium">
                    Brand
                  </label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="brand"
                      {...register('brand')}
                      placeholder="SalonPro"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="sku" className="text-sm font-medium">
                    SKU
                  </label>
                  <div className="relative">
                    <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="sku"
                      {...register('sku')}
                      placeholder="SP-SHAM-001"
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="supplier" className="text-sm font-medium">
                    Supplier
                  </label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="supplier"
                      {...register('supplier')}
                      placeholder="Beauty Supply Co"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="description" className="text-sm font-medium">
                  Description
                </label>
                <div className="relative">
                  <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <textarea
                    id="description"
                    {...register('description')}
                    placeholder="Brief description of the item..."
                    className="w-full pl-10 pt-2 pb-2 pr-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stock and Pricing */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                Stock & Pricing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label htmlFor="current_stock" className="text-sm font-medium">
                    Current Stock *
                  </label>
                  <Input
                    id="current_stock"
                    type="number"
                    {...register('current_stock', { valueAsNumber: true })}
                    placeholder="0"
                    error={!!errors.current_stock}
                  />
                  {errors.current_stock && (
                    <p className="text-sm text-red-500">{errors.current_stock.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="min_stock_level" className="text-sm font-medium">
                    Min Stock Level *
                  </label>
                  <Input
                    id="min_stock_level"
                    type="number"
                    {...register('min_stock_level', { valueAsNumber: true })}
                    placeholder="0"
                    error={!!errors.min_stock_level}
                  />
                  {errors.min_stock_level && (
                    <p className="text-sm text-red-500">{errors.min_stock_level.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="unit_cost" className="text-sm font-medium">
                    Unit Cost *
                  </label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="unit_cost"
                      type="number"
                      step="0.01"
                      {...register('unit_cost', { valueAsNumber: true })}
                      placeholder="0.00"
                      className="pl-10"
                      error={!!errors.unit_cost}
                    />
                  </div>
                  {errors.unit_cost && (
                    <p className="text-sm text-red-500">{errors.unit_cost.message}</p>
                  )}
                </div>
              </div>

              {/* Stock Summary */}
              <div className="p-4 bg-muted/30 rounded-lg">
                <h4 className="font-medium mb-2">Stock Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Total Value:</span>
                    <p className="font-medium">
                      ${((currentStock || 0) * (unitCost || 0)).toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Stock Status:</span>
                    <p className={`font-medium ${
                      (currentStock || 0) <= 0 ? 'text-red-600' :
                      (currentStock || 0) <= (watch('min_stock_level') || 0) ? 'text-orange-600' :
                      'text-green-600'
                    }`}>
                      {(currentStock || 0) <= 0 ? 'Out of Stock' :
                       (currentStock || 0) <= (watch('min_stock_level') || 0) ? 'Low Stock' :
                       'In Stock'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <LoadingSpinner size="sm" />
                  <span>{isEditing ? 'Updating...' : 'Creating...'}</span>
                </div>
              ) : (
                isEditing ? 'Update Item' : 'Create Item'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
