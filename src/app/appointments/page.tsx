'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader, PageHeaderActions } from '@/components/layout/page-header'
import { AppointmentForm } from '@/components/appointments/appointment-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { EmptyTableState } from '@/components/ui/empty-state'
import { Loading } from '@/components/ui/loading'
import { 
  Calendar, 
  Plus, 
  Search, 
  Filter, 
  Clock,
  User,
  Scissors,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { Appointment } from '@/types'
import { db } from '@/lib/supabase'
import { formatDate, formatTime, formatCurrency } from '@/lib/utils'

export default function AppointmentsPage() {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0])
  const [showForm, setShowForm] = useState(false)
  const [editingAppointment, setEditingAppointment] = useState<Appointment | undefined>()

  useEffect(() => {
    loadAppointments()
  }, [])

  useEffect(() => {
    // Filter appointments based on search term and status
    let filtered = appointments

    if (searchTerm.trim() !== '') {
      filtered = filtered.filter(appointment =>
        appointment.customer?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        appointment.customer?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        appointment.staff?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        appointment.staff?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        appointment.service?.name?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedStatus !== 'all') {
      filtered = filtered.filter(appointment => appointment.status === selectedStatus)
    }

    setFilteredAppointments(filtered)
  }, [appointments, searchTerm, selectedStatus])

  const loadAppointments = async () => {
    try {
      setLoading(true)
      const { data, error } = await db.appointments.getAll()
      
      if (error) {
        console.error('Error loading appointments:', error)
        // For demo purposes, use mock data if Supabase is not configured
        setAppointments(mockAppointments)
      } else {
        setAppointments(data || [])
      }
    } catch (error) {
      console.error('Error loading appointments:', error)
      // Use mock data as fallback
      setAppointments(mockAppointments)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteAppointment = async (appointmentId: string) => {
    if (confirm('Are you sure you want to delete this appointment?')) {
      try {
        const { error } = await db.appointments.delete(appointmentId)
        if (error) {
          console.error('Error deleting appointment:', error)
        } else {
          setAppointments(appointments.filter(a => a.id !== appointmentId))
        }
      } catch (error) {
        console.error('Error deleting appointment:', error)
      }
    }
  }

  const handleUpdateStatus = async (appointment: Appointment, newStatus: string) => {
    try {
      const { error } = await db.appointments.update(appointment.id, { 
        status: newStatus 
      })
      if (error) {
        console.error('Error updating appointment:', error)
      } else {
        setAppointments(appointments.map(a => 
          a.id === appointment.id ? { ...a, status: newStatus as any } : a
        ))
      }
    } catch (error) {
      console.error('Error updating appointment:', error)
    }
  }

  const handleAddAppointment = () => {
    setEditingAppointment(undefined)
    setShowForm(true)
  }

  const handleEditAppointment = (appointment: Appointment) => {
    setEditingAppointment(appointment)
    setShowForm(true)
  }

  const handleFormSuccess = (appointment: Appointment) => {
    if (editingAppointment) {
      // Update existing appointment
      setAppointments(appointments.map(a => a.id === appointment.id ? appointment : a))
    } else {
      // Add new appointment
      setAppointments([appointment, ...appointments])
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
      case 'no_show':
        return <XCircle className="h-4 w-4" />
      case 'in_progress':
        return <Clock className="h-4 w-4" />
      case 'confirmed':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success'
      case 'cancelled':
      case 'no_show':
        return 'destructive'
      case 'in_progress':
        return 'warning'
      case 'confirmed':
        return 'info'
      default:
        return 'secondary'
    }
  }

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'scheduled', label: 'Scheduled' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'no_show', label: 'No Show' }
  ]

  const todayAppointments = appointments.filter(a => a.appointment_date === selectedDate)
  const upcomingAppointments = appointments.filter(a => 
    new Date(a.appointment_date) > new Date() && a.status !== 'cancelled'
  )

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="Appointments"
          description="Manage appointments, schedules, and booking calendar"
          icon={<Calendar className="h-6 w-6 text-white" />}
          actions={
            <PageHeaderActions>
              <Button variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Calendar View
              </Button>
              <Button onClick={handleAddAppointment}>
                <Plus className="h-4 w-4 mr-2" />
                Book Appointment
              </Button>
            </PageHeaderActions>
          }
        />

        <div className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-elegant">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by customer, staff, or service..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex gap-2">
                  <Input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="w-auto"
                  />
                  {statusOptions.map((status) => (
                    <Button
                      key={status.value}
                      variant={selectedStatus === status.value ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedStatus(status.value)}
                    >
                      {status.label}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Appointment Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Today's Appointments</p>
                    <p className="text-2xl font-bold">{todayAppointments.length}</p>
                  </div>
                  <Calendar className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Upcoming</p>
                    <p className="text-2xl font-bold">{upcomingAppointments.length}</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Completed</p>
                    <p className="text-2xl font-bold">
                      {appointments.filter(a => a.status === 'completed').length}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Revenue Today</p>
                    <p className="text-2xl font-bold">
                      {formatCurrency(
                        todayAppointments
                          .filter(a => a.status === 'completed')
                          .reduce((sum, a) => sum + a.total_amount, 0)
                      )}
                    </p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="text-green-600 font-bold">$</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Appointments List */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle>Appointment Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Loading text="Loading appointments..." />
              ) : filteredAppointments.length === 0 ? (
                searchTerm || selectedStatus !== 'all' ? (
                  <EmptyTableState
                    icon={<Search className="h-8 w-8 text-muted-foreground" />}
                    title="No appointments found"
                    description="No appointments match your current filters"
                    action={{
                      label: "Clear filters",
                      onClick: () => {
                        setSearchTerm('')
                        setSelectedStatus('all')
                      }
                    }}
                  />
                ) : (
                  <EmptyTableState
                    icon={<Calendar className="h-8 w-8 text-muted-foreground" />}
                    title="No appointments scheduled"
                    description="Start booking appointments for your customers"
                    action={{
                      label: "Book Appointment",
                      onClick: handleAddAppointment
                    }}
                  />
                )
              ) : (
                <div className="space-y-4">
                  {filteredAppointments.map((appointment) => (
                    <AppointmentCard
                      key={appointment.id}
                      appointment={appointment}
                      onEdit={() => handleEditAppointment(appointment)}
                      onDelete={handleDeleteAppointment}
                      onUpdateStatus={handleUpdateStatus}
                      getStatusIcon={getStatusIcon}
                      getStatusBadgeVariant={getStatusBadgeVariant}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Appointment Form Dialog */}
        <AppointmentForm
          appointment={editingAppointment}
          open={showForm}
          onOpenChange={setShowForm}
          onSuccess={handleFormSuccess}
        />
      </MainLayout>
    </ProtectedRoute>
  )
}

interface AppointmentCardProps {
  appointment: Appointment
  onEdit: () => void
  onDelete: (id: string) => void
  onUpdateStatus: (appointment: Appointment, status: string) => void
  getStatusIcon: (status: string) => React.ReactNode
  getStatusBadgeVariant: (status: string) => any
}

function AppointmentCard({ 
  appointment, 
  onEdit, 
  onDelete, 
  onUpdateStatus, 
  getStatusIcon, 
  getStatusBadgeVariant 
}: AppointmentCardProps) {
  return (
    <div className="p-4 border rounded-lg hover:shadow-elegant transition-all duration-200 bg-card">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center">
            <span className="text-white font-semibold">
              {appointment.customer?.first_name?.[0]}{appointment.customer?.last_name?.[0]}
            </span>
          </div>
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="font-semibold text-foreground">
                {appointment.customer?.first_name} {appointment.customer?.last_name}
              </h3>
              <Badge variant={getStatusBadgeVariant(appointment.status)} className="flex items-center space-x-1">
                {getStatusIcon(appointment.status)}
                <span className="capitalize">{appointment.status.replace('_', ' ')}</span>
              </Badge>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                {formatDate(appointment.appointment_date)}
              </div>
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}
              </div>
              <div className="flex items-center">
                <Scissors className="h-3 w-3 mr-1" />
                {appointment.service?.name}
              </div>
              <div className="flex items-center">
                <User className="h-3 w-3 mr-1" />
                {appointment.staff?.first_name} {appointment.staff?.last_name}
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="text-right mr-4">
            <p className="font-semibold">{formatCurrency(appointment.total_amount)}</p>
            <p className="text-xs text-muted-foreground">{appointment.service?.duration}m</p>
          </div>
          
          {appointment.status === 'scheduled' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onUpdateStatus(appointment, 'confirmed')}
            >
              Confirm
            </Button>
          )}
          
          {appointment.status === 'confirmed' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onUpdateStatus(appointment, 'in_progress')}
            >
              Start
            </Button>
          )}
          
          {appointment.status === 'in_progress' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onUpdateStatus(appointment, 'completed')}
            >
              Complete
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onEdit}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(appointment.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {appointment.notes && (
        <div className="mt-3 pt-3 border-t">
          <p className="text-sm text-muted-foreground">
            <strong>Notes:</strong> {appointment.notes}
          </p>
        </div>
      )}
    </div>
  )
}

// Mock data for demonstration
const mockAppointments: Appointment[] = [
  {
    id: '1',
    customer_id: '1',
    staff_id: '1',
    service_id: '1',
    appointment_date: new Date().toISOString().split('T')[0],
    start_time: '10:00',
    end_time: '10:30',
    status: 'confirmed',
    total_amount: 25.00,
    notes: 'Regular customer, prefers short cuts',
    created_at: '2024-12-10T10:00:00Z',
    updated_at: '2024-12-15T10:30:00Z',
    customer: { 
      id: '1', 
      first_name: 'John', 
      last_name: 'Doe', 
      email: '<EMAIL>', 
      phone: '******-0101',
      created_at: '', 
      updated_at: '' 
    },
    staff: { 
      id: '1', 
      first_name: 'Maria', 
      last_name: 'Garcia', 
      email: '<EMAIL>', 
      phone: '******-0202', 
      role: 'barber', 
      hire_date: '2021-03-10', 
      is_active: true, 
      created_at: '', 
      updated_at: '' 
    },
    service: { 
      id: '1', 
      name: 'Classic Haircut', 
      duration: 30, 
      price: 25.00, 
      category: 'haircut', 
      is_active: true, 
      created_at: '', 
      updated_at: '' 
    }
  },
  {
    id: '2',
    customer_id: '2',
    staff_id: '2',
    service_id: '2',
    appointment_date: new Date().toISOString().split('T')[0],
    start_time: '14:00',
    end_time: '15:00',
    status: 'in_progress',
    total_amount: 45.00,
    created_at: '2024-12-10T10:00:00Z',
    updated_at: '2024-12-15T14:00:00Z',
    customer: { 
      id: '2', 
      first_name: 'Jane', 
      last_name: 'Smith', 
      email: '<EMAIL>', 
      phone: '******-0102',
      created_at: '', 
      updated_at: '' 
    },
    staff: { 
      id: '2', 
      first_name: 'James', 
      last_name: 'Thompson', 
      email: '<EMAIL>', 
      phone: '******-0203', 
      role: 'stylist', 
      hire_date: '2021-06-20', 
      is_active: true, 
      created_at: '', 
      updated_at: '' 
    },
    service: { 
      id: '2', 
      name: 'Women\'s Cut & Style', 
      duration: 60, 
      price: 45.00, 
      category: 'styling', 
      is_active: true, 
      created_at: '', 
      updated_at: '' 
    }
  }
]
