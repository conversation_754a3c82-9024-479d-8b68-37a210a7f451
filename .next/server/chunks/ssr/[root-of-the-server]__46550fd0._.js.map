{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'\n\n// Only create Supabase client if proper credentials are provided\nconst isSupabaseConfigured =\n  supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseUrl !== 'https://demo.supabase.co' &&\n  supabaseAnonKey !== 'your_supabase_anon_key' &&\n  supabaseAnonKey !== 'demo-key' &&\n  supabaseUrl.startsWith('https://') &&\n  supabaseUrl.includes('.supabase.co')\n\nexport const supabase = isSupabaseConfigured\n  ? createClient(supabaseUrl, supabaseAnonKey)\n  : null\n\nexport function createSupabaseClient() {\n  if (!isSupabaseConfigured) return null\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Database helper functions\nconst createDbHelpers = () => {\n  if (!supabase) {\n    // Return mock functions when Supabase is not configured\n    return {\n      customers: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') }),\n        search: (query: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') })\n      },\n      staff: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getActive: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      services: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getActive: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      appointments: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getByDate: (date: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getByStaff: (staffId: string, date?: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      inventory: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getLowStock: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })\n      },\n      transactions: {\n        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        getByDateRange: (startDate: string, endDate: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),\n        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') })\n      }\n    }\n  }\n\n  return {\n    // Customers\n    customers: {\n      getAll: () => supabase.from('customers').select('*').order('created_at', { ascending: false }),\n      getById: (id: string) => supabase.from('customers').select('*').eq('id', id).single(),\n      create: (data: any) => supabase.from('customers').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('customers').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('customers').delete().eq('id', id),\n      search: (query: string) => supabase\n        .from('customers')\n        .select('*')\n        .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%,phone.ilike.%${query}%`)\n        .order('created_at', { ascending: false })\n    },\n\n    // Staff\n    staff: {\n      getAll: () => supabase.from('staff').select('*').order('created_at', { ascending: false }),\n      getById: (id: string) => supabase.from('staff').select('*').eq('id', id).single(),\n      getActive: () => supabase.from('staff').select('*').eq('is_active', true).order('first_name'),\n      create: (data: any) => supabase.from('staff').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('staff').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('staff').delete().eq('id', id),\n    },\n\n    // Services\n    services: {\n      getAll: () => supabase.from('services').select('*').order('name'),\n      getById: (id: string) => supabase.from('services').select('*').eq('id', id).single(),\n      getActive: () => supabase.from('services').select('*').eq('is_active', true).order('name'),\n      create: (data: any) => supabase.from('services').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('services').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('services').delete().eq('id', id),\n    },\n\n    // Appointments\n    appointments: {\n      getAll: () => supabase\n        .from('appointments')\n        .select(`\n          *,\n          customer:customers(*),\n          staff:staff(*),\n          service:services(*)\n        `)\n        .order('appointment_date', { ascending: false }),\n      getById: (id: string) => supabase\n        .from('appointments')\n        .select(`\n          *,\n          customer:customers(*),\n          staff:staff(*),\n          service:services(*)\n        `)\n        .eq('id', id)\n        .single(),\n      getByDate: (date: string) => supabase\n        .from('appointments')\n        .select(`\n          *,\n          customer:customers(*),\n          staff:staff(*),\n          service:services(*)\n        `)\n        .eq('appointment_date', date)\n        .order('start_time'),\n      getByStaff: (staffId: string, date?: string) => {\n        let query = supabase\n          .from('appointments')\n          .select(`\n            *,\n            customer:customers(*),\n            staff:staff(*),\n            service:services(*)\n          `)\n          .eq('staff_id', staffId)\n\n        if (date) {\n          query = query.eq('appointment_date', date)\n        }\n\n        return query.order('appointment_date', { ascending: false })\n      },\n      create: (data: any) => supabase.from('appointments').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('appointments').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('appointments').delete().eq('id', id),\n    },\n\n    // Inventory\n    inventory: {\n      getAll: () => supabase.from('inventory').select('*').order('name'),\n      getById: (id: string) => supabase.from('inventory').select('*').eq('id', id).single(),\n      getLowStock: () => supabase\n        .from('inventory')\n        .select('*')\n        .filter('current_stock', 'lte', 'min_stock_level')\n        .order('name'),\n      create: (data: any) => supabase.from('inventory').insert(data).select().single(),\n      update: (id: string, data: any) => supabase.from('inventory').update(data).eq('id', id).select().single(),\n      delete: (id: string) => supabase.from('inventory').delete().eq('id', id),\n    },\n\n    // Transactions\n    transactions: {\n      getAll: () => supabase.from('transactions').select('*').order('transaction_date', { ascending: false }),\n      getById: (id: string) => supabase.from('transactions').select('*').eq('id', id).single(),\n      getByDateRange: (startDate: string, endDate: string) => supabase\n        .from('transactions')\n        .select('*')\n        .gte('transaction_date', startDate)\n        .lte('transaction_date', endDate)\n        .order('transaction_date', { ascending: false }),\n      create: (data: any) => supabase.from('transactions').insert(data).select().single(),\n    }\n  }\n}\n\nexport const db = createDbHelpers()\n"], "names": [], "mappings": ";;;;;AACA;;;AAEA,MAAM,cAAc,iEAAwC;AAC5D,MAAM,kBAAkB,8DAA6C;AAErE,iEAAiE;AACjE,MAAM,uBACJ,eACA,mBACA,gBAAgB,+BAChB,gBAAgB,8BAChB,oBAAoB,4BACpB,oBAAoB,cACpB,YAAY,UAAU,CAAC,eACvB,YAAY,QAAQ,CAAC;AAEhB,MAAM,WAAW,6EAEpB;AAEG,SAAS;IACd,wCAA2B,OAAO;;AAEpC;AAEA,4BAA4B;AAC5B,MAAM,kBAAkB;IACtB,wCAAe;QACb,wDAAwD;QACxD,OAAO;YACL,WAAW;gBACT,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;gBACtF,QAAQ,CAAC,QAAkB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;YACvG;YACA,OAAO;gBACL,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,WAAW,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC3F,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,UAAU;gBACR,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,WAAW,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC3F,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,cAAc;gBACZ,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,WAAW,CAAC,OAAiB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACvG,YAAY,CAAC,SAAiB,OAAkB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC1H,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,WAAW;gBACT,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,aAAa,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7F,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACjG,QAAQ,CAAC,IAAY,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAC7G,QAAQ,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,OAAO,IAAI,MAAM;oBAA2B;YACxF;YACA,cAAc;gBACZ,QAAQ,IAAM,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACxF,SAAS,CAAC,KAAe,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBACnG,gBAAgB,CAAC,WAAmB,UAAoB,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;gBAClI,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM;oBAA2B;YACnG;QACF;IACF;;AAqHF;AAEO,MAAM,KAAK", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/contexts/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, userData?: any) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  resetPassword: (email: string) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if Supabase is configured\n    if (!supabase) {\n      // For demo purposes, create a mock user when Supa<PERSON> is not configured\n      const mockUser = {\n        id: 'demo-user',\n        email: '<EMAIL>',\n        user_metadata: { role: 'admin', full_name: 'Demo Admin' },\n        app_metadata: { role: 'admin' }\n      } as any\n\n      setUser(mockUser)\n      setSession({ user: mockUser } as any)\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session }, error } = await supabase.auth.getSession()\n      if (error) {\n        console.error('Error getting session:', error)\n      } else {\n        setSession(session)\n        setUser(session?.user ?? null)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session)\n        setSession(session)\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n\n      if (!supabase) {\n        // Demo mode - accept any credentials\n        const mockUser = {\n          id: 'demo-user',\n          email: email,\n          user_metadata: { role: 'admin', full_name: 'Demo Admin' },\n          app_metadata: { role: 'admin' }\n        } as any\n\n        setUser(mockUser)\n        setSession({ user: mockUser } as any)\n        return { error: null }\n      }\n\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) {\n        console.error('Sign in error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Sign in error:', error)\n      return { error }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string, userData?: any) => {\n    try {\n      setLoading(true)\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: userData,\n        },\n      })\n\n      if (error) {\n        console.error('Sign up error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Sign up error:', error)\n      return { error }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n\n      if (!supabase) {\n        // Demo mode - just clear the user\n        setUser(null)\n        setSession(null)\n        return\n      }\n\n      const { error } = await supabase.auth.signOut()\n      if (error) {\n        console.error('Sign out error:', error)\n      }\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) {\n        console.error('Reset password error:', error)\n        return { error }\n      }\n\n      return { error: null }\n    } catch (error) {\n      console.error('Reset password error:', error)\n      return { error }\n    }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Helper hook to check if user is admin\nexport function useIsAdmin() {\n  const { user } = useAuth()\n  \n  // Check if user has admin role in metadata or email domain\n  const isAdmin = user?.user_metadata?.role === 'admin' || \n                  user?.email?.endsWith('@royalcuts.com') ||\n                  user?.app_metadata?.role === 'admin'\n  \n  return isAdmin\n}\n\n// Helper hook to get user role\nexport function useUserRole() {\n  const { user } = useAuth()\n  \n  return user?.user_metadata?.role || \n         user?.app_metadata?.role || \n         'user'\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YACb,wEAAwE;YACxE,MAAM,WAAW;gBACf,IAAI;gBACJ,OAAO;gBACP,eAAe;oBAAE,MAAM;oBAAS,WAAW;gBAAa;gBACxD,cAAc;oBAAE,MAAM;gBAAQ;YAChC;YAEA,QAAQ;YACR,WAAW;gBAAE,MAAM;YAAS;YAC5B,WAAW;YACX;QACF;QAEA,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YACnE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,OAAO;gBACL,WAAW;gBACX,QAAQ,SAAS,QAAQ;YAC3B;YACA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO;YAC1C,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,qCAAqC;gBACrC,MAAM,WAAW;oBACf,IAAI;oBACJ,OAAO;oBACP,eAAe;wBAAE,MAAM;wBAAS,WAAW;oBAAa;oBACxD,cAAc;wBAAE,MAAM;oBAAQ;gBAChC;gBAEA,QAAQ;gBACR,WAAW;oBAAE,MAAM;gBAAS;gBAC5B,OAAO;oBAAE,OAAO;gBAAK;YACvB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE;YAAM;QACjB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;gBACR;YACF;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE;YAAM;QACjB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;gBACb,kCAAkC;gBAClC,QAAQ;gBACR,WAAW;gBACX;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mBAAmB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,2DAA2D;IAC3D,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;IAE7C,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,OAAO,MAAM,eAAe,QACrB,MAAM,cAAc,QACpB;AACT", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/settings.ts"], "sourcesContent": ["// Settings management for local storage\nexport interface BusinessSettings {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  timezone: string\n  currency: string\n}\n\nexport interface DayHours {\n  open: string\n  close: string\n  closed: boolean\n}\n\nexport interface OperatingHours {\n  [key: string]: DayHours\n}\n\nexport interface Holiday {\n  id: string\n  name: string\n  date: string // YYYY-MM-DD format\n  type: 'fixed' | 'lunar' | 'custom' // 固定日期、农历、自定义\n  recurring: boolean // 是否每年重复\n  closed: boolean // 是否休息\n  hours?: DayHours // 如果不休息，特殊营业时间\n  description?: string\n  source?: 'manual' | 'api' | 'wannianli' | 'china_holiday' | 'tianapi' | 'juhe' // 数据来源\n}\n\nexport interface HolidaySettings {\n  holidays: Holiday[]\n  enableHolidayMode: boolean // 是否启用节假日模式\n}\n\nexport interface NotificationSettings {\n  emailNotifications: boolean\n  smsNotifications: boolean\n  appointmentReminders: boolean\n  lowStockAlerts: boolean\n  dailyReports: boolean\n  weeklyReports: boolean\n}\n\nexport interface AppSettings {\n  business: BusinessSettings\n  operatingHours: OperatingHours\n  holidays: HolidaySettings\n  notifications: NotificationSettings\n  theme: string\n  language: string\n}\n\nconst DEFAULT_SETTINGS: AppSettings = {\n  business: {\n    name: '皇家理发店',\n    address: '北京市朝阳区三里屯街道1号',\n    phone: '+86 138-0013-8000',\n    email: '<EMAIL>',\n    website: 'www.royalcuts.cn',\n    timezone: 'Asia/Shanghai',\n    currency: 'CNY'\n  },\n  operatingHours: {\n    monday: { open: '09:00', close: '18:00', closed: false },\n    tuesday: { open: '09:00', close: '18:00', closed: false },\n    wednesday: { open: '09:00', close: '18:00', closed: false },\n    thursday: { open: '09:00', close: '19:00', closed: false },\n    friday: { open: '09:00', close: '19:00', closed: false },\n    saturday: { open: '08:00', close: '17:00', closed: false },\n    sunday: { open: '10:00', close: '16:00', closed: false }\n  },\n  holidays: {\n    enableHolidayMode: true,\n    holidays: [\n      {\n        id: 'new-year',\n        name: '元旦',\n        date: '2024-01-01',\n        type: 'fixed',\n        recurring: true,\n        closed: true,\n        description: '新年第一天',\n        source: 'manual'\n      },\n      {\n        id: 'spring-festival',\n        name: '春节',\n        date: '2024-02-10',\n        type: 'lunar',\n        recurring: true,\n        closed: true,\n        description: '农历新年',\n        source: 'manual'\n      },\n      {\n        id: 'labor-day',\n        name: '劳动节',\n        date: '2024-05-01',\n        type: 'fixed',\n        recurring: true,\n        closed: true,\n        description: '国际劳动节',\n        source: 'manual'\n      },\n      {\n        id: 'national-day',\n        name: '国庆节',\n        date: '2024-10-01',\n        type: 'fixed',\n        recurring: true,\n        closed: true,\n        description: '中华人民共和国国庆节',\n        source: 'manual'\n      }\n    ]\n  },\n  notifications: {\n    emailNotifications: true,\n    smsNotifications: false,\n    appointmentReminders: true,\n    lowStockAlerts: true,\n    dailyReports: false,\n    weeklyReports: true\n  },\n  theme: 'royal-gold',\n  language: 'zh-CN'\n}\n\nconst SETTINGS_KEY = 'barbershop_settings'\n\nexport class SettingsManager {\n  static getSettings(): AppSettings {\n    if (typeof window === 'undefined') {\n      return DEFAULT_SETTINGS\n    }\n\n    try {\n      const stored = localStorage.getItem(SETTINGS_KEY)\n      if (stored) {\n        const parsed = JSON.parse(stored)\n        // Merge with defaults to ensure all properties exist\n        return {\n          ...DEFAULT_SETTINGS,\n          ...parsed,\n          business: { ...DEFAULT_SETTINGS.business, ...parsed.business },\n          operatingHours: { ...DEFAULT_SETTINGS.operatingHours, ...parsed.operatingHours },\n          holidays: { ...DEFAULT_SETTINGS.holidays, ...parsed.holidays },\n          notifications: { ...DEFAULT_SETTINGS.notifications, ...parsed.notifications }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading settings:', error)\n    }\n\n    return DEFAULT_SETTINGS\n  }\n\n  static saveSettings(settings: AppSettings): void {\n    if (typeof window === 'undefined') {\n      return\n    }\n\n    try {\n      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings))\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      throw new Error('无法保存设置')\n    }\n  }\n\n  static updateBusinessSettings(business: BusinessSettings): void {\n    const settings = this.getSettings()\n    settings.business = business\n    this.saveSettings(settings)\n  }\n\n  static updateOperatingHours(operatingHours: OperatingHours): void {\n    const settings = this.getSettings()\n    settings.operatingHours = operatingHours\n    this.saveSettings(settings)\n  }\n\n  static updateHolidaySettings(holidays: HolidaySettings): void {\n    const settings = this.getSettings()\n    settings.holidays = holidays\n    this.saveSettings(settings)\n  }\n\n  static updateNotificationSettings(notifications: NotificationSettings): void {\n    const settings = this.getSettings()\n    settings.notifications = notifications\n    this.saveSettings(settings)\n  }\n\n  static updateTheme(theme: string): void {\n    const settings = this.getSettings()\n    settings.theme = theme\n    this.saveSettings(settings)\n  }\n\n  static resetToDefaults(): void {\n    this.saveSettings(DEFAULT_SETTINGS)\n  }\n\n  static exportSettings(): string {\n    const settings = this.getSettings()\n    return JSON.stringify(settings, null, 2)\n  }\n\n  static importSettings(settingsJson: string): void {\n    try {\n      const imported = JSON.parse(settingsJson)\n      // Validate the structure\n      if (imported.business && imported.operatingHours && imported.notifications) {\n        // Ensure holidays exist, use default if not\n        if (!imported.holidays) {\n          imported.holidays = DEFAULT_SETTINGS.holidays\n        }\n        this.saveSettings(imported)\n      } else {\n        throw new Error('Invalid settings format')\n      }\n    } catch (error) {\n      console.error('Error importing settings:', error)\n      throw new Error('无效的设置格式')\n    }\n  }\n}\n\n// Utility functions for formatting\nexport function formatOperatingHours(hours: OperatingHours): string {\n  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']\n  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\n  \n  return days.map((day, index) => {\n    const dayHours = hours[day]\n    if (dayHours.closed) {\n      return `${dayNames[index]}: 休息`\n    }\n    return `${dayNames[index]}: ${dayHours.open} - ${dayHours.close}`\n  }).join('\\n')\n}\n\nexport function validateBusinessSettings(business: BusinessSettings): string[] {\n  const errors: string[] = []\n  \n  if (!business.name.trim()) {\n    errors.push('商户名称不能为空')\n  }\n  \n  if (!business.phone.trim()) {\n    errors.push('联系电话不能为空')\n  }\n  \n  if (!business.email.trim()) {\n    errors.push('邮箱地址不能为空')\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(business.email)) {\n    errors.push('邮箱地址格式不正确')\n  }\n  \n  if (!business.address.trim()) {\n    errors.push('商户地址不能为空')\n  }\n  \n  return errors\n}\n\nexport function validateOperatingHours(hours: OperatingHours): string[] {\n  const errors: string[] = []\n  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']\n  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\n  \n  days.forEach((day, index) => {\n    const dayHours = hours[day]\n    if (!dayHours.closed) {\n      if (!dayHours.open || !dayHours.close) {\n        errors.push(`${dayNames[index]}的营业时间不完整`)\n      } else if (dayHours.open >= dayHours.close) {\n        errors.push(`${dayNames[index]}的开始时间必须早于结束时间`)\n      }\n    }\n  })\n  \n  return errors\n}\n\n// 主题配置\nexport interface ThemeConfig {\n  id: string\n  name: string\n  description: string\n  colors: {\n    primary: string\n    primaryForeground: string\n    secondary?: string\n    accent?: string\n    background?: string\n    foreground?: string\n    muted?: string\n    border?: string\n  }\n  preview: {\n    gradient: string\n    textColor: string\n  }\n}\n\nexport const AVAILABLE_THEMES: ThemeConfig[] = [\n  {\n    id: 'royal-gold',\n    name: '皇家金色',\n    description: '经典奢华的金色主题，彰显专业品质',\n    colors: {\n      primary: '45 93% 47%', // 金色\n      primaryForeground: '0 0% 98%',\n      secondary: '45 84% 60%',\n      accent: '45 84% 60%',\n      background: '0 0% 100%',\n      foreground: '0 0% 3.9%',\n      muted: '0 0% 96.1%',\n      border: '0 0% 89.8%'\n    },\n    preview: {\n      gradient: 'from-amber-600 to-amber-800',\n      textColor: 'text-amber-700'\n    }\n  },\n  {\n    id: 'ocean-blue',\n    name: '海洋蓝',\n    description: '清新的蓝色主题，营造宁静专业氛围',\n    colors: {\n      primary: '217 91% 60%', // 蓝色\n      primaryForeground: '0 0% 98%',\n      secondary: '217 84% 70%',\n      accent: '217 84% 70%',\n      background: '0 0% 100%',\n      foreground: '0 0% 3.9%',\n      muted: '0 0% 96.1%',\n      border: '0 0% 89.8%'\n    },\n    preview: {\n      gradient: 'from-blue-600 to-blue-800',\n      textColor: 'text-blue-700'\n    }\n  },\n  {\n    id: 'forest-green',\n    name: '森林绿',\n    description: '自然的绿色主题，传达健康活力理念',\n    colors: {\n      primary: '142 76% 36%', // 绿色\n      primaryForeground: '0 0% 98%',\n      secondary: '142 70% 45%',\n      accent: '142 70% 45%',\n      background: '0 0% 100%',\n      foreground: '0 0% 3.9%',\n      muted: '0 0% 96.1%',\n      border: '0 0% 89.8%'\n    },\n    preview: {\n      gradient: 'from-emerald-600 to-emerald-800',\n      textColor: 'text-emerald-700'\n    }\n  },\n  {\n    id: 'sunset-orange',\n    name: '日落橙',\n    description: '温暖的橙色主题，营造友好亲切氛围',\n    colors: {\n      primary: '24 95% 53%', // 橙色\n      primaryForeground: '0 0% 98%',\n      secondary: '24 90% 60%',\n      accent: '24 90% 60%',\n      background: '0 0% 100%',\n      foreground: '0 0% 3.9%',\n      muted: '0 0% 96.1%',\n      border: '0 0% 89.8%'\n    },\n    preview: {\n      gradient: 'from-orange-600 to-red-600',\n      textColor: 'text-orange-700'\n    }\n  },\n  {\n    id: 'purple-luxury',\n    name: '紫色奢华',\n    description: '高贵的紫色主题，展现独特品味',\n    colors: {\n      primary: '262 83% 58%', // 紫色\n      primaryForeground: '0 0% 98%',\n      secondary: '262 75% 65%',\n      accent: '262 75% 65%',\n      background: '0 0% 100%',\n      foreground: '0 0% 3.9%',\n      muted: '0 0% 96.1%',\n      border: '0 0% 89.8%'\n    },\n    preview: {\n      gradient: 'from-purple-600 to-purple-800',\n      textColor: 'text-purple-700'\n    }\n  },\n  {\n    id: 'rose-elegant',\n    name: '玫瑰雅致',\n    description: '优雅的玫瑰色主题，适合高端美容院',\n    colors: {\n      primary: '330 81% 60%', // 玫瑰色\n      primaryForeground: '0 0% 98%',\n      secondary: '330 75% 70%',\n      accent: '330 75% 70%',\n      background: '0 0% 100%',\n      foreground: '0 0% 3.9%',\n      muted: '0 0% 96.1%',\n      border: '0 0% 89.8%'\n    },\n    preview: {\n      gradient: 'from-rose-600 to-pink-700',\n      textColor: 'text-rose-700'\n    }\n  }\n]\n\n// Theme utilities\nexport function applyTheme(themeId: string): void {\n  if (typeof document === 'undefined') return\n\n  const theme = AVAILABLE_THEMES.find(t => t.id === themeId)\n  if (!theme) return\n\n  const root = document.documentElement\n\n  // 应用主题颜色\n  Object.entries(theme.colors).forEach(([key, value]) => {\n    const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase()\n    root.style.setProperty(`--${cssVar}`, value)\n  })\n\n  // 确保所有必要的CSS变量都被设置\n  const essentialVars = {\n    '--background': theme.colors.background || '0 0% 100%',\n    '--foreground': theme.colors.foreground || '0 0% 3.9%',\n    '--muted': theme.colors.muted || '0 0% 96.1%',\n    '--muted-foreground': '0 0% 45.1%',\n    '--popover': theme.colors.background || '0 0% 100%',\n    '--popover-foreground': theme.colors.foreground || '0 0% 3.9%',\n    '--card': theme.colors.background || '0 0% 100%',\n    '--card-foreground': theme.colors.foreground || '0 0% 3.9%',\n    '--border': theme.colors.border || '0 0% 89.8%',\n    '--input': theme.colors.border || '0 0% 89.8%',\n    '--ring': theme.colors.primary || '0 0% 3.9%',\n    '--radius': '0.5rem'\n  }\n\n  Object.entries(essentialVars).forEach(([key, value]) => {\n    root.style.setProperty(key, value)\n  })\n\n  // 强制重新渲染\n  root.style.display = 'none'\n  root.offsetHeight // 触发重排\n  root.style.display = ''\n}\n\nexport function getThemeConfig(themeId: string): ThemeConfig | undefined {\n  return AVAILABLE_THEMES.find(t => t.id === themeId)\n}\n\n// 初始化主题\nexport function initializeTheme(): void {\n  if (typeof document === 'undefined') return\n\n  const settings = SettingsManager.getSettings()\n  const theme = settings.theme || 'royal-gold'\n  applyTheme(theme)\n}\n\n// 重置主题到默认状态\nexport function resetTheme(): void {\n  if (typeof document === 'undefined') return\n\n  const root = document.documentElement\n\n  // 重置所有主题相关的CSS变量\n  const defaultVars = {\n    '--background': '0 0% 100%',\n    '--foreground': '0 0% 3.9%',\n    '--card': '0 0% 100%',\n    '--card-foreground': '0 0% 3.9%',\n    '--popover': '0 0% 100%',\n    '--popover-foreground': '0 0% 3.9%',\n    '--primary': '0 0% 9%',\n    '--primary-foreground': '0 0% 98%',\n    '--secondary': '0 0% 96.1%',\n    '--secondary-foreground': '0 0% 9%',\n    '--muted': '0 0% 96.1%',\n    '--muted-foreground': '0 0% 45.1%',\n    '--accent': '0 0% 96.1%',\n    '--accent-foreground': '0 0% 9%',\n    '--destructive': '0 84.2% 60.2%',\n    '--destructive-foreground': '0 0% 98%',\n    '--border': '0 0% 89.8%',\n    '--input': '0 0% 89.8%',\n    '--ring': '0 0% 3.9%',\n    '--radius': '0.5rem'\n  }\n\n  Object.entries(defaultVars).forEach(([key, value]) => {\n    root.style.setProperty(key, value)\n  })\n}\n\n\n\n// Holiday management utilities\nexport function generateHolidayId(): string {\n  return 'holiday-' + Math.random().toString(36).substr(2, 9)\n}\n\nexport function isHolidayToday(holidays: Holiday[]): Holiday | null {\n  const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD\n  return holidays.find(holiday => {\n    if (!holiday.recurring) {\n      return holiday.date === today\n    }\n\n    // For recurring holidays, check if month and day match\n    const holidayDate = new Date(holiday.date)\n    const todayDate = new Date(today)\n\n    if (holiday.type === 'fixed') {\n      return holidayDate.getMonth() === todayDate.getMonth() &&\n             holidayDate.getDate() === todayDate.getDate()\n    }\n\n    // For lunar holidays, would need lunar calendar conversion\n    // For now, just check exact date\n    return holiday.date === today\n  }) || null\n}\n\nexport function getEffectiveHours(\n  date: string,\n  operatingHours: OperatingHours,\n  holidaySettings: HolidaySettings\n): DayHours | null {\n  if (!holidaySettings.enableHolidayMode) {\n    // If holiday mode is disabled, use regular hours\n    const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' })\n    return operatingHours[dayOfWeek] || null\n  }\n\n  // Check if date is a holiday\n  const holiday = holidaySettings.holidays.find(h => {\n    if (!h.recurring) {\n      return h.date === date\n    }\n\n    const holidayDate = new Date(h.date)\n    const checkDate = new Date(date)\n\n    if (h.type === 'fixed') {\n      return holidayDate.getMonth() === checkDate.getMonth() &&\n             holidayDate.getDate() === checkDate.getDate()\n    }\n\n    return h.date === date\n  })\n\n  if (holiday) {\n    if (holiday.closed) {\n      return { open: '', close: '', closed: true }\n    }\n    if (holiday.hours) {\n      return holiday.hours\n    }\n  }\n\n  // Use regular operating hours\n  const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' })\n  return operatingHours[dayOfWeek] || null\n}\n\nexport function validateHoliday(holiday: Partial<Holiday>): string[] {\n  const errors: string[] = []\n\n  if (!holiday.name?.trim()) {\n    errors.push('节假日名称不能为空')\n  }\n\n  if (!holiday.date) {\n    errors.push('请选择日期')\n  } else {\n    const date = new Date(holiday.date)\n    if (isNaN(date.getTime())) {\n      errors.push('日期格式不正确')\n    }\n  }\n\n  if (!holiday.type) {\n    errors.push('请选择节假日类型')\n  }\n\n  if (!holiday.closed && holiday.hours) {\n    if (!holiday.hours.open || !holiday.hours.close) {\n      errors.push('请设置营业时间')\n    } else if (holiday.hours.open >= holiday.hours.close) {\n      errors.push('开始时间必须早于结束时间')\n    }\n  }\n\n  return errors\n}\n\nexport function formatHolidayDate(date: string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function getHolidayTypeLabel(type: string): string {\n  switch (type) {\n    case 'fixed': return '固定日期'\n    case 'lunar': return '农历日期'\n    case 'custom': return '自定义'\n    default: return type\n  }\n}\n\nexport function getUpcomingHolidays(holidays: Holiday[], days: number = 30): Holiday[] {\n  const today = new Date()\n  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000)\n\n  return holidays.filter(holiday => {\n    const holidayDate = new Date(holiday.date)\n\n    if (!holiday.recurring) {\n      return holidayDate >= today && holidayDate <= futureDate\n    }\n\n    // For recurring holidays, check if they occur within the next 'days' period\n    if (holiday.type === 'fixed') {\n      const thisYear = new Date(today.getFullYear(), holidayDate.getMonth(), holidayDate.getDate())\n      const nextYear = new Date(today.getFullYear() + 1, holidayDate.getMonth(), holidayDate.getDate())\n\n      return (thisYear >= today && thisYear <= futureDate) ||\n             (nextYear >= today && nextYear <= futureDate)\n    }\n\n    return false\n  }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;;;;;;;;;AAwDxC,MAAM,mBAAgC;IACpC,UAAU;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA,gBAAgB;QACd,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,SAAS;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACxD,WAAW;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QAC1D,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;IACzD;IACA,UAAU;QACR,mBAAmB;QACnB,UAAU;YACR;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,aAAa;gBACb,QAAQ;YACV;SACD;IACH;IACA,eAAe;QACb,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,eAAe;IACjB;IACA,OAAO;IACP,UAAU;AACZ;AAEA,MAAM,eAAe;AAEd,MAAM;IACX,OAAO,cAA2B;QAChC,wCAAmC;YACjC,OAAO;QACT;;IAqBF;IAEA,OAAO,aAAa,QAAqB,EAAQ;QAC/C,wCAAmC;YACjC;QACF;;IAQF;IAEA,OAAO,uBAAuB,QAA0B,EAAQ;QAC9D,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,QAAQ,GAAG;QACpB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,qBAAqB,cAA8B,EAAQ;QAChE,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,cAAc,GAAG;QAC1B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,sBAAsB,QAAyB,EAAQ;QAC5D,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,QAAQ,GAAG;QACpB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,2BAA2B,aAAmC,EAAQ;QAC3E,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,aAAa,GAAG;QACzB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,YAAY,KAAa,EAAQ;QACtC,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,KAAK,GAAG;QACjB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,kBAAwB;QAC7B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,iBAAyB;QAC9B,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO,KAAK,SAAS,CAAC,UAAU,MAAM;IACxC;IAEA,OAAO,eAAe,YAAoB,EAAQ;QAChD,IAAI;YACF,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,yBAAyB;YACzB,IAAI,SAAS,QAAQ,IAAI,SAAS,cAAc,IAAI,SAAS,aAAa,EAAE;gBAC1E,4CAA4C;gBAC5C,IAAI,CAAC,SAAS,QAAQ,EAAE;oBACtB,SAAS,QAAQ,GAAG,iBAAiB,QAAQ;gBAC/C;gBACA,IAAI,CAAC,YAAY,CAAC;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,SAAS,qBAAqB,KAAqB;IACxD,MAAM,OAAO;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAC3F,MAAM,WAAW;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE3D,OAAO,KAAK,GAAG,CAAC,CAAC,KAAK;QACpB,MAAM,WAAW,KAAK,CAAC,IAAI;QAC3B,IAAI,SAAS,MAAM,EAAE;YACnB,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;QACjC;QACA,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,SAAS,KAAK,EAAE;IACnE,GAAG,IAAI,CAAC;AACV;AAEO,SAAS,yBAAyB,QAA0B;IACjE,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;QAC1B,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;QAC7D,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAEO,SAAS,uBAAuB,KAAqB;IAC1D,MAAM,SAAmB,EAAE;IAC3B,MAAM,OAAO;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAC3F,MAAM,WAAW;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE3D,KAAK,OAAO,CAAC,CAAC,KAAK;QACjB,MAAM,WAAW,KAAK,CAAC,IAAI;QAC3B,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;gBACrC,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC1C,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;gBAC1C,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;YAC/C;QACF;IACF;IAEA,OAAO;AACT;AAuBO,MAAM,mBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;YACN,SAAS;YACT,mBAAmB;YACnB,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,QAAQ;QACV;QACA,SAAS;YACP,UAAU;YACV,WAAW;QACb;IACF;CACD;AAGM,SAAS,WAAW,OAAe;IACxC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,QAAQ,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAClD,IAAI,CAAC,OAAO;IAEZ,MAAM,OAAO,SAAS,eAAe;IAErC,SAAS;IACT,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAChD,MAAM,SAAS,IAAI,OAAO,CAAC,YAAY,OAAO,WAAW;QACzD,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE;IACxC;IAEA,mBAAmB;IACnB,MAAM,gBAAgB;QACpB,gBAAgB,MAAM,MAAM,CAAC,UAAU,IAAI;QAC3C,gBAAgB,MAAM,MAAM,CAAC,UAAU,IAAI;QAC3C,WAAW,MAAM,MAAM,CAAC,KAAK,IAAI;QACjC,sBAAsB;QACtB,aAAa,MAAM,MAAM,CAAC,UAAU,IAAI;QACxC,wBAAwB,MAAM,MAAM,CAAC,UAAU,IAAI;QACnD,UAAU,MAAM,MAAM,CAAC,UAAU,IAAI;QACrC,qBAAqB,MAAM,MAAM,CAAC,UAAU,IAAI;QAChD,YAAY,MAAM,MAAM,CAAC,MAAM,IAAI;QACnC,WAAW,MAAM,MAAM,CAAC,MAAM,IAAI;QAClC,UAAU,MAAM,MAAM,CAAC,OAAO,IAAI;QAClC,YAAY;IACd;IAEA,OAAO,OAAO,CAAC,eAAe,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACjD,KAAK,KAAK,CAAC,WAAW,CAAC,KAAK;IAC9B;IAEA,SAAS;IACT,KAAK,KAAK,CAAC,OAAO,GAAG;IACrB,KAAK,YAAY,CAAC,OAAO;;IACzB,KAAK,KAAK,CAAC,OAAO,GAAG;AACvB;AAEO,SAAS,eAAe,OAAe;IAC5C,OAAO,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AAC7C;AAGO,SAAS;IACd,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,WAAW,gBAAgB,WAAW;IAC5C,MAAM,QAAQ,SAAS,KAAK,IAAI;IAChC,WAAW;AACb;AAGO,SAAS;IACd,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,OAAO,SAAS,eAAe;IAErC,iBAAiB;IACjB,MAAM,cAAc;QAClB,gBAAgB;QAChB,gBAAgB;QAChB,UAAU;QACV,qBAAqB;QACrB,aAAa;QACb,wBAAwB;QACxB,aAAa;QACb,wBAAwB;QACxB,eAAe;QACf,0BAA0B;QAC1B,WAAW;QACX,sBAAsB;QACtB,YAAY;QACZ,uBAAuB;QACvB,iBAAiB;QACjB,4BAA4B;QAC5B,YAAY;QACZ,WAAW;QACX,UAAU;QACV,YAAY;IACd;IAEA,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC/C,KAAK,KAAK,CAAC,WAAW,CAAC,KAAK;IAC9B;AACF;AAKO,SAAS;IACd,OAAO,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC3D;AAEO,SAAS,eAAe,QAAmB;IAChD,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa;;IAClE,OAAO,SAAS,IAAI,CAAC,CAAA;QACnB,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,OAAO,QAAQ,IAAI,KAAK;QAC1B;QAEA,uDAAuD;QACvD,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;QACzC,MAAM,YAAY,IAAI,KAAK;QAE3B,IAAI,QAAQ,IAAI,KAAK,SAAS;YAC5B,OAAO,YAAY,QAAQ,OAAO,UAAU,QAAQ,MAC7C,YAAY,OAAO,OAAO,UAAU,OAAO;QACpD;QAEA,2DAA2D;QAC3D,iCAAiC;QACjC,OAAO,QAAQ,IAAI,KAAK;IAC1B,MAAM;AACR;AAEO,SAAS,kBACd,IAAY,EACZ,cAA8B,EAC9B,eAAgC;IAEhC,IAAI,CAAC,gBAAgB,iBAAiB,EAAE;QACtC,iDAAiD;QACjD,MAAM,YAAY,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAY;QACpF,OAAO,cAAc,CAAC,UAAU,IAAI;IACtC;IAEA,6BAA6B;IAC7B,MAAM,UAAU,gBAAgB,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,EAAE,SAAS,EAAE;YAChB,OAAO,EAAE,IAAI,KAAK;QACpB;QAEA,MAAM,cAAc,IAAI,KAAK,EAAE,IAAI;QACnC,MAAM,YAAY,IAAI,KAAK;QAE3B,IAAI,EAAE,IAAI,KAAK,SAAS;YACtB,OAAO,YAAY,QAAQ,OAAO,UAAU,QAAQ,MAC7C,YAAY,OAAO,OAAO,UAAU,OAAO;QACpD;QAEA,OAAO,EAAE,IAAI,KAAK;IACpB;IAEA,IAAI,SAAS;QACX,IAAI,QAAQ,MAAM,EAAE;YAClB,OAAO;gBAAE,MAAM;gBAAI,OAAO;gBAAI,QAAQ;YAAK;QAC7C;QACA,IAAI,QAAQ,KAAK,EAAE;YACjB,OAAO,QAAQ,KAAK;QACtB;IACF;IAEA,8BAA8B;IAC9B,MAAM,YAAY,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAAE,SAAS;IAAY;IACpF,OAAO,cAAc,CAAC,UAAU,IAAI;AACtC;AAEO,SAAS,gBAAgB,OAAyB;IACvD,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,QAAQ,IAAI,EAAE,QAAQ;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC;IACd,OAAO;QACL,MAAM,OAAO,IAAI,KAAK,QAAQ,IAAI;QAClC,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,IAAI,CAAC,QAAQ,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,MAAM,IAAI,QAAQ,KAAK,EAAE;QACpC,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,KAAK,EAAE;YAC/C,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,QAAQ,KAAK,CAAC,IAAI,IAAI,QAAQ,KAAK,CAAC,KAAK,EAAE;YACpD,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;AACT;AAEO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YAAS,OAAO;QACrB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAU,OAAO;QACtB;YAAS,OAAO;IAClB;AACF;AAEO,SAAS,oBAAoB,QAAmB,EAAE,OAAe,EAAE;IACxE,MAAM,QAAQ,IAAI;IAClB,MAAM,aAAa,IAAI,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;IAEpE,OAAO,SAAS,MAAM,CAAC,CAAA;QACrB,MAAM,cAAc,IAAI,KAAK,QAAQ,IAAI;QAEzC,IAAI,CAAC,QAAQ,SAAS,EAAE;YACtB,OAAO,eAAe,SAAS,eAAe;QAChD;QAEA,4EAA4E;QAC5E,IAAI,QAAQ,IAAI,KAAK,SAAS;YAC5B,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,IAAI,YAAY,QAAQ,IAAI,YAAY,OAAO;YAC1F,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,YAAY,QAAQ,IAAI,YAAY,OAAO;YAE9F,OAAO,AAAC,YAAY,SAAS,YAAY,cACjC,YAAY,SAAS,YAAY;QAC3C;QAEA,OAAO;IACT,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;AACzE", "debugId": null}}, {"offset": {"line": 1079, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/theme-initializer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { initializeTheme } from '@/lib/settings'\n\nexport function ThemeInitializer() {\n  useEffect(() => {\n    // Initialize theme on client side\n    initializeTheme()\n  }, [])\n\n  // This component doesn't render anything\n  return null\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD;IAChB,GAAG,EAAE;IAEL,yCAAyC;IACzC,OAAO;AACT", "debugId": null}}]}