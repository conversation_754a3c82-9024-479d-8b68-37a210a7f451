'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader, PageHeaderActions } from '@/components/layout/page-header'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Users,
  Calendar,
  Clock,
  Scissors,
  Download,
  Filter,
  Eye,
  Target,
  Award,
  Star
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

export default function AnalyticsPage() {
  const [dateRange, setDateRange] = useState('30')
  const [loading, setLoading] = useState(false)

  // Mock analytics data
  const analytics = {
    revenue: {
      total: 12450.00,
      change: 15.2,
      trend: 'up'
    },
    appointments: {
      total: 156,
      change: 8.5,
      trend: 'up'
    },
    customers: {
      total: 89,
      new: 12,
      change: 12.3,
      trend: 'up'
    },
    avgTicket: {
      value: 79.81,
      change: -2.1,
      trend: 'down'
    }
  }

  const topServices = [
    { name: 'Classic Haircut', bookings: 45, revenue: 1125.00, growth: 12 },
    { name: 'Women\'s Cut & Style', bookings: 32, revenue: 1440.00, growth: 8 },
    { name: 'Beard Trim', bookings: 28, revenue: 420.00, growth: 15 },
    { name: 'Full Color', bookings: 18, revenue: 1530.00, growth: -5 },
    { name: 'Highlights', bookings: 15, revenue: 1425.00, growth: 22 }
  ]

  const topStaff = [
    { name: 'Maria Garcia', appointments: 42, revenue: 3150.00, rating: 4.9 },
    { name: 'James Thompson', appointments: 38, revenue: 2850.00, rating: 4.8 },
    { name: 'Sofia Martinez', appointments: 35, revenue: 2625.00, rating: 4.7 },
    { name: 'Alex Rodriguez', appointments: 28, revenue: 2100.00, rating: 4.6 }
  ]

  const recentTrends = [
    { metric: 'Revenue', value: '+15.2%', status: 'positive' },
    { metric: 'New Customers', value: '+12.3%', status: 'positive' },
    { metric: 'Avg. Ticket', value: '-2.1%', status: 'negative' },
    { metric: 'Cancellations', value: '-8.5%', status: 'positive' },
    { metric: 'No-shows', value: '-12.1%', status: 'positive' }
  ]

  const handleExportReport = () => {
    console.log('Export report')
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="Analytics & Reports"
          description="Business insights, performance metrics, and financial reports"
          icon={<BarChart3 className="h-6 w-6 text-white" />}
          actions={
            <PageHeaderActions>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 3 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" onClick={handleExportReport}>
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            </PageHeaderActions>
          }
        />

        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                    <p className="text-2xl font-bold">{formatCurrency(analytics.revenue.total)}</p>
                    <div className="flex items-center mt-1">
                      <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600">+{analytics.revenue.change}%</span>
                    </div>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Appointments</p>
                    <p className="text-2xl font-bold">{analytics.appointments.total}</p>
                    <div className="flex items-center mt-1">
                      <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600">+{analytics.appointments.change}%</span>
                    </div>
                  </div>
                  <Calendar className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Customers</p>
                    <p className="text-2xl font-bold">{analytics.customers.total}</p>
                    <div className="flex items-center mt-1">
                      <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs text-green-600">+{analytics.customers.new} new</span>
                    </div>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Avg. Ticket</p>
                    <p className="text-2xl font-bold">{formatCurrency(analytics.avgTicket.value)}</p>
                    <div className="flex items-center mt-1">
                      <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                      <span className="text-xs text-red-600">{analytics.avgTicket.change}%</span>
                    </div>
                  </div>
                  <Target className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts and Detailed Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Services */}
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Scissors className="h-5 w-5 mr-2" />
                  Top Services
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topServices.map((service, index) => (
                    <div key={service.name} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                      <div className="flex items-center space-x-3">
                        <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center">
                          <span className="text-white text-sm font-bold">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{service.name}</p>
                          <p className="text-sm text-muted-foreground">{service.bookings} bookings</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{formatCurrency(service.revenue)}</p>
                        <div className="flex items-center">
                          {service.growth > 0 ? (
                            <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                          )}
                          <span className={`text-xs ${service.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {service.growth > 0 ? '+' : ''}{service.growth}%
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Staff Performance */}
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2" />
                  Staff Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topStaff.map((staff, index) => (
                    <div key={staff.name} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                      <div className="flex items-center space-x-3">
                        <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center">
                          <span className="text-white text-sm font-bold">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{staff.name}</p>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground">{staff.appointments} appointments</span>
                            <div className="flex items-center">
                              <Star className="h-3 w-3 text-yellow-500 mr-1" />
                              <span className="text-xs text-muted-foreground">{staff.rating}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{formatCurrency(staff.revenue)}</p>
                        <Badge variant="success" className="text-xs">
                          Top Performer
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Trends and Insights */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Trends */}
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Recent Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentTrends.map((trend) => (
                    <div key={trend.metric} className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">{trend.metric}</span>
                      <Badge 
                        variant={trend.status === 'positive' ? 'success' : 'destructive'}
                        className="text-xs"
                      >
                        {trend.value}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Insights */}
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Eye className="h-5 w-5 mr-2" />
                  Quick Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/30">
                    <p className="font-medium text-blue-700 dark:text-blue-300">Peak Hours</p>
                    <p className="text-blue-600 dark:text-blue-400">2:00 PM - 4:00 PM on weekends</p>
                  </div>
                  <div className="p-3 rounded-lg bg-green-50 dark:bg-green-950/30">
                    <p className="font-medium text-green-700 dark:text-green-300">Best Day</p>
                    <p className="text-green-600 dark:text-green-400">Saturdays generate 35% more revenue</p>
                  </div>
                  <div className="p-3 rounded-lg bg-purple-50 dark:bg-purple-950/30">
                    <p className="font-medium text-purple-700 dark:text-purple-300">Customer Retention</p>
                    <p className="text-purple-600 dark:text-purple-400">78% return within 6 weeks</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Goals & Targets */}
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Monthly Goals
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Revenue Target</span>
                      <span>83%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '83%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatCurrency(12450)} / {formatCurrency(15000)}
                    </p>
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>New Customers</span>
                      <span>60%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">12 / 20 new customers</p>
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Appointments</span>
                      <span>78%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="bg-purple-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">156 / 200 appointments</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
