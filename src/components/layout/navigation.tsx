'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { 
  Calendar, 
  Users, 
  Scissors, 
  UserCheck, 
  Package, 
  BarChart3, 
  Settings,
  Home
} from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Appointments', href: '/appointments', icon: Calendar },
  { name: 'Customers', href: '/customers', icon: Users },
  { name: 'Staff', href: '/staff', icon: UserCheck },
  { name: 'Services', href: '/services', icon: Scissors },
  { name: 'Inventory', href: '/inventory', icon: Package },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export function Navigation() {
  const pathname = usePathname()

  return (
    <nav className="flex flex-col space-y-2">
      {navigation.map((item, index) => {
        const isActive = pathname === item.href
        return (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',
              isActive
                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'
                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'
            )}
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <item.icon className={cn(
              "mr-3 h-5 w-5 transition-all duration-200",
              isActive
                ? "text-primary scale-110"
                : "text-muted-foreground group-hover:text-primary group-hover:scale-105"
            )} />
            <span className="font-medium">{item.name}</span>
            {isActive && (
              <div className="ml-auto h-2 w-2 rounded-full bg-primary animate-pulse"></div>
            )}
          </Link>
        )
      })}
    </nav>
  )
}
