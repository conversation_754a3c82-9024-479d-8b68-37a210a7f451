'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { Card, CardContent } from '@/components/ui/card'
import { Crown, Scissors } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAdmin?: boolean
  fallback?: React.ReactNode
}

export function ProtectedRoute({ 
  children, 
  requireAdmin = false, 
  fallback 
}: ProtectedRouteProps) {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center">
        <Card className="shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8">
          <CardContent className="flex flex-col items-center space-y-4">
            <div className="relative animate-pulse">
              <Crown className="h-12 w-12 text-primary" />
              <Scissors className="h-8 w-8 text-accent absolute -bottom-2 -right-2" />
            </div>
            <div className="text-center">
              <h2 className="text-xl font-semibold text-foreground mb-2">Royal Cuts</h2>
              <p className="text-muted-foreground">Loading your dashboard...</p>
            </div>
            <div className="flex space-x-1">
              <div className="h-2 w-2 bg-primary rounded-full animate-bounce"></div>
              <div className="h-2 w-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="h-2 w-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show unauthorized if user is not logged in
  if (!user) {
    return fallback || (
      <div className="min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center">
        <Card className="shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8">
          <CardContent className="text-center">
            <div className="mb-4">
              <Crown className="h-16 w-16 text-muted-foreground mx-auto" />
            </div>
            <h2 className="text-2xl font-semibold text-foreground mb-2">Access Denied</h2>
            <p className="text-muted-foreground mb-4">
              You need to be logged in to access this page.
            </p>
            <button
              onClick={() => router.push('/auth/login')}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              Go to Login
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Check admin requirement
  if (requireAdmin) {
    const isAdmin = user?.user_metadata?.role === 'admin' || 
                    user?.email?.endsWith('@royalcuts.com') ||
                    user?.app_metadata?.role === 'admin'

    if (!isAdmin) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center">
          <Card className="shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8">
            <CardContent className="text-center">
              <div className="mb-4">
                <Crown className="h-16 w-16 text-muted-foreground mx-auto" />
              </div>
              <h2 className="text-2xl font-semibold text-foreground mb-2">Admin Access Required</h2>
              <p className="text-muted-foreground mb-4">
                You need administrator privileges to access this page.
              </p>
              <button
                onClick={() => router.push('/')}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Go to Dashboard
              </button>
            </CardContent>
          </Card>
        </div>
      )
    }
  }

  return <>{children}</>
}

// Higher-order component for protecting pages
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: { requireAdmin?: boolean }
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute requireAdmin={options?.requireAdmin}>
        <Component {...props} />
      </ProtectedRoute>
    )
  }
}

// Hook for checking authentication status
export function useRequireAuth(requireAdmin = false) {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push('/auth/login')
        return
      }

      if (requireAdmin) {
        const isAdmin = user?.user_metadata?.role === 'admin' || 
                        user?.email?.endsWith('@royalcuts.com') ||
                        user?.app_metadata?.role === 'admin'
        
        if (!isAdmin) {
          router.push('/')
          return
        }
      }
    }
  }, [user, loading, requireAdmin, router])

  return { user, loading }
}
