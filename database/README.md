# Barbershop Management System - Database Setup

This directory contains the database schema and setup files for the Barbershop Management System.

## Database Structure

The system uses PostgreSQL as the primary database with the following main tables:

### Core Tables

1. **customers** - Customer information and contact details
2. **staff** - Staff members, roles, and employment information
3. **services** - Available services with pricing and duration
4. **appointments** - Appointment scheduling and status tracking
5. **inventory** - Product and supply inventory management
6. **transactions** - Payment and transaction records

### Supporting Tables

- **staff_schedules** - Staff working hours and availability

## Setup Instructions

### Option 1: Using Supabase (Recommended)

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Run the Schema**
   - Go to the SQL Editor in your Supabase dashboard
   - Copy and paste the contents of `schema.sql`
   - Execute the script

3. **Add Sample Data (Optional)**
   - Copy and paste the contents of `sample-data.sql`
   - Execute the script to populate with test data

4. **Update Environment Variables**
   - Copy your project URL and anon key
   - Update `.env.local` in the project root:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   ```

### Option 2: Local PostgreSQL

1. **Install PostgreSQL**
   - Download and install PostgreSQL
   - Create a new database: `createdb barbershop_management`

2. **Run Schema**
   ```bash
   psql -d barbershop_management -f database/schema.sql
   ```

3. **Add Sample Data**
   ```bash
   psql -d barbershop_management -f database/sample-data.sql
   ```

4. **Update Connection String**
   - Update `.env.local` with your PostgreSQL connection details

## Database Schema Details

### Relationships

- **appointments** → **customers** (many-to-one)
- **appointments** → **staff** (many-to-one)
- **appointments** → **services** (many-to-one)
- **staff_schedules** → **staff** (many-to-one)
- **transactions** → **customers** (many-to-one)
- **transactions** → **staff** (many-to-one)
- **transactions** → **appointments** (one-to-one, optional)

### Key Features

- **UUID Primary Keys** - All tables use UUID for better security and scalability
- **Automatic Timestamps** - `created_at` and `updated_at` fields with triggers
- **Data Validation** - Check constraints for enums and data integrity
- **Indexes** - Optimized for common query patterns
- **Soft Deletes** - Important records are preserved with status flags

### Sample Data Overview

The sample data includes:
- 8 customers with diverse profiles
- 4 staff members with different roles and schedules
- 12 services covering various categories
- 12 inventory items across different categories
- Sample appointments for testing scheduling
- Transaction records for financial tracking

## Security Considerations

- Enable Row Level Security (RLS) for production
- Use service role key only for admin operations
- Implement proper authentication and authorization
- Regular backups and monitoring

## Maintenance

### Regular Tasks

1. **Backup Database** - Schedule regular backups
2. **Monitor Performance** - Check slow queries and optimize
3. **Update Statistics** - Keep PostgreSQL statistics current
4. **Archive Old Data** - Move old appointments and transactions to archive tables

### Monitoring Queries

```sql
-- Check appointment distribution
SELECT appointment_date, COUNT(*) as appointment_count 
FROM appointments 
WHERE appointment_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY appointment_date 
ORDER BY appointment_date;

-- Staff utilization
SELECT s.first_name, s.last_name, COUNT(a.id) as appointments_count
FROM staff s
LEFT JOIN appointments a ON s.id = a.staff_id 
WHERE a.appointment_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY s.id, s.first_name, s.last_name;

-- Revenue by service
SELECT srv.name, COUNT(a.id) as bookings, SUM(a.total_amount) as revenue
FROM services srv
LEFT JOIN appointments a ON srv.id = a.service_id
WHERE a.appointment_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY srv.id, srv.name
ORDER BY revenue DESC;

-- Low stock items
SELECT name, current_stock, min_stock_level
FROM inventory 
WHERE current_stock <= min_stock_level
ORDER BY (current_stock - min_stock_level);
```

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify environment variables
   - Check network connectivity
   - Confirm database credentials

2. **Permission Errors**
   - Ensure proper RLS policies
   - Check user roles and permissions
   - Verify API keys

3. **Performance Issues**
   - Review query execution plans
   - Check index usage
   - Monitor connection pool

For additional support, refer to the main project documentation or create an issue in the project repository.
