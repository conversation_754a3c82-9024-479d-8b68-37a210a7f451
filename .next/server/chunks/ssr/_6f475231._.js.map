{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx <module evaluation>\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProtectedRoute = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/protected-route.tsx <module evaluation>\",\n    \"ProtectedRoute\",\n);\nexport const useRequireAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useRequireAuth() from the server but useRequireAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/protected-route.tsx <module evaluation>\",\n    \"useRequireAuth\",\n);\nexport const withAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call with<PERSON><PERSON>() from the server but with<PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/protected-route.tsx <module evaluation>\",\n    \"withAuth\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yEACA", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProtectedRoute = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/protected-route.tsx\",\n    \"ProtectedRoute\",\n);\nexport const useRequireAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useRequireAuth() from the server but useRequireAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/protected-route.tsx\",\n    \"useRequireAuth\",\n);\nexport const withAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call withA<PERSON>() from the server but with<PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/protected-route.tsx\",\n    \"withAuth\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qDACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qDACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qDACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/page.tsx"], "sourcesContent": ["import { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Calendar, Users, DollarSign, Scissors, Package, AlertTriangle } from 'lucide-react'\n\nexport default function Dashboard() {\n  // Mock data - will be replaced with real data from Supabase\n  const stats = {\n    todayAppointments: 12,\n    todayRevenue: 850,\n    monthlyRevenue: 15420,\n    totalCustomers: 234,\n    activeStaff: 4,\n    lowStockItems: 3\n  }\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n      <div className=\"space-y-6\">\n        <div className=\"mb-8\">\n          <div className=\"flex items-center space-x-4 mb-2\">\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              <Calendar className=\"h-6 w-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n                Dashboard\n              </h1>\n              <p className=\"text-muted-foreground text-lg\">Welcome to Royal Cuts Management System</p>\n            </div>\n          </div>\n          <div className=\"text-sm text-muted-foreground\">\n            Today is {new Date().toLocaleDateString('en-US', {\n              weekday: 'long',\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })}\n          </div>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8\">\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 hover:shadow-xl transition-all duration-300 group\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-semibold text-blue-700 dark:text-blue-300\">Today's Appointments</CardTitle>\n              <div className=\"h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                <Calendar className=\"h-5 w-5 text-white\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-3xl font-bold text-blue-800 dark:text-blue-200 mb-1\">{stats.todayAppointments}</div>\n              <p className=\"text-sm text-blue-600 dark:text-blue-400 flex items-center\">\n                <span className=\"text-green-500 mr-1\">↗</span>\n                +2 from yesterday\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-950 dark:to-emerald-900 hover:shadow-xl transition-all duration-300 group\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-semibold text-green-700 dark:text-green-300\">Today's Revenue</CardTitle>\n              <div className=\"h-10 w-10 rounded-full bg-green-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                <DollarSign className=\"h-5 w-5 text-white\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-3xl font-bold text-green-800 dark:text-green-200 mb-1\">${stats.todayRevenue}</div>\n              <p className=\"text-sm text-green-600 dark:text-green-400 flex items-center\">\n                <span className=\"text-green-500 mr-1\">↗</span>\n                +12% from yesterday\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-950 dark:to-violet-900 hover:shadow-xl transition-all duration-300 group\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-semibold text-purple-700 dark:text-purple-300\">Monthly Revenue</CardTitle>\n              <div className=\"h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                <DollarSign className=\"h-5 w-5 text-white\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-3xl font-bold text-purple-800 dark:text-purple-200 mb-1\">${stats.monthlyRevenue}</div>\n              <p className=\"text-sm text-purple-600 dark:text-purple-400 flex items-center\">\n                <span className=\"text-green-500 mr-1\">↗</span>\n                +8% from last month\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-orange-50 to-amber-100 dark:from-orange-950 dark:to-amber-900 hover:shadow-xl transition-all duration-300 group\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-semibold text-orange-700 dark:text-orange-300\">Total Customers</CardTitle>\n              <div className=\"h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                <Users className=\"h-5 w-5 text-white\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-3xl font-bold text-orange-800 dark:text-orange-200 mb-1\">{stats.totalCustomers}</div>\n              <p className=\"text-sm text-orange-600 dark:text-orange-400 flex items-center\">\n                <span className=\"text-green-500 mr-1\">↗</span>\n                +5 new this week\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-indigo-50 to-blue-100 dark:from-indigo-950 dark:to-blue-900 hover:shadow-xl transition-all duration-300 group\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-semibold text-indigo-700 dark:text-indigo-300\">Active Staff</CardTitle>\n              <div className=\"h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                <Scissors className=\"h-5 w-5 text-white\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-3xl font-bold text-indigo-800 dark:text-indigo-200 mb-1\">{stats.activeStaff}</div>\n              <p className=\"text-sm text-indigo-600 dark:text-indigo-400 flex items-center\">\n                <span className=\"text-green-500 mr-1\">●</span>\n                All staff available\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-red-50 to-pink-100 dark:from-red-950 dark:to-pink-900 hover:shadow-xl transition-all duration-300 group\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n              <CardTitle className=\"text-sm font-semibold text-red-700 dark:text-red-300\">Low Stock Items</CardTitle>\n              <div className=\"h-10 w-10 rounded-full bg-red-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                <AlertTriangle className=\"h-5 w-5 text-white\" />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-3xl font-bold text-red-800 dark:text-red-200 mb-1\">{stats.lowStockItems}</div>\n              <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n                <span className=\"text-red-500 mr-1\">⚠</span>\n                Need restocking\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid gap-6 md:grid-cols-2\">\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900 dark:to-gray-800 hover:shadow-xl transition-all duration-300\">\n            <CardHeader className=\"pb-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 rounded-xl bg-gradient-primary flex items-center justify-center\">\n                  <Package className=\"h-5 w-5 text-white\" />\n                </div>\n                <div>\n                  <CardTitle className=\"text-xl font-bold text-foreground\">Quick Actions</CardTitle>\n                  <CardDescription className=\"text-muted-foreground\">Common tasks and shortcuts</CardDescription>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group\">\n                <Calendar className=\"h-4 w-4 text-primary group-hover:scale-110 transition-transform\" />\n                <span className=\"text-sm font-medium text-foreground\">Schedule new appointment</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group\">\n                <Users className=\"h-4 w-4 text-primary group-hover:scale-110 transition-transform\" />\n                <span className=\"text-sm font-medium text-foreground\">Add new customer</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group\">\n                <Package className=\"h-4 w-4 text-primary group-hover:scale-110 transition-transform\" />\n                <span className=\"text-sm font-medium text-foreground\">Update inventory</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-white/50 dark:bg-black/20 hover:bg-white dark:hover:bg-black/40 transition-colors cursor-pointer group\">\n                <Scissors className=\"h-4 w-4 text-primary group-hover:scale-110 transition-transform\" />\n                <span className=\"text-sm font-medium text-foreground\">View today's schedule</span>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"shadow-elegant-lg border-0 bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900 dark:to-gray-800 hover:shadow-xl transition-all duration-300\">\n            <CardHeader className=\"pb-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 rounded-xl bg-gradient-to-r from-accent to-primary flex items-center justify-center\">\n                  <DollarSign className=\"h-5 w-5 text-white\" />\n                </div>\n                <div>\n                  <CardTitle className=\"text-xl font-bold text-foreground\">Recent Activity</CardTitle>\n                  <CardDescription className=\"text-muted-foreground\">Latest updates and changes</CardDescription>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-green-50 dark:bg-green-950/30 border-l-4 border-green-500\">\n                <div className=\"h-2 w-2 rounded-full bg-green-500\"></div>\n                <span className=\"text-sm text-foreground\">John Doe appointment completed</span>\n                <span className=\"text-xs text-muted-foreground ml-auto\">2m ago</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/30 border-l-4 border-blue-500\">\n                <div className=\"h-2 w-2 rounded-full bg-blue-500\"></div>\n                <span className=\"text-sm text-foreground\">New customer Sarah Johnson added</span>\n                <span className=\"text-xs text-muted-foreground ml-auto\">5m ago</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-orange-50 dark:bg-orange-950/30 border-l-4 border-orange-500\">\n                <div className=\"h-2 w-2 rounded-full bg-orange-500\"></div>\n                <span className=\"text-sm text-foreground\">Shampoo inventory updated</span>\n                <span className=\"text-xs text-muted-foreground ml-auto\">10m ago</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-purple-50 dark:bg-purple-950/30 border-l-4 border-purple-500\">\n                <div className=\"h-2 w-2 rounded-full bg-purple-500\"></div>\n                <span className=\"text-sm text-foreground\">Staff schedule modified</span>\n                <span className=\"text-xs text-muted-foreground ml-auto\">15m ago</span>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </MainLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,4DAA4D;IAC5D,MAAM,QAAQ;QACZ,mBAAmB;QACnB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,aAAa;QACb,eAAe;IACjB;IAEA,qBACE,8OAAC,gJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA2F;;;;;;0DAGzG,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGjD,8OAAC;gCAAI,WAAU;;oCAAgC;oCACnC,IAAI,OAAO,kBAAkB,CAAC,SAAS;wCAC/C,SAAS;wCACT,MAAM;wCACN,OAAO;wCACP,KAAK;oCACP;;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAyD;;;;;;0DAC9E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGxB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DAA4D,MAAM,iBAAiB;;;;;;0DAClG,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA2D;;;;;;0DAChF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;;oDAA6D;oDAAE,MAAM,YAAY;;;;;;;0DAChG,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA6D;;;;;;0DAClF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;;oDAA+D;oDAAE,MAAM,cAAc;;;;;;;0DACpG,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA6D;;;;;;0DAClF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGrB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DAAgE,MAAM,cAAc;;;;;;0DACnG,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA6D;;;;;;0DAClF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGxB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DAAgE,MAAM,WAAW;;;;;;0DAChG,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAuD;;;;;;0DAC5E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG7B,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DAA0D,MAAM,aAAa;;;;;;0DAC5F,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAK,WAAU;kEAAoB;;;;;;oDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAoC;;;;;;sEACzD,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAIzD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;;;;;;;0DAExD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;;;;;;;;;;;;;;;;;;;0CAK5D,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,8OAAC;;sEACC,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAoC;;;;;;sEACzD,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAIzD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,8OAAC;wDAAK,WAAU;kEAAwC;;;;;;;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,8OAAC;wDAAK,WAAU;kEAAwC;;;;;;;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,8OAAC;wDAAK,WAAU;kEAAwC;;;;;;;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEAA0B;;;;;;kEAC1C,8OAAC;wDAAK,WAAU;kEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE", "debugId": null}}]}