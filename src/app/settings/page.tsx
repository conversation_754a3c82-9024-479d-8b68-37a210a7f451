'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader } from '@/components/layout/page-header'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Settings, 
  Building, 
  Clock, 
  DollarSign, 
  Bell, 
  Shield, 
  Palette,
  Globe,
  Mail,
  Phone,
  MapPin,
  Save,
  User,
  Key,
  Database,
  Smartphone,
  Download,
  Upload,
  RotateCcw,
  Calendar
} from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import {
  SettingsManager,
  validateBusinessSettings,
  validateOperatingHours,
  applyTheme,
  getThemeConfig,
  AVAILABLE_THEMES,
  emergencyFixTheme,
  type BusinessSettings,
  type OperatingHours,
  type NotificationSettings,
  type HolidaySettings
} from '@/lib/settings'
import { HolidayManager } from '@/components/settings/holiday-manager'

export default function SettingsPage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('business')
  const [errors, setErrors] = useState<string[]>([])

  // Settings state
  const [businessSettings, setBusinessSettings] = useState<BusinessSettings>({
    name: '皇家理发店',
    address: '北京市朝阳区三里屯街道1号',
    phone: '+86 138-0013-8000',
    email: '<EMAIL>',
    website: 'www.royalcuts.cn',
    timezone: 'Asia/Shanghai',
    currency: 'CNY'
  })

  const [operatingHours, setOperatingHours] = useState<OperatingHours>({
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '19:00', closed: false },
    friday: { open: '09:00', close: '19:00', closed: false },
    saturday: { open: '08:00', close: '17:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: false }
  })

  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    smsNotifications: false,
    appointmentReminders: true,
    lowStockAlerts: true,
    dailyReports: false,
    weeklyReports: true
  })

  const [holidaySettings, setHolidaySettings] = useState<HolidaySettings>({
    enableHolidayMode: true,
    holidays: []
  })

  const [selectedTheme, setSelectedTheme] = useState('royal-gold')

  // Load settings on component mount
  useEffect(() => {
    const settings = SettingsManager.getSettings()
    setBusinessSettings(settings.business)
    setOperatingHours(settings.operatingHours)
    setHolidaySettings(settings.holidays)
    setNotifications(settings.notifications)
    setSelectedTheme(settings.theme)
  }, [])

  // 分别保存不同模块的设置
  const handleSaveBusinessSettings = async () => {
    setLoading(true)
    setErrors([])

    try {
      const businessErrors = validateBusinessSettings(businessSettings)
      if (businessErrors.length > 0) {
        setErrors(businessErrors)
        setLoading(false)
        return
      }

      SettingsManager.updateBusinessSettings(businessSettings)
      await new Promise(resolve => setTimeout(resolve, 500))
      alert('商户信息保存成功！')
    } catch (error) {
      console.error('Error saving business settings:', error)
      alert('保存商户信息时出错，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveOperatingHours = async () => {
    setLoading(true)
    setErrors([])

    try {
      const hoursErrors = validateOperatingHours(operatingHours)
      if (hoursErrors.length > 0) {
        setErrors(hoursErrors)
        setLoading(false)
        return
      }

      SettingsManager.updateOperatingHours(operatingHours)
      await new Promise(resolve => setTimeout(resolve, 500))
      alert('营业时间保存成功！')
    } catch (error) {
      console.error('Error saving operating hours:', error)
      alert('保存营业时间时出错，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveHolidaySettings = async () => {
    setLoading(true)
    try {
      SettingsManager.updateHolidaySettings(holidaySettings)
      await new Promise(resolve => setTimeout(resolve, 500))
      alert('节假日设置保存成功！')
    } catch (error) {
      console.error('Error saving holiday settings:', error)
      alert('保存节假日设置时出错，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveNotifications = async () => {
    setLoading(true)
    try {
      SettingsManager.updateNotificationSettings(notifications)
      await new Promise(resolve => setTimeout(resolve, 500))
      alert('通知设置保存成功！')
    } catch (error) {
      console.error('Error saving notifications:', error)
      alert('保存通知设置时出错，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveTheme = async () => {
    setLoading(true)
    try {
      SettingsManager.updateTheme(selectedTheme)
      applyTheme(selectedTheme)
      await new Promise(resolve => setTimeout(resolve, 500))
      alert('主题设置保存成功！')
    } catch (error) {
      console.error('Error saving theme:', error)
      alert('保存主题设置时出错，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleThemeChange = (theme: string) => {
    setSelectedTheme(theme)
    applyTheme(theme)
  }

  const handleResetSettings = () => {
    if (confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {
      SettingsManager.resetToDefaults()
      const settings = SettingsManager.getSettings()
      setBusinessSettings(settings.business)
      setOperatingHours(settings.operatingHours)
      setHolidaySettings(settings.holidays)
      setNotifications(settings.notifications)
      setSelectedTheme(settings.theme)
      applyTheme(settings.theme)
      alert('设置已重置为默认值')
    }
  }

  const handleExportSettings = () => {
    try {
      const settingsJson = SettingsManager.exportSettings()
      const blob = new Blob([settingsJson], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'barbershop-settings.json'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      alert('导出设置失败')
    }
  }

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        SettingsManager.importSettings(content)
        const settings = SettingsManager.getSettings()
        setBusinessSettings(settings.business)
        setOperatingHours(settings.operatingHours)
        setHolidaySettings(settings.holidays)
        setNotifications(settings.notifications)
        setSelectedTheme(settings.theme)
        applyTheme(settings.theme)
        alert('设置导入成功！')
      } catch (error) {
        alert('导入设置失败：文件格式不正确')
      }
    }
    reader.readAsText(file)
    // Reset input
    event.target.value = ''
  }

  const tabs = [
    { id: 'business', label: '商户信息', icon: Building },
    { id: 'hours', label: '营业时间', icon: Clock },
    { id: 'holidays', label: '节假日设置', icon: Calendar },
    { id: 'notifications', label: '通知设置', icon: Bell },
    { id: 'appearance', label: '外观设置', icon: Palette },
    { id: 'security', label: '安全设置', icon: Shield },
    { id: 'integrations', label: '系统集成', icon: Database }
  ]

  const renderBusinessSettings = () => (
    <div className="space-y-6">
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="h-5 w-5 mr-2" />
            商户信息
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">商户名称</label>
              <Input
                value={businessSettings.name}
                onChange={(e) => setBusinessSettings({...businessSettings, name: e.target.value})}
                placeholder="皇家理发店"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">联系电话</label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  value={businessSettings.phone}
                  onChange={(e) => setBusinessSettings({...businessSettings, phone: e.target.value})}
                  className="pl-10"
                  placeholder="+86 138-0013-8000"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">邮箱地址</label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  value={businessSettings.email}
                  onChange={(e) => setBusinessSettings({...businessSettings, email: e.target.value})}
                  className="pl-10"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">网站地址</label>
              <div className="relative">
                <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  value={businessSettings.website}
                  onChange={(e) => setBusinessSettings({...businessSettings, website: e.target.value})}
                  className="pl-10"
                  placeholder="www.royalcuts.cn"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">商户地址</label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <textarea
                value={businessSettings.address}
                onChange={(e) => setBusinessSettings({...businessSettings, address: e.target.value})}
                className="w-full pl-10 pt-2 pb-2 pr-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                rows={2}
                placeholder="北京市朝阳区三里屯街道1号"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">时区设置</label>
              <Select value={businessSettings.timezone} onValueChange={(value) => setBusinessSettings({...businessSettings, timezone: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Asia/Shanghai">北京时间 (GMT+8)</SelectItem>
                  <SelectItem value="Asia/Hong_Kong">香港时间 (GMT+8)</SelectItem>
                  <SelectItem value="Asia/Taipei">台北时间 (GMT+8)</SelectItem>
                  <SelectItem value="America/New_York">美国东部时间 (ET)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">货币设置</label>
              <Select value={businessSettings.currency} onValueChange={(value) => setBusinessSettings({...businessSettings, currency: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CNY">CNY - 人民币</SelectItem>
                  <SelectItem value="HKD">HKD - 港币</SelectItem>
                  <SelectItem value="TWD">TWD - 新台币</SelectItem>
                  <SelectItem value="USD">USD - 美元</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={handleSaveBusinessSettings} disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>保存中...</span>
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存商户信息
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderOperatingHours = () => (
    <div className="space-y-6">
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            营业时间
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(operatingHours).map(([day, hours]) => (
              <div key={day} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="font-medium capitalize w-20">
                    {day === 'monday' ? '周一' :
                     day === 'tuesday' ? '周二' :
                     day === 'wednesday' ? '周三' :
                     day === 'thursday' ? '周四' :
                     day === 'friday' ? '周五' :
                     day === 'saturday' ? '周六' :
                     day === 'sunday' ? '周日' : day}
                  </span>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={!hours.closed}
                      onChange={(e) => setOperatingHours({
                        ...operatingHours,
                        [day]: { ...hours, closed: !e.target.checked }
                      })}
                      className="rounded"
                    />
                    <span className="text-sm">营业</span>
                  </label>
                </div>
                {!hours.closed && (
                  <div className="flex items-center space-x-2">
                    <Input
                      type="time"
                      value={hours.open}
                      onChange={(e) => setOperatingHours({
                        ...operatingHours,
                        [day]: { ...hours, open: e.target.value }
                      })}
                      className="w-24"
                    />
                    <span className="text-muted-foreground">至</span>
                    <Input
                      type="time"
                      value={hours.close}
                      onChange={(e) => setOperatingHours({
                        ...operatingHours,
                        [day]: { ...hours, close: e.target.value }
                      })}
                      className="w-24"
                    />
                  </div>
                )}
                {hours.closed && (
                  <Badge variant="secondary">休息</Badge>
                )}
              </div>
            ))}
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={handleSaveOperatingHours} disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>保存中...</span>
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存营业时间
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderNotifications = () => (
    <div className="space-y-6">
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="h-5 w-5 mr-2" />
            通知偏好设置
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { key: 'emailNotifications', label: '邮件通知', description: '通过邮件接收通知' },
              { key: 'smsNotifications', label: '短信通知', description: '通过短信接收通知' },
              { key: 'appointmentReminders', label: '预约提醒', description: '向客户发送预约提醒' },
              { key: 'lowStockAlerts', label: '库存不足提醒', description: '库存不足时接收通知' },
              { key: 'dailyReports', label: '日报', description: '接收每日业务摘要' },
              { key: 'weeklyReports', label: '周报', description: '接收每周分析报告' }
            ].map((setting) => (
              <div key={setting.key} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">{setting.label}</p>
                  <p className="text-sm text-muted-foreground">{setting.description}</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notifications[setting.key as keyof typeof notifications]}
                    onChange={(e) => setNotifications({
                      ...notifications,
                      [setting.key]: e.target.checked
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={handleSaveNotifications} disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>保存中...</span>
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存通知设置
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderAppearance = () => (
    <div className="space-y-6">
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Palette className="h-5 w-5 mr-2" />
            外观设置
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* 当前主题预览 */}
            <div className="p-4 border rounded-lg bg-muted/30">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="font-medium">当前主题</p>
                  <p className="text-sm text-muted-foreground">
                    {getThemeConfig(selectedTheme)?.name || '未知主题'}
                  </p>
                </div>
                <div className={`h-12 w-24 bg-gradient-to-r ${getThemeConfig(selectedTheme)?.preview.gradient} rounded-lg`}></div>
              </div>
              <p className="text-sm text-muted-foreground">
                {getThemeConfig(selectedTheme)?.description}
              </p>
            </div>

            {/* 主题选择 */}
            <div>
              <p className="font-medium mb-4">选择主题配色</p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {AVAILABLE_THEMES.map((theme) => (
                  <div
                    key={theme.id}
                    className={`p-4 border rounded-lg cursor-pointer hover:bg-accent transition-all ${
                      selectedTheme === theme.id ? 'ring-2 ring-primary bg-accent' : ''
                    }`}
                    onClick={() => handleThemeChange(theme.id)}
                  >
                    <div className={`h-12 bg-gradient-to-r ${theme.preview.gradient} rounded-lg mb-3`}></div>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-sm">{theme.name}</p>
                        {selectedTheme === theme.id && (
                          <Badge variant="default" className="text-xs">当前</Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {theme.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 主题详情 */}
            {selectedTheme && (
              <div className="p-4 border rounded-lg">
                <p className="font-medium mb-3">主题详情</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">主题名称</p>
                    <p className="font-medium">{getThemeConfig(selectedTheme)?.name}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">主题ID</p>
                    <p className="font-mono text-xs">{selectedTheme}</p>
                  </div>
                  <div className="col-span-2">
                    <p className="text-muted-foreground">描述</p>
                    <p>{getThemeConfig(selectedTheme)?.description}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-between pt-4 border-t">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  emergencyFixTheme()
                  alert('界面已恢复正常！')
                }}
              >
                🚨 紧急修复
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  if (selectedTheme) {
                    applyTheme(selectedTheme)
                    alert('主题已立即应用！')
                  }
                }}
                disabled={!selectedTheme}
              >
                👁️ 立即预览
              </Button>
            </div>

            <Button onClick={handleSaveTheme} disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>保存中...</span>
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  保存主题设置
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderSecurity = () => (
    <div className="space-y-6">
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            安全设置
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">双重身份验证</p>
                <p className="text-sm text-muted-foreground">为您的账户添加额外的安全保护</p>
              </div>
              <Button variant="outline" size="sm">
                <Smartphone className="h-4 w-4 mr-2" />
                启用双重验证
              </Button>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">修改密码</p>
                <p className="text-sm text-muted-foreground">更新您的账户密码</p>
              </div>
              <Button variant="outline" size="sm">
                <Key className="h-4 w-4 mr-2" />
                修改密码
              </Button>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">活跃会话</p>
                <p className="text-sm text-muted-foreground">管理您的活跃登录会话</p>
              </div>
              <Button variant="outline" size="sm">
                查看会话
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderIntegrations = () => (
    <div className="space-y-6">
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="h-5 w-5 mr-2" />
            系统集成
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Database className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="font-medium">Supabase 数据库</p>
                  <p className="text-sm text-muted-foreground">已连接并同步</p>
                </div>
              </div>
              <Badge variant="success">已连接</Badge>
            </div>
          </div>

          <div className="p-4 border rounded-lg opacity-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium">邮件服务</p>
                  <p className="text-sm text-muted-foreground">发送自动化邮件</p>
                </div>
              </div>
              <Button variant="outline" size="sm">连接</Button>
            </div>
          </div>

          <div className="p-4 border rounded-lg opacity-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Smartphone className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium">短信服务</p>
                  <p className="text-sm text-muted-foreground">发送短信通知</p>
                </div>
              </div>
              <Button variant="outline" size="sm">连接</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderHolidays = () => (
    <HolidayManager
      holidaySettings={holidaySettings}
      onUpdate={(newSettings) => {
        setHolidaySettings(newSettings)
        // 自动保存节假日设置
        SettingsManager.updateHolidaySettings(newSettings)
      }}
    />
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'business': return renderBusinessSettings()
      case 'hours': return renderOperatingHours()
      case 'holidays': return renderHolidays()
      case 'notifications': return renderNotifications()
      case 'appearance': return renderAppearance()
      case 'security': return renderSecurity()
      case 'integrations': return renderIntegrations()
      default: return renderBusinessSettings()
    }
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="系统设置"
          description="配置理发店管理系统"
          icon={<Settings className="h-6 w-6 text-white" />}
        />

        {/* Error Display */}
        {errors.length > 0 && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-start space-x-2">
              <div className="h-4 w-4 text-red-500 mt-0.5">⚠️</div>
              <div>
                <h4 className="text-sm font-medium text-red-700 dark:text-red-300">
                  设置验证错误
                </h4>
                <ul className="text-sm text-red-600 dark:text-red-400 mt-1">
                  {errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Settings Navigation */}
          <div className="lg:w-64">
            <Card className="shadow-elegant">
              <CardContent className="p-4">
                <nav className="space-y-1">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-accent hover:text-accent-foreground'
                      }`}
                    >
                      <tab.icon className="h-4 w-4" />
                      <span className="text-sm font-medium">{tab.label}</span>
                    </button>
                  ))}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Settings Content */}
          <div className="flex-1">
            {renderTabContent()}
            
            {/* Global Action Buttons */}
            <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
              <Button variant="outline" onClick={handleExportSettings}>
                <Download className="h-4 w-4 mr-2" />
                导出所有设置
              </Button>
              <div className="relative">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportSettings}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  导入设置
                </Button>
              </div>
              <Button variant="outline" onClick={handleResetSettings}>
                <RotateCcw className="h-4 w-4 mr-2" />
                重置所有设置
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
