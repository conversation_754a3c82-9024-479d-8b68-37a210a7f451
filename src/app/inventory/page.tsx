'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader, PageHeaderActions } from '@/components/layout/page-header'
import { InventoryForm } from '@/components/inventory/inventory-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { EmptyTableState } from '@/components/ui/empty-state'
import { Loading } from '@/components/ui/loading'
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  Edit,
  Trash2,
  Eye,
  ShoppingCart,
  Droplets,
  Scissors,
  Sparkles
} from 'lucide-react'
import { InventoryItem } from '@/types'
import { db } from '@/lib/supabase'
import { formatCurrency, formatDate } from '@/lib/utils'

export default function InventoryPage() {
  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredInventory, setFilteredInventory] = useState<InventoryItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState<InventoryItem | undefined>()

  useEffect(() => {
    loadInventory()
  }, [])

  useEffect(() => {
    // Filter inventory based on search term and category
    let filtered = inventory

    if (searchTerm.trim() !== '') {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.brand?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    setFilteredInventory(filtered)
  }, [inventory, searchTerm, selectedCategory])

  const loadInventory = async () => {
    try {
      setLoading(true)
      const { data, error } = await db.inventory.getAll()
      
      if (error) {
        console.error('Error loading inventory:', error)
        // For demo purposes, use mock data if Supabase is not configured
        setInventory(mockInventory)
      } else {
        setInventory(data || [])
      }
    } catch (error) {
      console.error('Error loading inventory:', error)
      // Use mock data as fallback
      setInventory(mockInventory)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteItem = async (itemId: string) => {
    if (confirm('Are you sure you want to delete this inventory item?')) {
      try {
        const { error } = await db.inventory.delete(itemId)
        if (error) {
          console.error('Error deleting item:', error)
        } else {
          setInventory(inventory.filter(i => i.id !== itemId))
        }
      } catch (error) {
        console.error('Error deleting item:', error)
      }
    }
  }

  const handleAddItem = () => {
    setEditingItem(undefined)
    setShowForm(true)
  }

  const handleEditItem = (item: InventoryItem) => {
    setEditingItem(item)
    setShowForm(true)
  }

  const handleFormSuccess = (item: InventoryItem) => {
    if (editingItem) {
      // Update existing item
      setInventory(inventory.map(i => i.id === item.id ? item : i))
    } else {
      // Add new item
      setInventory([item, ...inventory])
    }
  }

  const handleRestock = (item: InventoryItem) => {
    console.log('Restock item:', item.id)
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'shampoo':
      case 'conditioner':
        return <Droplets className="h-4 w-4" />
      case 'styling':
        return <Sparkles className="h-4 w-4" />
      case 'tools':
        return <Scissors className="h-4 w-4" />
      case 'supplies':
        return <Package className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getCategoryBadgeVariant = (category: string) => {
    switch (category) {
      case 'shampoo':
        return 'info'
      case 'conditioner':
        return 'purple'
      case 'styling':
        return 'warning'
      case 'tools':
        return 'destructive'
      case 'supplies':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const getStockStatus = (item: InventoryItem) => {
    if (item.current_stock <= 0) return 'out_of_stock'
    if (item.current_stock <= item.min_stock_level) return 'low_stock'
    return 'in_stock'
  }

  const getStockBadgeVariant = (status: string) => {
    switch (status) {
      case 'out_of_stock':
        return 'destructive'
      case 'low_stock':
        return 'warning'
      default:
        return 'success'
    }
  }

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'shampoo', label: 'Shampoo' },
    { value: 'conditioner', label: 'Conditioner' },
    { value: 'styling', label: 'Styling' },
    { value: 'tools', label: 'Tools' },
    { value: 'supplies', label: 'Supplies' },
    { value: 'other', label: 'Other' }
  ]

  const lowStockItems = inventory.filter(item => getStockStatus(item) === 'low_stock')
  const outOfStockItems = inventory.filter(item => getStockStatus(item) === 'out_of_stock')
  const totalValue = inventory.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0)

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="库存管理"
          description="跟踪库存水平、管理用品和监控库存成本"
          icon={<Package className="h-6 w-6 text-white" />}
          actions={
            <PageHeaderActions>
              <Button variant="outline">
                <ShoppingCart className="h-4 w-4 mr-2" />
                订购用品
              </Button>
              <Button onClick={handleAddItem}>
                <Plus className="h-4 w-4 mr-2" />
                添加物品
              </Button>
            </PageHeaderActions>
          }
        />

        <div className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-elegant">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name, brand, or SKU..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category.value}
                      variant={selectedCategory === category.value ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category.value)}
                    >
                      {category.label}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Inventory Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Items</p>
                    <p className="text-2xl font-bold">{inventory.length}</p>
                  </div>
                  <Package className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Low Stock</p>
                    <p className="text-2xl font-bold text-orange-600">{lowStockItems.length}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Out of Stock</p>
                    <p className="text-2xl font-bold text-red-600">{outOfStockItems.length}</p>
                  </div>
                  <TrendingDown className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                    <p className="text-2xl font-bold">{formatCurrency(totalValue)}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Low Stock Alert */}
          {lowStockItems.length > 0 && (
            <Card className="shadow-elegant border-orange-200 bg-orange-50 dark:bg-orange-950/30">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-orange-700 dark:text-orange-300">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Low Stock Alert
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-orange-600 dark:text-orange-400 mb-3">
                  {lowStockItems.length} item(s) are running low and need restocking:
                </p>
                <div className="flex flex-wrap gap-2">
                  {lowStockItems.slice(0, 5).map((item) => (
                    <Badge key={item.id} variant="warning" className="text-xs">
                      {item.name} ({item.current_stock} left)
                    </Badge>
                  ))}
                  {lowStockItems.length > 5 && (
                    <Badge variant="outline" className="text-xs">
                      +{lowStockItems.length - 5} more
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Inventory List */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle>Inventory Items</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Loading text="Loading inventory..." />
              ) : filteredInventory.length === 0 ? (
                searchTerm || selectedCategory !== 'all' ? (
                  <EmptyTableState
                    icon={<Search className="h-8 w-8 text-muted-foreground" />}
                    title="No items found"
                    description="No inventory items match your current filters"
                    action={{
                      label: "Clear filters",
                      onClick: () => {
                        setSearchTerm('')
                        setSelectedCategory('all')
                      }
                    }}
                  />
                ) : (
                  <EmptyTableState
                    icon={<Package className="h-8 w-8 text-muted-foreground" />}
                    title="No inventory items"
                    description="Start managing your inventory by adding your first item"
                    action={{
                      label: "Add Item",
                      onClick: handleAddItem
                    }}
                  />
                )
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {filteredInventory.map((item) => (
                    <InventoryCard
                      key={item.id}
                      item={item}
                      onEdit={() => handleEditItem(item)}
                      onDelete={handleDeleteItem}
                      onRestock={handleRestock}
                      getCategoryIcon={getCategoryIcon}
                      getCategoryBadgeVariant={getCategoryBadgeVariant}
                      getStockStatus={getStockStatus}
                      getStockBadgeVariant={getStockBadgeVariant}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Inventory Form Dialog */}
        <InventoryForm
          item={editingItem}
          open={showForm}
          onOpenChange={setShowForm}
          onSuccess={handleFormSuccess}
        />
      </MainLayout>
    </ProtectedRoute>
  )
}

interface InventoryCardProps {
  item: InventoryItem
  onEdit: () => void
  onDelete: (id: string) => void
  onRestock: (item: InventoryItem) => void
  getCategoryIcon: (category: string) => React.ReactNode
  getCategoryBadgeVariant: (category: string) => any
  getStockStatus: (item: InventoryItem) => string
  getStockBadgeVariant: (status: string) => any
}

function InventoryCard({ 
  item, 
  onEdit, 
  onDelete, 
  onRestock,
  getCategoryIcon, 
  getCategoryBadgeVariant,
  getStockStatus,
  getStockBadgeVariant
}: InventoryCardProps) {
  const stockStatus = getStockStatus(item)
  
  return (
    <div className="p-4 border rounded-lg hover:shadow-elegant transition-all duration-200 bg-card">
      <div className="space-y-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="font-semibold text-foreground">{item.name}</h3>
              <Badge variant={getStockBadgeVariant(stockStatus)} className="text-xs">
                {stockStatus === 'out_of_stock' ? 'Out of Stock' :
                 stockStatus === 'low_stock' ? 'Low Stock' : 'In Stock'}
              </Badge>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <Badge variant={getCategoryBadgeVariant(item.category)} className="flex items-center space-x-1">
                {getCategoryIcon(item.category)}
                <span className="capitalize">{item.category}</span>
              </Badge>
              {item.brand && (
                <Badge variant="outline" className="text-xs">
                  {item.brand}
                </Badge>
              )}
            </div>
            {item.sku && (
              <p className="text-xs text-muted-foreground">SKU: {item.sku}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Current Stock:</span>
            <p className="font-medium">{item.current_stock}</p>
          </div>
          <div>
            <span className="text-muted-foreground">Min Level:</span>
            <p className="font-medium">{item.min_stock_level}</p>
          </div>
          <div>
            <span className="text-muted-foreground">Unit Cost:</span>
            <p className="font-medium">{formatCurrency(item.unit_cost)}</p>
          </div>
          <div>
            <span className="text-muted-foreground">Total Value:</span>
            <p className="font-medium">{formatCurrency(item.current_stock * item.unit_cost)}</p>
          </div>
        </div>

        {item.last_restocked && (
          <div className="text-xs text-muted-foreground">
            Last restocked: {formatDate(item.last_restocked)}
          </div>
        )}

        <div className="flex items-center justify-between pt-2 border-t">
          {stockStatus !== 'in_stock' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onRestock(item)}
              className="flex items-center space-x-1"
            >
              <ShoppingCart className="h-3 w-3" />
              <span>Restock</span>
            </Button>
          )}
          
          <div className="flex space-x-1 ml-auto">
            <Button
              variant="ghost"
              size="sm"
              onClick={onEdit}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(item.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Mock data for demonstration
const mockInventory: InventoryItem[] = [
  {
    id: '1',
    name: 'Professional Shampoo',
    description: 'Moisturizing shampoo for all hair types',
    category: 'shampoo',
    brand: 'SalonPro',
    sku: 'SP-SHAM-001',
    current_stock: 15,
    min_stock_level: 5,
    unit_cost: 12.50,
    supplier: 'Beauty Supply Co',
    last_restocked: '2024-12-01',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-12-01T10:00:00Z'
  },
  {
    id: '2',
    name: 'Hair Gel',
    description: 'Strong hold styling gel',
    category: 'styling',
    brand: 'StyleMax',
    sku: 'SM-GEL-001',
    current_stock: 3,
    min_stock_level: 8,
    unit_cost: 8.50,
    supplier: 'Style Products Inc',
    last_restocked: '2024-11-15',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-11-15T10:00:00Z'
  },
  {
    id: '3',
    name: 'Professional Scissors',
    description: 'High-quality cutting scissors',
    category: 'tools',
    brand: 'CutMaster',
    sku: 'CM-SCIS-001',
    current_stock: 0,
    min_stock_level: 2,
    unit_cost: 85.00,
    supplier: 'Professional Tools Ltd',
    last_restocked: '2024-10-01',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-10-01T10:00:00Z'
  },
  {
    id: '4',
    name: 'Disposable Razors',
    description: 'Single-use safety razors',
    category: 'supplies',
    brand: 'SafeShave',
    sku: 'SS-RAZ-001',
    current_stock: 50,
    min_stock_level: 20,
    unit_cost: 0.75,
    supplier: 'Barber Supplies Co',
    last_restocked: '2024-12-10',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-12-10T10:00:00Z'
  }
]
