'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { LoadingSpinner } from '@/components/ui/loading'
import { UserCheck, Mail, Phone, Calendar, DollarSign, Crown, Scissors, Tag } from 'lucide-react'
import { Staff, StaffFormData } from '@/types'
import { db } from '@/lib/supabase'

const staffSchema = z.object({
  first_name: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  last_name: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(1, 'Phone number is required'),
  role: z.enum(['barber', 'stylist', 'manager', 'admin'], {
    required_error: 'Please select a role'
  }),
  hire_date: z.string().min(1, 'Hire date is required'),
  hourly_rate: z.number().min(0, 'Hourly rate must be positive').optional(),
  specialties: z.array(z.string()).optional()
})

interface StaffFormProps {
  staff?: Staff
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: (staff: Staff) => void
}

const specialtyOptions = [
  'haircuts',
  'beard_trimming',
  'classic_styles',
  'modern_cuts',
  'styling',
  'coloring',
  'highlights',
  'treatments',
  'women_cuts',
  'men_cuts',
  'customer_service',
  'management'
]

export function StaffForm({ staff, open, onOpenChange, onSuccess }: StaffFormProps) {
  const [loading, setLoading] = useState(false)
  const [selectedSpecialties, setSelectedSpecialties] = useState<string[]>(staff?.specialties || [])
  const isEditing = !!staff

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<StaffFormData>({
    resolver: zodResolver(staffSchema),
    defaultValues: staff ? {
      first_name: staff.first_name,
      last_name: staff.last_name,
      email: staff.email,
      phone: staff.phone,
      role: staff.role,
      hire_date: staff.hire_date,
      hourly_rate: staff.hourly_rate,
      specialties: staff.specialties || []
    } : {
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      role: 'barber',
      hire_date: '',
      hourly_rate: undefined,
      specialties: []
    }
  })

  const selectedRole = watch('role')

  const onSubmit = async (data: StaffFormData) => {
    try {
      setLoading(true)
      
      const staffData = {
        ...data,
        specialties: selectedSpecialties,
        hourly_rate: data.hourly_rate || undefined
      }

      let result
      if (isEditing) {
        result = await db.staff.update(staff.id, staffData)
      } else {
        result = await db.staff.create(staffData)
      }

      if (result.error) {
        console.error('Error saving staff:', result.error)
        alert('Error saving staff member. Please try again.')
        return
      }

      onSuccess(result.data)
      onOpenChange(false)
      reset()
      setSelectedSpecialties([])
    } catch (error) {
      console.error('Error saving staff:', error)
      alert('Error saving staff member. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    onOpenChange(false)
    reset()
    setSelectedSpecialties(staff?.specialties || [])
  }

  const toggleSpecialty = (specialty: string) => {
    const updated = selectedSpecialties.includes(specialty)
      ? selectedSpecialties.filter(s => s !== specialty)
      : [...selectedSpecialties, specialty]
    
    setSelectedSpecialties(updated)
    setValue('specialties', updated)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'manager':
      case 'admin':
        return <Crown className="h-4 w-4" />
      case 'barber':
      case 'stylist':
        return <Scissors className="h-4 w-4" />
      default:
        return <UserCheck className="h-4 w-4" />
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <UserCheck className="h-5 w-5" />
            <span>{isEditing ? 'Edit Staff Member' : 'Add New Staff Member'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update staff member information and role'
              : 'Add a new team member to your staff'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Personal Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <UserCheck className="h-4 w-4 mr-2" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="first_name" className="text-sm font-medium">
                    First Name *
                  </label>
                  <Input
                    id="first_name"
                    {...register('first_name')}
                    placeholder="Alex"
                    error={!!errors.first_name}
                  />
                  {errors.first_name && (
                    <p className="text-sm text-red-500">{errors.first_name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="last_name" className="text-sm font-medium">
                    Last Name *
                  </label>
                  <Input
                    id="last_name"
                    {...register('last_name')}
                    placeholder="Rodriguez"
                    error={!!errors.last_name}
                  />
                  {errors.last_name && (
                    <p className="text-sm text-red-500">{errors.last_name.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  Email Address *
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="<EMAIL>"
                    className="pl-10"
                    error={!!errors.email}
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="phone" className="text-sm font-medium">
                  Phone Number *
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    {...register('phone')}
                    placeholder="******-0123"
                    className="pl-10"
                    error={!!errors.phone}
                  />
                </div>
                {errors.phone && (
                  <p className="text-sm text-red-500">{errors.phone.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Employment Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Crown className="h-4 w-4 mr-2" />
                Employment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="role" className="text-sm font-medium">
                    Role *
                  </label>
                  <Select
                    value={selectedRole}
                    onValueChange={(value) => setValue('role', value as any)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="barber">
                        <div className="flex items-center space-x-2">
                          <Scissors className="h-4 w-4" />
                          <span>Barber</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="stylist">
                        <div className="flex items-center space-x-2">
                          <Scissors className="h-4 w-4" />
                          <span>Stylist</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="manager">
                        <div className="flex items-center space-x-2">
                          <Crown className="h-4 w-4" />
                          <span>Manager</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="admin">
                        <div className="flex items-center space-x-2">
                          <Crown className="h-4 w-4" />
                          <span>Admin</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.role && (
                    <p className="text-sm text-red-500">{errors.role.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="hire_date" className="text-sm font-medium">
                    Hire Date *
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="hire_date"
                      type="date"
                      {...register('hire_date')}
                      className="pl-10"
                      error={!!errors.hire_date}
                    />
                  </div>
                  {errors.hire_date && (
                    <p className="text-sm text-red-500">{errors.hire_date.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="hourly_rate" className="text-sm font-medium">
                  Hourly Rate
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="hourly_rate"
                    type="number"
                    step="0.01"
                    {...register('hourly_rate', { valueAsNumber: true })}
                    placeholder="25.00"
                    className="pl-10"
                    error={!!errors.hourly_rate}
                  />
                </div>
                {errors.hourly_rate && (
                  <p className="text-sm text-red-500">{errors.hourly_rate.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Specialties */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                Specialties
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Select the services and skills this staff member specializes in:
                </p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {specialtyOptions.map((specialty) => (
                    <button
                      key={specialty}
                      type="button"
                      onClick={() => toggleSpecialty(specialty)}
                      className={`p-2 text-sm rounded-lg border transition-colors text-left ${
                        selectedSpecialties.includes(specialty)
                          ? 'bg-primary text-primary-foreground border-primary'
                          : 'bg-background hover:bg-accent border-border'
                      }`}
                    >
                      {specialty.replace('_', ' ')}
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <LoadingSpinner size="sm" />
                  <span>{isEditing ? 'Updating...' : 'Creating...'}</span>
                </div>
              ) : (
                isEditing ? 'Update Staff Member' : 'Create Staff Member'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
