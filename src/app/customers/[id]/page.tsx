'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader, PageHeaderActions } from '@/components/layout/page-header'
import { CustomerForm } from '@/components/customers/customer-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loading } from '@/components/ui/loading'
import { 
  User, 
  Edit, 
  Trash2, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar,
  Clock,
  ArrowLeft,
  Scissors
} from 'lucide-react'
import { Customer, Appointment } from '@/types'
import { db } from '@/lib/supabase'
import { formatDate, formatDateTime } from '@/lib/utils'

export default function CustomerDetailPage() {
  const params = useParams()
  const router = useRouter()
  const customerId = params.id as string

  const [customer, setCustomer] = useState<Customer | null>(null)
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [showEditForm, setShowEditForm] = useState(false)

  useEffect(() => {
    if (customerId) {
      loadCustomerData()
    }
  }, [customerId])

  const loadCustomerData = async () => {
    try {
      setLoading(true)
      
      // Load customer details
      const { data: customerData, error: customerError } = await db.customers.getById(customerId)
      
      if (customerError) {
        console.error('Error loading customer:', customerError)
        // Use mock data for demo
        setCustomer(mockCustomer)
      } else {
        setCustomer(customerData)
      }

      // Load customer appointments
      // const { data: appointmentsData, error: appointmentsError } = await db.appointments.getByCustomer(customerId)
      // For now, use mock appointments
      setAppointments(mockAppointments)

    } catch (error) {
      console.error('Error loading customer data:', error)
      setCustomer(mockCustomer)
      setAppointments(mockAppointments)
    } finally {
      setLoading(false)
    }
  }

  const handleEditCustomer = () => {
    setShowEditForm(true)
  }

  const handleDeleteCustomer = async () => {
    if (confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      try {
        const { error } = await db.customers.delete(customerId)
        if (error) {
          console.error('Error deleting customer:', error)
          alert('Error deleting customer. Please try again.')
        } else {
          router.push('/customers')
        }
      } catch (error) {
        console.error('Error deleting customer:', error)
        alert('Error deleting customer. Please try again.')
      }
    }
  }

  const handleFormSuccess = (updatedCustomer: Customer) => {
    setCustomer(updatedCustomer)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <Loading text="Loading customer details..." />
        </MainLayout>
      </ProtectedRoute>
    )
  }

  if (!customer) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="text-center py-12">
            <h2 className="text-2xl font-semibold mb-2">Customer Not Found</h2>
            <p className="text-muted-foreground mb-4">The customer you're looking for doesn't exist.</p>
            <Button onClick={() => router.push('/customers')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Customers
            </Button>
          </div>
        </MainLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title={`${customer.first_name} ${customer.last_name}`}
          description="Customer profile and appointment history"
          icon={<User className="h-6 w-6 text-white" />}
          breadcrumbs={[
            { label: 'Customers', href: '/customers' },
            { label: `${customer.first_name} ${customer.last_name}` }
          ]}
          actions={
            <PageHeaderActions>
              <Button variant="outline" onClick={() => router.push('/customers')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Button variant="outline" onClick={handleEditCustomer}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="destructive" onClick={handleDeleteCustomer}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </PageHeaderActions>
          }
        />

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Customer Information */}
          <div className="lg:col-span-1 space-y-6">
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="h-16 w-16 rounded-full bg-gradient-primary flex items-center justify-center">
                    <span className="text-white text-xl font-bold">
                      {customer.first_name[0]}{customer.last_name[0]}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{customer.first_name} {customer.last_name}</h3>
                    <Badge variant="secondary">Customer since {formatDate(customer.created_at)}</Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  {customer.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{customer.email}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{customer.phone}</span>
                  </div>

                  {customer.date_of_birth && (
                    <div className="flex items-center space-x-3">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Born {formatDate(customer.date_of_birth)}</span>
                    </div>
                  )}

                  {customer.address && (
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <span className="text-sm">{customer.address}</span>
                    </div>
                  )}
                </div>

                {customer.notes && (
                  <div className="pt-4 border-t">
                    <h4 className="text-sm font-medium mb-2">Notes</h4>
                    <p className="text-sm text-muted-foreground">{customer.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle>Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Appointments</span>
                  <span className="font-semibold">{appointments.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Total Spent</span>
                  <span className="font-semibold">$450.00</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Last Visit</span>
                  <span className="font-semibold">Dec 15, 2024</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Appointment History */}
          <div className="lg:col-span-2">
            <Card className="shadow-elegant">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Scissors className="h-5 w-5 mr-2" />
                  Appointment History
                </CardTitle>
              </CardHeader>
              <CardContent>
                {appointments.length === 0 ? (
                  <div className="text-center py-8">
                    <Scissors className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No appointments yet</h3>
                    <p className="text-muted-foreground mb-4">This customer hasn't booked any appointments.</p>
                    <Button>
                      <Calendar className="h-4 w-4 mr-2" />
                      Book Appointment
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {appointments.map((appointment) => (
                      <div key={appointment.id} className="p-4 border rounded-lg hover:shadow-elegant transition-all">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold">{appointment.service?.name}</h4>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                              <span className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                {formatDate(appointment.appointment_date)}
                              </span>
                              <span className="flex items-center">
                                <Clock className="h-3 w-3 mr-1" />
                                {appointment.start_time}
                              </span>
                              <span>with {appointment.staff?.first_name} {appointment.staff?.last_name}</span>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge 
                              variant={
                                appointment.status === 'completed' ? 'success' :
                                appointment.status === 'cancelled' ? 'destructive' :
                                appointment.status === 'confirmed' ? 'info' : 'secondary'
                              }
                            >
                              {appointment.status}
                            </Badge>
                            <p className="text-sm font-semibold mt-1">${appointment.total_amount}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Edit Customer Form */}
        <CustomerForm
          customer={customer}
          open={showEditForm}
          onOpenChange={setShowEditForm}
          onSuccess={handleFormSuccess}
        />
      </MainLayout>
    </ProtectedRoute>
  )
}

// Mock data for demonstration
const mockCustomer: Customer = {
  id: '1',
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  phone: '******-0101',
  date_of_birth: '1985-03-15',
  address: '123 Main St, Anytown, ST 12345',
  notes: 'Regular customer, prefers short cuts. Allergic to certain hair products.',
  created_at: '2024-01-15T10:00:00Z',
  updated_at: '2024-01-15T10:00:00Z'
}

const mockAppointments: Appointment[] = [
  {
    id: '1',
    customer_id: '1',
    staff_id: '1',
    service_id: '1',
    appointment_date: '2024-12-15',
    start_time: '10:00',
    end_time: '10:30',
    status: 'completed',
    total_amount: 25.00,
    created_at: '2024-12-10T10:00:00Z',
    updated_at: '2024-12-15T10:30:00Z',
    service: { id: '1', name: 'Classic Haircut', duration: 30, price: 25.00, category: 'haircut', is_active: true, created_at: '', updated_at: '' },
    staff: { id: '1', first_name: 'Maria', last_name: 'Garcia', email: '', phone: '', role: 'barber', hire_date: '', is_active: true, created_at: '', updated_at: '' }
  }
]
