module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createSupabaseClient": (()=>createSupabaseClient),
    "db": (()=>db),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-ssr] (ecmascript) <module evaluation>");
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "your_supabase_project_url") || 'https://demo.supabase.co';
const supabaseAnonKey = ("TURBOPACK compile-time value", "your_supabase_anon_key") || 'demo-key';
// Only create Supabase client if proper credentials are provided
const isSupabaseConfigured = supabaseUrl !== 'your_supabase_project_url' && supabaseUrl !== 'https://demo.supabase.co' && supabaseAnonKey !== 'your_supabase_anon_key' && supabaseAnonKey !== 'demo-key';
const supabase = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
function createSupabaseClient() {
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
}
// Database helper functions
const createDbHelpers = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        // Return mock functions when Supabase is not configured
        return {
            customers: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    }),
                search: (query)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    })
            },
            staff: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getActive: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            services: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getActive: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            appointments: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getByDate: (date)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getByStaff: (staffId, date)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            inventory: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getLowStock: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                update: (id, data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                delete: (id)=>Promise.resolve({
                        error: new Error('Supabase not configured')
                    })
            },
            transactions: {
                getAll: ()=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getById: (id)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                getByDateRange: (startDate, endDate)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    }),
                create: (data)=>Promise.resolve({
                        data: null,
                        error: new Error('Supabase not configured')
                    })
            }
        };
    }
    "TURBOPACK unreachable";
};
const db = createDbHelpers();
}}),
"[project]/src/contexts/auth-context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "useIsAdmin": (()=>useIsAdmin),
    "useUserRole": (()=>useUserRole)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [session, setSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Check if Supabase is configured
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"]) {
            // For demo purposes, create a mock user when Supabase is not configured
            const mockUser = {
                id: 'demo-user',
                email: '<EMAIL>',
                user_metadata: {
                    role: 'admin',
                    full_name: 'Demo Admin'
                },
                app_metadata: {
                    role: 'admin'
                }
            };
            setUser(mockUser);
            setSession({
                user: mockUser
            });
            setLoading(false);
            return;
        }
        // Get initial session
        const getInitialSession = async ()=>{
            const { data: { session }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
            if (error) {
                console.error('Error getting session:', error);
            } else {
                setSession(session);
                setUser(session?.user ?? null);
            }
            setLoading(false);
        };
        getInitialSession();
        // Listen for auth changes
        const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange(async (event, session)=>{
            console.log('Auth state changed:', event, session);
            setSession(session);
            setUser(session?.user ?? null);
            setLoading(false);
        });
        return ()=>subscription.unsubscribe();
    }, []);
    const signIn = async (email, password)=>{
        try {
            setLoading(true);
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"]) {
                // Demo mode - accept any credentials
                const mockUser = {
                    id: 'demo-user',
                    email: email,
                    user_metadata: {
                        role: 'admin',
                        full_name: 'Demo Admin'
                    },
                    app_metadata: {
                        role: 'admin'
                    }
                };
                setUser(mockUser);
                setSession({
                    user: mockUser
                });
                return {
                    error: null
                };
            }
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
                email,
                password
            });
            if (error) {
                console.error('Sign in error:', error);
                return {
                    error
                };
            }
            return {
                error: null
            };
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                error
            };
        } finally{
            setLoading(false);
        }
    };
    const signUp = async (email, password, userData)=>{
        try {
            setLoading(true);
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signUp({
                email,
                password,
                options: {
                    data: userData
                }
            });
            if (error) {
                console.error('Sign up error:', error);
                return {
                    error
                };
            }
            return {
                error: null
            };
        } catch (error) {
            console.error('Sign up error:', error);
            return {
                error
            };
        } finally{
            setLoading(false);
        }
    };
    const signOut = async ()=>{
        try {
            setLoading(true);
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"]) {
                // Demo mode - just clear the user
                setUser(null);
                setSession(null);
                return;
            }
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
            if (error) {
                console.error('Sign out error:', error);
            }
        } catch (error) {
            console.error('Sign out error:', error);
        } finally{
            setLoading(false);
        }
    };
    const resetPassword = async (email)=>{
        try {
            const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.resetPasswordForEmail(email, {
                redirectTo: `${window.location.origin}/auth/reset-password`
            });
            if (error) {
                console.error('Reset password error:', error);
                return {
                    error
                };
            }
            return {
                error: null
            };
        } catch (error) {
            console.error('Reset password error:', error);
            return {
                error
            };
        }
    };
    const value = {
        user,
        session,
        loading,
        signIn,
        signUp,
        signOut,
        resetPassword
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/auth-context.tsx",
        lineNumber: 181,
        columnNumber: 5
    }, this);
}
function useAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
function useIsAdmin() {
    const { user } = useAuth();
    // Check if user has admin role in metadata or email domain
    const isAdmin = user?.user_metadata?.role === 'admin' || user?.email?.endsWith('@royalcuts.com') || user?.app_metadata?.role === 'admin';
    return isAdmin;
}
function useUserRole() {
    const { user } = useAuth();
    return user?.user_metadata?.role || user?.app_metadata?.role || 'user';
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7fa20791._.js.map