-- Sample data for Barbershop Management System
-- This file contains sample data to populate the database for testing and demonstration

-- Insert sample customers
INSERT INTO customers (first_name, last_name, email, phone, date_of_birth, address, notes) VALUES
('<PERSON>', '<PERSON><PERSON>', '<EMAIL>', '******-0101', '1985-03-15', '123 Main St, Anytown, ST 12345', 'Regular customer, prefers short cuts'),
('<PERSON>', '<PERSON>', '<EMAIL>', '******-0102', '1990-07-22', '456 Oak Ave, Anytown, ST 12345', 'Likes modern styling'),
('<PERSON>', '<PERSON>', '<EMAIL>', '******-0103', '1988-11-08', '789 Pine Rd, Anytown, ST 12345', 'Beard trimming specialist needed'),
('<PERSON>', '<PERSON>', '<EMAIL>', '******-0104', '1992-05-30', '321 Elm St, Anytown, ST 12345', 'Color treatments preferred'),
('<PERSON>', '<PERSON>', '<EMAIL>', '******-0105', '1987-09-12', '654 Maple Dr, Anytown, ST 12345', 'Business professional cuts'),
('<PERSON>', '<PERSON>', '<EMAIL>', '******-0106', '1995-01-18', '987 <PERSON> Ln, <PERSON>town, ST 12345', 'Trendy styles'),
('<PERSON>', '<PERSON>', '<EMAIL>', '******-0107', '1983-12-03', '147 <PERSON> <PERSON>, <PERSON>town, ST 12345', '<PERSON> cuts only'),
('<PERSON>', 'Wilson', '<EMAIL>', '******-0108', '1991-08-25', '258 Spruce Ave, Anytown, ST 12345', 'Highlights and styling');

-- Insert sample staff
INSERT INTO staff (first_name, last_name, email, phone, role, hire_date, hourly_rate, specialties) VALUES
('Alex', 'Rodriguez', '<EMAIL>', '******-0201', 'manager', '2020-01-15', 25.00, ARRAY['management', 'customer_service', 'haircuts']),
('Maria', 'Garcia', '<EMAIL>', '******-0202', 'barber', '2021-03-10', 22.00, ARRAY['haircuts', 'beard_trimming', 'classic_styles']),
('James', 'Thompson', '<EMAIL>', '******-0203', 'stylist', '2021-06-20', 24.00, ARRAY['modern_cuts', 'styling', 'coloring']),
('Sofia', 'Martinez', '<EMAIL>', '******-0204', 'stylist', '2022-02-01', 23.00, ARRAY['women_cuts', 'highlights', 'treatments']);

-- Insert sample staff schedules
INSERT INTO staff_schedules (staff_id, day_of_week, start_time, end_time) VALUES
-- Alex Rodriguez (Manager) - Monday to Friday
((SELECT id FROM staff WHERE email = '<EMAIL>'), 1, '08:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 2, '08:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 3, '08:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 4, '08:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 5, '08:00', '18:00'),

-- Maria Garcia (Barber) - Tuesday to Saturday
((SELECT id FROM staff WHERE email = '<EMAIL>'), 2, '09:00', '17:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 3, '09:00', '17:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 4, '09:00', '17:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 5, '09:00', '17:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 6, '09:00', '17:00'),

-- James Thompson (Stylist) - Monday to Friday
((SELECT id FROM staff WHERE email = '<EMAIL>'), 1, '10:00', '19:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 2, '10:00', '19:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 3, '10:00', '19:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 4, '10:00', '19:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 5, '10:00', '19:00'),

-- Sofia Martinez (Stylist) - Wednesday to Sunday
((SELECT id FROM staff WHERE email = '<EMAIL>'), 3, '09:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 4, '09:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 5, '09:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 6, '09:00', '18:00'),
((SELECT id FROM staff WHERE email = '<EMAIL>'), 0, '10:00', '16:00');

-- Insert sample services
INSERT INTO services (name, description, duration, price, category) VALUES
('Classic Haircut', 'Traditional men''s haircut with scissors and clippers', 30, 25.00, 'haircut'),
('Beard Trim', 'Professional beard trimming and shaping', 20, 15.00, 'haircut'),
('Buzz Cut', 'Short all-over cut with clippers', 15, 20.00, 'haircut'),
('Fade Cut', 'Modern fade haircut with styling', 45, 35.00, 'haircut'),
('Women''s Cut & Style', 'Haircut with wash and styling', 60, 45.00, 'styling'),
('Hair Wash & Blow Dry', 'Shampoo, condition, and blow dry', 30, 20.00, 'styling'),
('Color Touch-up', 'Root touch-up and color refresh', 90, 65.00, 'coloring'),
('Full Color', 'Complete hair coloring service', 120, 85.00, 'coloring'),
('Highlights', 'Partial or full highlights', 150, 95.00, 'coloring'),
('Deep Conditioning', 'Intensive hair treatment', 45, 30.00, 'treatment'),
('Scalp Treatment', 'Therapeutic scalp massage and treatment', 30, 25.00, 'treatment'),
('Hot Towel Shave', 'Traditional hot towel straight razor shave', 45, 40.00, 'other');

-- Insert sample inventory items
INSERT INTO inventory (name, description, category, brand, sku, current_stock, min_stock_level, unit_cost, supplier) VALUES
('Professional Shampoo', 'Moisturizing shampoo for all hair types', 'shampoo', 'SalonPro', 'SP-SHAM-001', 15, 5, 12.50, 'Beauty Supply Co'),
('Clarifying Shampoo', 'Deep cleansing shampoo for oily hair', 'shampoo', 'SalonPro', 'SP-SHAM-002', 8, 3, 14.00, 'Beauty Supply Co'),
('Hydrating Conditioner', 'Deep moisturizing conditioner', 'conditioner', 'SalonPro', 'SP-COND-001', 12, 4, 15.00, 'Beauty Supply Co'),
('Leave-in Conditioner', 'Lightweight leave-in treatment', 'conditioner', 'SalonPro', 'SP-COND-002', 6, 2, 18.00, 'Beauty Supply Co'),
('Hair Gel', 'Strong hold styling gel', 'styling', 'StyleMax', 'SM-GEL-001', 20, 8, 8.50, 'Style Products Inc'),
('Hair Wax', 'Matte finish styling wax', 'styling', 'StyleMax', 'SM-WAX-001', 18, 6, 12.00, 'Style Products Inc'),
('Hair Spray', 'Flexible hold hair spray', 'styling', 'StyleMax', 'SM-SPRAY-001', 25, 10, 6.75, 'Style Products Inc'),
('Professional Scissors', 'High-quality cutting scissors', 'tools', 'CutMaster', 'CM-SCIS-001', 4, 2, 85.00, 'Professional Tools Ltd'),
('Hair Clippers', 'Electric hair clippers with guards', 'tools', 'ClipPro', 'CP-CLIP-001', 3, 1, 120.00, 'Professional Tools Ltd'),
('Disposable Razors', 'Single-use safety razors', 'supplies', 'SafeShave', 'SS-RAZ-001', 50, 20, 0.75, 'Barber Supplies Co'),
('Towels', 'Professional salon towels', 'supplies', 'SalonLinen', 'SL-TOW-001', 30, 15, 8.00, 'Linen Supply Co'),
('Hair Color - Brown', 'Permanent hair color in brown', 'styling', 'ColorPro', 'CP-COL-BR', 12, 4, 22.00, 'Color Specialists');

-- Insert sample appointments (for current and upcoming dates)
INSERT INTO appointments (customer_id, staff_id, service_id, appointment_date, start_time, end_time, status, total_amount, notes) VALUES
-- Today's appointments
((SELECT id FROM customers WHERE email = '<EMAIL>'), 
 (SELECT id FROM staff WHERE email = '<EMAIL>'), 
 (SELECT id FROM services WHERE name = 'Classic Haircut'), 
 CURRENT_DATE, '10:00', '10:30', 'completed', 25.00, 'Regular monthly cut'),

((SELECT id FROM customers WHERE email = '<EMAIL>'), 
 (SELECT id FROM staff WHERE email = '<EMAIL>'), 
 (SELECT id FROM services WHERE name = 'Women''s Cut & Style'), 
 CURRENT_DATE, '14:00', '15:00', 'in_progress', 45.00, 'New style consultation'),

-- Tomorrow's appointments
((SELECT id FROM customers WHERE email = '<EMAIL>'), 
 (SELECT id FROM staff WHERE email = '<EMAIL>'), 
 (SELECT id FROM services WHERE name = 'Beard Trim'), 
 CURRENT_DATE + INTERVAL '1 day', '11:00', '11:20', 'confirmed', 15.00, 'Beard maintenance'),

((SELECT id FROM customers WHERE email = '<EMAIL>'), 
 (SELECT id FROM staff WHERE email = '<EMAIL>'), 
 (SELECT id FROM services WHERE name = 'Highlights'), 
 CURRENT_DATE + INTERVAL '1 day', '13:00', '15:30', 'scheduled', 95.00, 'Partial highlights requested');

-- Insert sample transactions
INSERT INTO transactions (appointment_id, customer_id, staff_id, amount, payment_method, transaction_date, notes) VALUES
((SELECT id FROM appointments WHERE customer_id = (SELECT id FROM customers WHERE email = '<EMAIL>') AND appointment_date = CURRENT_DATE),
 (SELECT id FROM customers WHERE email = '<EMAIL>'),
 (SELECT id FROM staff WHERE email = '<EMAIL>'),
 25.00, 'card', NOW() - INTERVAL '2 hours', 'Regular payment'),

-- Additional historical transactions
((SELECT id FROM customers WHERE email = '<EMAIL>'),
 (SELECT id FROM staff WHERE email = '<EMAIL>'),
 45.00, 'cash', NOW() - INTERVAL '1 day', 'Walk-in service'),

((SELECT id FROM customers WHERE email = '<EMAIL>'),
 (SELECT id FROM staff WHERE email = '<EMAIL>'),
 30.00, 'card', NOW() - INTERVAL '3 days', 'Business cut + beard trim');
