'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader, PageHeaderActions } from '@/components/layout/page-header'
import { ServiceForm } from '@/components/services/service-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { EmptyTableState } from '@/components/ui/empty-state'
import { Loading } from '@/components/ui/loading'
import { 
  Scissors, 
  Plus, 
  Search, 
  Filter, 
  Clock,
  DollarSign,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Palette,
  Sparkles,
  Heart
} from 'lucide-react'
import { Service } from '@/types'
import { db } from '@/lib/supabase'
import { formatCurrency } from '@/lib/utils'

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredServices, setFilteredServices] = useState<Service[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [showForm, setShowForm] = useState(false)
  const [editingService, setEditingService] = useState<Service | undefined>()

  useEffect(() => {
    loadServices()
  }, [])

  useEffect(() => {
    // Filter services based on search term and category
    let filtered = services

    if (searchTerm.trim() !== '') {
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(service => service.category === selectedCategory)
    }

    setFilteredServices(filtered)
  }, [services, searchTerm, selectedCategory])

  const loadServices = async () => {
    try {
      setLoading(true)
      const { data, error } = await db.services.getAll()
      
      if (error) {
        console.error('Error loading services:', error)
        // For demo purposes, use mock data if Supabase is not configured
        setServices(mockServices)
      } else {
        setServices(data || [])
      }
    } catch (error) {
      console.error('Error loading services:', error)
      // Use mock data as fallback
      setServices(mockServices)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteService = async (serviceId: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      try {
        const { error } = await db.services.delete(serviceId)
        if (error) {
          console.error('Error deleting service:', error)
        } else {
          setServices(services.filter(s => s.id !== serviceId))
        }
      } catch (error) {
        console.error('Error deleting service:', error)
      }
    }
  }

  const handleToggleActive = async (service: Service) => {
    try {
      const { error } = await db.services.update(service.id, { 
        is_active: !service.is_active 
      })
      if (error) {
        console.error('Error updating service:', error)
      } else {
        setServices(services.map(s => 
          s.id === service.id ? { ...s, is_active: !s.is_active } : s
        ))
      }
    } catch (error) {
      console.error('Error updating service:', error)
    }
  }

  const handleAddService = () => {
    setEditingService(undefined)
    setShowForm(true)
  }

  const handleEditService = (service: Service) => {
    setEditingService(service)
    setShowForm(true)
  }

  const handleFormSuccess = (service: Service) => {
    if (editingService) {
      // Update existing service
      setServices(services.map(s => s.id === service.id ? service : s))
    } else {
      // Add new service
      setServices([service, ...services])
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'haircut':
        return <Scissors className="h-4 w-4" />
      case 'styling':
        return <Sparkles className="h-4 w-4" />
      case 'coloring':
        return <Palette className="h-4 w-4" />
      case 'treatment':
        return <Heart className="h-4 w-4" />
      default:
        return <Scissors className="h-4 w-4" />
    }
  }

  const getCategoryBadgeVariant = (category: string) => {
    switch (category) {
      case 'haircut':
        return 'info'
      case 'styling':
        return 'purple'
      case 'coloring':
        return 'warning'
      case 'treatment':
        return 'success'
      default:
        return 'secondary'
    }
  }

  const categories = [
    { value: 'all', label: 'All Services' },
    { value: 'haircut', label: 'Haircuts' },
    { value: 'styling', label: 'Styling' },
    { value: 'coloring', label: 'Coloring' },
    { value: 'treatment', label: 'Treatments' },
    { value: 'other', label: 'Other' }
  ]

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="Services"
          description="Manage your service catalog, pricing, and availability"
          icon={<Scissors className="h-6 w-6 text-white" />}
          actions={
            <PageHeaderActions>
              <Button onClick={handleAddService}>
                <Plus className="h-4 w-4 mr-2" />
                Add Service
              </Button>
            </PageHeaderActions>
          }
        />

        <div className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-elegant">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search services by name or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex gap-2">
                  {categories.map((category) => (
                    <Button
                      key={category.value}
                      variant={selectedCategory === category.value ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category.value)}
                    >
                      {category.label}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Services</p>
                    <p className="text-2xl font-bold">{services.length}</p>
                  </div>
                  <Scissors className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Services</p>
                    <p className="text-2xl font-bold">{services.filter(s => s.is_active).length}</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Avg. Price</p>
                    <p className="text-2xl font-bold">
                      {formatCurrency(services.reduce((sum, s) => sum + s.price, 0) / services.length || 0)}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Avg. Duration</p>
                    <p className="text-2xl font-bold">
                      {Math.round(services.reduce((sum, s) => sum + s.duration, 0) / services.length || 0)}m
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Services List */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle>Service Catalog</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Loading text="Loading services..." />
              ) : filteredServices.length === 0 ? (
                searchTerm || selectedCategory !== 'all' ? (
                  <EmptyTableState
                    icon={<Search className="h-8 w-8 text-muted-foreground" />}
                    title="No services found"
                    description="No services match your current filters"
                    action={{
                      label: "Clear filters",
                      onClick: () => {
                        setSearchTerm('')
                        setSelectedCategory('all')
                      }
                    }}
                  />
                ) : (
                  <EmptyTableState
                    icon={<Scissors className="h-8 w-8 text-muted-foreground" />}
                    title="No services yet"
                    description="Start building your service catalog by adding your first service"
                    action={{
                      label: "Add Service",
                      onClick: handleAddService
                    }}
                  />
                )
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {filteredServices.map((service) => (
                    <ServiceCard
                      key={service.id}
                      service={service}
                      onEdit={() => handleEditService(service)}
                      onDelete={handleDeleteService}
                      onToggleActive={handleToggleActive}
                      getCategoryIcon={getCategoryIcon}
                      getCategoryBadgeVariant={getCategoryBadgeVariant}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Service Form Dialog */}
        <ServiceForm
          service={editingService}
          open={showForm}
          onOpenChange={setShowForm}
          onSuccess={handleFormSuccess}
        />
      </MainLayout>
    </ProtectedRoute>
  )
}

interface ServiceCardProps {
  service: Service
  onEdit: () => void
  onDelete: (id: string) => void
  onToggleActive: (service: Service) => void
  getCategoryIcon: (category: string) => React.ReactNode
  getCategoryBadgeVariant: (category: string) => any
}

function ServiceCard({ 
  service, 
  onEdit, 
  onDelete, 
  onToggleActive, 
  getCategoryIcon, 
  getCategoryBadgeVariant 
}: ServiceCardProps) {
  return (
    <div className="p-4 border rounded-lg hover:shadow-elegant transition-all duration-200 bg-card">
      <div className="space-y-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className="font-semibold text-foreground">{service.name}</h3>
              {!service.is_active && (
                <Badge variant="destructive" className="text-xs">Inactive</Badge>
              )}
            </div>
            <Badge variant={getCategoryBadgeVariant(service.category)} className="flex items-center space-x-1 w-fit">
              {getCategoryIcon(service.category)}
              <span className="capitalize">{service.category}</span>
            </Badge>
          </div>
          <div className="text-right">
            <p className="text-lg font-bold text-foreground">{formatCurrency(service.price)}</p>
            <div className="flex items-center text-sm text-muted-foreground">
              <Clock className="h-3 w-3 mr-1" />
              {service.duration}m
            </div>
          </div>
        </div>

        {service.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {service.description}
          </p>
        )}

        <div className="flex items-center justify-between pt-2 border-t">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleActive(service)}
            className="flex items-center space-x-1"
          >
            {service.is_active ? (
              <>
                <EyeOff className="h-3 w-3" />
                <span>Deactivate</span>
              </>
            ) : (
              <>
                <Eye className="h-3 w-3" />
                <span>Activate</span>
              </>
            )}
          </Button>
          
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onEdit}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(service.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Mock data for demonstration
const mockServices: Service[] = [
  {
    id: '1',
    name: 'Classic Haircut',
    description: 'Traditional men\'s haircut with scissors and clippers',
    duration: 30,
    price: 25.00,
    category: 'haircut',
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z'
  },
  {
    id: '2',
    name: 'Beard Trim',
    description: 'Professional beard trimming and shaping',
    duration: 20,
    price: 15.00,
    category: 'haircut',
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z'
  },
  {
    id: '3',
    name: 'Women\'s Cut & Style',
    description: 'Haircut with wash and styling',
    duration: 60,
    price: 45.00,
    category: 'styling',
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z'
  },
  {
    id: '4',
    name: 'Full Color',
    description: 'Complete hair coloring service',
    duration: 120,
    price: 85.00,
    category: 'coloring',
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z'
  },
  {
    id: '5',
    name: 'Deep Conditioning',
    description: 'Intensive hair treatment',
    duration: 45,
    price: 30.00,
    category: 'treatment',
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z'
  },
  {
    id: '6',
    name: 'Hot Towel Shave',
    description: 'Traditional hot towel straight razor shave',
    duration: 45,
    price: 40.00,
    category: 'other',
    is_active: false,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z'
  }
]
