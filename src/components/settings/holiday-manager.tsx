'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Calendar,
  Plus,
  Edit,
  Trash2,
  Clock,
  AlertCircle,
  Download,
  RefreshCw,
  Settings as SettingsIcon
} from 'lucide-react'
import { 
  type Holiday, 
  type HolidaySettings,
  type DayHours,
  generateHolidayId,
  validateHoliday,
  formatHolidayDate,
  getHolidayTypeLabel,
  getUpcomingHolidays
} from '@/lib/settings'
import { CachedHolidayService } from '@/lib/holiday-api'
import { HolidayApiConfig } from './holiday-api-config'

interface HolidayManagerProps {
  holidaySettings: HolidaySettings
  onUpdate: (settings: HolidaySettings) => void
}

export function HolidayManager({ holidaySettings, onUpdate }: HolidayManagerProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null)
  const [errors, setErrors] = useState<string[]>([])
  const [showApiConfig, setShowApiConfig] = useState(false)
  const [isImporting, setIsImporting] = useState(false)

  const [formData, setFormData] = useState<Partial<Holiday>>({
    name: '',
    date: '',
    type: 'fixed',
    recurring: true,
    closed: true,
    description: ''
  })

  const resetForm = () => {
    setFormData({
      name: '',
      date: '',
      type: 'fixed',
      recurring: true,
      closed: true,
      description: ''
    })
    setErrors([])
  }

  const handleAddHoliday = () => {
    const validationErrors = validateHoliday(formData)
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }

    const newHoliday: Holiday = {
      id: generateHolidayId(),
      name: formData.name!,
      date: formData.date!,
      type: formData.type as Holiday['type'],
      recurring: formData.recurring!,
      closed: formData.closed!,
      description: formData.description,
      hours: formData.hours
    }

    const updatedSettings = {
      ...holidaySettings,
      holidays: [...holidaySettings.holidays, newHoliday]
    }

    onUpdate(updatedSettings)
    setIsAddDialogOpen(false)
    resetForm()
  }

  const handleEditHoliday = (holiday: Holiday) => {
    setEditingHoliday(holiday)
    setFormData(holiday)
    setIsAddDialogOpen(true)
  }

  const handleUpdateHoliday = () => {
    const validationErrors = validateHoliday(formData)
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      return
    }

    const updatedHoliday: Holiday = {
      ...editingHoliday!,
      ...formData
    } as Holiday

    const updatedSettings = {
      ...holidaySettings,
      holidays: holidaySettings.holidays.map(h => 
        h.id === editingHoliday!.id ? updatedHoliday : h
      )
    }

    onUpdate(updatedSettings)
    setIsAddDialogOpen(false)
    setEditingHoliday(null)
    resetForm()
  }

  const handleDeleteHoliday = (holidayId: string) => {
    if (confirm('确定要删除这个节假日吗？')) {
      const updatedSettings = {
        ...holidaySettings,
        holidays: holidaySettings.holidays.filter(h => h.id !== holidayId)
      }
      onUpdate(updatedSettings)
    }
  }

  const handleToggleHolidayMode = () => {
    onUpdate({
      ...holidaySettings,
      enableHolidayMode: !holidaySettings.enableHolidayMode
    })
  }

  const handleImportFromApi = async (apiHolidays: any[]) => {
    setIsImporting(true)
    try {
      // 将API节假日转换为系统格式
      const importedHolidays = apiHolidays.map(apiHoliday => ({
        id: apiHoliday.id || generateHolidayId(),
        name: apiHoliday.name,
        date: apiHoliday.date,
        type: apiHoliday.type || 'fixed',
        recurring: apiHoliday.recurring !== false,
        closed: apiHoliday.closed !== false,
        description: apiHoliday.description || `从API导入 - ${apiHoliday.name}`,
        source: apiHoliday.source || 'api'
      }))

      // 合并现有节假日和导入的节假日，避免重复
      const existingHolidays = holidaySettings.holidays.filter(h => !h.source || h.source !== 'api')
      const mergedHolidays = [...existingHolidays, ...importedHolidays]

      const updatedSettings = {
        ...holidaySettings,
        holidays: mergedHolidays
      }

      onUpdate(updatedSettings)
      setShowApiConfig(false)
    } catch (error) {
      console.error('导入节假日失败:', error)
    } finally {
      setIsImporting(false)
    }
  }

  const handleQuickImport = async () => {
    setIsImporting(true)
    try {
      const currentYear = new Date().getFullYear()
      const nextYear = currentYear + 1

      // 获取今年和明年的节假日
      const [thisYearHolidays, nextYearHolidays] = await Promise.all([
        CachedHolidayService.getHolidays(currentYear),
        CachedHolidayService.getHolidays(nextYear)
      ])

      const allApiHolidays = [...thisYearHolidays, ...nextYearHolidays]
      await handleImportFromApi(allApiHolidays)
    } catch (error) {
      console.error('快速导入失败:', error)
      alert('导入失败，请检查API配置')
    } finally {
      setIsImporting(false)
    }
  }

  const upcomingHolidays = getUpcomingHolidays(holidaySettings.holidays)

  return (
    <div className="space-y-6">
      {/* Holiday Mode Toggle */}
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              节假日模式
            </span>
            <Button
              variant={holidaySettings.enableHolidayMode ? "default" : "outline"}
              onClick={handleToggleHolidayMode}
            >
              {holidaySettings.enableHolidayMode ? '已启用' : '已禁用'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            启用节假日模式后，系统将在节假日期间自动调整营业时间或休息。
          </p>
        </CardContent>
      </Card>

      {/* Upcoming Holidays */}
      {holidaySettings.enableHolidayMode && upcomingHolidays.length > 0 && (
        <Card className="shadow-elegant">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              即将到来的节假日
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {upcomingHolidays.slice(0, 3).map((holiday) => (
                <div key={holiday.id} className="flex items-center justify-between p-2 rounded-lg bg-muted/30">
                  <div>
                    <span className="font-medium">{holiday.name}</span>
                    <span className="text-sm text-muted-foreground ml-2">
                      {formatHolidayDate(holiday.date)}
                    </span>
                  </div>
                  <Badge variant={holiday.closed ? "destructive" : "secondary"}>
                    {holiday.closed ? '休息' : '营业'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Holiday List */}
      <Card className="shadow-elegant">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>节假日管理</span>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={handleQuickImport} disabled={isImporting}>
                {isImporting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    导入中...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    从API导入
                  </>
                )}
              </Button>
              <Button variant="outline" onClick={() => setShowApiConfig(true)}>
                <SettingsIcon className="h-4 w-4 mr-2" />
                API设置
              </Button>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={resetForm}>
                    <Plus className="h-4 w-4 mr-2" />
                    手动添加
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                  <DialogTitle>
                    {editingHoliday ? '编辑节假日' : '添加节假日'}
                  </DialogTitle>
                </DialogHeader>
                
                {/* Error Display */}
                {errors.length > 0 && (
                  <div className="p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
                    <ul className="text-sm text-red-600 dark:text-red-400">
                      {errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">节假日名称</label>
                    <Input
                      value={formData.name || ''}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="例如：春节、国庆节"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">日期</label>
                    <Input
                      type="date"
                      value={formData.date || ''}
                      onChange={(e) => setFormData({...formData, date: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">类型</label>
                    <Select 
                      value={formData.type || 'fixed'} 
                      onValueChange={(value) => setFormData({...formData, type: value as Holiday['type']})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fixed">固定日期</SelectItem>
                        <SelectItem value="lunar">农历日期</SelectItem>
                        <SelectItem value="custom">自定义</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.recurring || false}
                      onChange={(e) => setFormData({...formData, recurring: e.target.checked})}
                      className="rounded"
                    />
                    <label className="text-sm">每年重复</label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.closed || false}
                      onChange={(e) => setFormData({...formData, closed: e.target.checked})}
                      className="rounded"
                    />
                    <label className="text-sm">休息日</label>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">描述（可选）</label>
                    <Input
                      value={formData.description || ''}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      placeholder="节假日描述"
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setIsAddDialogOpen(false)
                        setEditingHoliday(null)
                        resetForm()
                      }}
                    >
                      取消
                    </Button>
                    <Button onClick={editingHoliday ? handleUpdateHoliday : handleAddHoliday}>
                      {editingHoliday ? '更新' : '添加'}
                    </Button>
                  </div>
                </div>
                  </DialogContent>
              </Dialog>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {holidaySettings.holidays.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>还没有设置任何节假日</p>
              <p className="text-sm">点击"添加节假日"开始设置</p>
            </div>
          ) : (
            <div className="space-y-3">
              {holidaySettings.holidays.map((holiday) => (
                <div key={holiday.id} className="flex items-center justify-between p-3 rounded-lg border">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium">{holiday.name}</h4>
                      <Badge variant="outline">
                        {getHolidayTypeLabel(holiday.type)}
                      </Badge>
                      {holiday.recurring && (
                        <Badge variant="secondary">每年重复</Badge>
                      )}
                      <Badge variant={holiday.closed ? "destructive" : "default"}>
                        {holiday.closed ? '休息' : '营业'}
                      </Badge>
                      {holiday.source && holiday.source !== 'manual' && (
                        <Badge variant="outline" className="text-xs">
                          {holiday.source === 'api' ? 'API' :
                           holiday.source === 'wannianli' ? '万年历' :
                           holiday.source === 'china_holiday' ? '中国节假日' :
                           holiday.source === 'tianapi' ? '天行数据' :
                           holiday.source === 'juhe' ? '聚合数据' : 'API'}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 mt-1 text-sm text-muted-foreground">
                      <span>{formatHolidayDate(holiday.date)}</span>
                      {holiday.description && (
                        <span>• {holiday.description}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditHoliday(holiday)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteHoliday(holiday.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* API配置对话框 */}
      <Dialog open={showApiConfig} onOpenChange={setShowApiConfig}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>节假日API配置</DialogTitle>
          </DialogHeader>
          <HolidayApiConfig onHolidaysUpdate={handleImportFromApi} />
        </DialogContent>
      </Dialog>
    </div>
  )
}
