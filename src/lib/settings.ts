// Settings management for local storage
export interface BusinessSettings {
  name: string
  address: string
  phone: string
  email: string
  website: string
  timezone: string
  currency: string
}

export interface OperatingHours {
  [key: string]: {
    open: string
    close: string
    closed: boolean
  }
}

export interface NotificationSettings {
  emailNotifications: boolean
  smsNotifications: boolean
  appointmentReminders: boolean
  lowStockAlerts: boolean
  dailyReports: boolean
  weeklyReports: boolean
}

export interface AppSettings {
  business: BusinessSettings
  operatingHours: OperatingHours
  notifications: NotificationSettings
  theme: string
  language: string
}

const DEFAULT_SETTINGS: AppSettings = {
  business: {
    name: '皇家理发店',
    address: '北京市朝阳区三里屯街道1号',
    phone: '+86 138-0013-8000',
    email: '<EMAIL>',
    website: 'www.royalcuts.cn',
    timezone: 'Asia/Shanghai',
    currency: 'CNY'
  },
  operatingHours: {
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '19:00', closed: false },
    friday: { open: '09:00', close: '19:00', closed: false },
    saturday: { open: '08:00', close: '17:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: false }
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    appointmentReminders: true,
    lowStockAlerts: true,
    dailyReports: false,
    weeklyReports: true
  },
  theme: 'royal-gold',
  language: 'zh-CN'
}

const SETTINGS_KEY = 'barbershop_settings'

export class SettingsManager {
  static getSettings(): AppSettings {
    if (typeof window === 'undefined') {
      return DEFAULT_SETTINGS
    }

    try {
      const stored = localStorage.getItem(SETTINGS_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        // Merge with defaults to ensure all properties exist
        return {
          ...DEFAULT_SETTINGS,
          ...parsed,
          business: { ...DEFAULT_SETTINGS.business, ...parsed.business },
          operatingHours: { ...DEFAULT_SETTINGS.operatingHours, ...parsed.operatingHours },
          notifications: { ...DEFAULT_SETTINGS.notifications, ...parsed.notifications }
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }

    return DEFAULT_SETTINGS
  }

  static saveSettings(settings: AppSettings): void {
    if (typeof window === 'undefined') {
      return
    }

    try {
      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings))
    } catch (error) {
      console.error('Error saving settings:', error)
      throw new Error('无法保存设置')
    }
  }

  static updateBusinessSettings(business: BusinessSettings): void {
    const settings = this.getSettings()
    settings.business = business
    this.saveSettings(settings)
  }

  static updateOperatingHours(operatingHours: OperatingHours): void {
    const settings = this.getSettings()
    settings.operatingHours = operatingHours
    this.saveSettings(settings)
  }

  static updateNotificationSettings(notifications: NotificationSettings): void {
    const settings = this.getSettings()
    settings.notifications = notifications
    this.saveSettings(settings)
  }

  static updateTheme(theme: string): void {
    const settings = this.getSettings()
    settings.theme = theme
    this.saveSettings(settings)
  }

  static resetToDefaults(): void {
    this.saveSettings(DEFAULT_SETTINGS)
  }

  static exportSettings(): string {
    const settings = this.getSettings()
    return JSON.stringify(settings, null, 2)
  }

  static importSettings(settingsJson: string): void {
    try {
      const imported = JSON.parse(settingsJson)
      // Validate the structure
      if (imported.business && imported.operatingHours && imported.notifications) {
        this.saveSettings(imported)
      } else {
        throw new Error('Invalid settings format')
      }
    } catch (error) {
      console.error('Error importing settings:', error)
      throw new Error('无效的设置格式')
    }
  }
}

// Utility functions for formatting
export function formatOperatingHours(hours: OperatingHours): string {
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  
  return days.map((day, index) => {
    const dayHours = hours[day]
    if (dayHours.closed) {
      return `${dayNames[index]}: 休息`
    }
    return `${dayNames[index]}: ${dayHours.open} - ${dayHours.close}`
  }).join('\n')
}

export function validateBusinessSettings(business: BusinessSettings): string[] {
  const errors: string[] = []
  
  if (!business.name.trim()) {
    errors.push('商户名称不能为空')
  }
  
  if (!business.phone.trim()) {
    errors.push('联系电话不能为空')
  }
  
  if (!business.email.trim()) {
    errors.push('邮箱地址不能为空')
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(business.email)) {
    errors.push('邮箱地址格式不正确')
  }
  
  if (!business.address.trim()) {
    errors.push('商户地址不能为空')
  }
  
  return errors
}

export function validateOperatingHours(hours: OperatingHours): string[] {
  const errors: string[] = []
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  
  days.forEach((day, index) => {
    const dayHours = hours[day]
    if (!dayHours.closed) {
      if (!dayHours.open || !dayHours.close) {
        errors.push(`${dayNames[index]}的营业时间不完整`)
      } else if (dayHours.open >= dayHours.close) {
        errors.push(`${dayNames[index]}的开始时间必须早于结束时间`)
      }
    }
  })
  
  return errors
}

// Theme utilities
export function applyTheme(theme: string): void {
  if (typeof document === 'undefined') return
  
  const root = document.documentElement
  
  switch (theme) {
    case 'royal-gold':
      root.style.setProperty('--primary', '180 83% 40%')
      root.style.setProperty('--primary-foreground', '0 0% 98%')
      break
    case 'ocean-blue':
      root.style.setProperty('--primary', '217 91% 60%')
      root.style.setProperty('--primary-foreground', '0 0% 98%')
      break
    case 'forest-green':
      root.style.setProperty('--primary', '142 76% 36%')
      root.style.setProperty('--primary-foreground', '0 0% 98%')
      break
    default:
      // Keep default theme
      break
  }
}

// Initialize theme on load
export function initializeTheme(): void {
  const settings = SettingsManager.getSettings()
  applyTheme(settings.theme)
}
