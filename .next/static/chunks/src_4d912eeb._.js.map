{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport {\n  Calendar,\n  Users,\n  Scissors,\n  UserCheck,\n  Package,\n  BarChart3,\n  Settings,\n  Home,\n  Navigation as NavigationIcon\n} from 'lucide-react'\n\nconst navigation = [\n  { name: '仪表板', href: '/', icon: Home },\n  { name: '预约管理', href: '/appointments', icon: Calendar },\n  { name: '客户管理', href: '/customers', icon: Users },\n  { name: '员工管理', href: '/staff', icon: UserCheck },\n  { name: '服务管理', href: '/services', icon: Scissors },\n  { name: '库存管理', href: '/inventory', icon: Package },\n  { name: '数据分析', href: '/analytics', icon: BarChart3 },\n  { name: '系统设置', href: '/settings', icon: Settings },\n  { name: '定位演示', href: '/demo-location', icon: NavigationIcon },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"flex flex-col space-y-2\">\n      {navigation.map((item, index) => {\n        const isActive = pathname === item.href\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            className={cn(\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',\n              isActive\n                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'\n                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'\n            )}\n            style={{ animationDelay: `${index * 50}ms` }}\n          >\n            <item.icon className={cn(\n              \"mr-3 h-5 w-5 transition-all duration-200\",\n              isActive\n                ? \"text-primary scale-110\"\n                : \"text-muted-foreground group-hover:text-primary group-hover:scale-105\"\n            )} />\n            <span className=\"font-medium\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary animate-pulse\"></div>\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAiBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IACrC;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,uMAAA,CAAA,QAAK;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAU,MAAM,mNAAA,CAAA,YAAS;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,2MAAA,CAAA,UAAO;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACpD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAkB,MAAM,iNAAA,CAAA,aAAc;IAAC;CAC9D;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,WAAW,aAAa,KAAK,IAAI;YACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gBAEH,MAAM,KAAK,IAAI;gBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gHACA,WACI,qEACA;gBAEN,OAAO;oBAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAAC;;kCAE3C,6LAAC,KAAK,IAAI;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,2BACA;;;;;;kCAEN,6LAAC;wBAAK,WAAU;kCAAe,KAAK,IAAI;;;;;;oBACvC,0BACC,6LAAC;wBAAI,WAAU;;;;;;;eAlBZ,KAAK,IAAI;;;;;QAsBpB;;;;;;AAGN;GAlCgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-elegant hover:shadow-elegant-lg transform hover:scale-105 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-primary text-white hover:opacity-90\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700\",\n        outline:\n          \"border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-white\",\n        secondary:\n          \"bg-gradient-secondary text-secondary-foreground hover:opacity-90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-elegant\",\n        link: \"text-primary underline-offset-4 hover:underline shadow-none\",\n        success: \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\",\n        warning: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600\",\n        info: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-10 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getInitials } from '@/lib/utils'\n\nexport function Sidebar() {\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const userDisplayName = user?.user_metadata?.full_name ||\n                          user?.email?.split('@')[0] ||\n                          'User'\n\n  const userInitials = getInitials(userDisplayName)\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b bg-gradient-primary\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"relative\">\n            <Crown className=\"h-7 w-7 text-white\" />\n            <Scissors className=\"h-4 w-4 text-white absolute -bottom-1 -right-1\" />\n          </div>\n          <div>\n            <span className=\"text-lg font-bold text-white\">皇家理发店</span>\n            <p className=\"text-xs text-white/80\">专业美发沙龙</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 px-4 py-6 bg-gradient-secondary\">\n        <Navigation />\n      </div>\n\n      {/* User info */}\n      <div className=\"border-t bg-card p-4\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant\">\n            <span className=\"text-sm font-bold text-white\">{userInitials}</span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-foreground truncate\">{userDisplayName}</p>\n            <p className=\"text-xs text-muted-foreground truncate\">{user?.email}</p>\n          </div>\n          <div className=\"h-2 w-2 rounded-full bg-green-500\" title=\"在线\"></div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex space-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={() => {/* TODO: Open profile settings */}}\n          >\n            <User className=\"h-3 w-3 mr-1\" />\n            个人资料\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={handleSignOut}\n          >\n            <LogOut className=\"h-3 w-3 mr-1\" />\n            退出登录\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM,eAAe,aACrB,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAC1B;IAExB,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;;8CACC,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAC/C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,6LAAC;wCAAE,WAAU;kDAA0C,MAAM;;;;;;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;gCAAoC,OAAM;;;;;;;;;;;;kCAI3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,KAAwC;;kDAEjD,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAvEgB;;QACY,sIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { Sidebar } from './sidebar'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background overflow-hidden\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto bg-gradient-to-br from-background via-secondary/30 to-muted/50\">\n        <div className=\"p-8 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;KAbgB", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Crown, Scissors } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireAdmin?: boolean\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallback \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"flex flex-col items-center space-y-4\">\n            <div className=\"relative animate-pulse\">\n              <Crown className=\"h-12 w-12 text-primary\" />\n              <Scissors className=\"h-8 w-8 text-accent absolute -bottom-2 -right-2\" />\n            </div>\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-semibold text-foreground mb-2\">Royal Cuts</h2>\n              <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n            </div>\n            <div className=\"flex space-x-1\">\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Show unauthorized if user is not logged in\n  if (!user) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"text-center\">\n            <div className=\"mb-4\">\n              <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              You need to be logged in to access this page.\n            </p>\n            <button\n              onClick={() => router.push('/auth/login')}\n              className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Go to Login\n            </button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Check admin requirement\n  if (requireAdmin) {\n    const isAdmin = user?.user_metadata?.role === 'admin' || \n                    user?.email?.endsWith('@royalcuts.com') ||\n                    user?.app_metadata?.role === 'admin'\n\n    if (!isAdmin) {\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n          <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n            <CardContent className=\"text-center\">\n              <div className=\"mb-4\">\n                <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Admin Access Required</h2>\n              <p className=\"text-muted-foreground mb-4\">\n                You need administrator privileges to access this page.\n              </p>\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Go to Dashboard\n              </button>\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: { requireAdmin?: boolean }\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={options?.requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Hook for checking authentication status\nexport function useRequireAuth(requireAdmin = false) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireAdmin) {\n        const isAdmin = user?.user_metadata?.role === 'admin' || \n                        user?.email?.endsWith('@royalcuts.com') ||\n                        user?.app_metadata?.role === 'admin'\n        \n        if (!isAdmin) {\n          router.push('/')\n          return\n        }\n      }\n    }\n  }, [user, loading, requireAdmin, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,KAAK,EACpB,QAAQ,EACY;;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAChG,6LAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5G;IAEA,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,OAAO,0BACL,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,0BAA0B;IAC1B,IAAI,cAAc;QAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;QAE7C,IAAI,CAAC,SAAS;YACZ,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;QAOX;IACF;IAEA,qBAAO;kBAAG;;AACZ;GAhGgB;;QAKY,sIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KANV;AAmGT,SAAS,SACd,SAAiC,EACjC,OAAoC;IAEpC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,6LAAC;YAAe,cAAc,SAAS;sBACrC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,eAAe,KAAK;;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,cAAc;oBAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;oBAE7C,IAAI,CAAC,SAAS;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;gBACF;YACF;QACF;mCAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,OAAO;QAAE;QAAM;IAAQ;AACzB;IAzBgB;;QACY,sIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/page-header.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: React.ReactNode\n  actions?: React.ReactNode\n  breadcrumbs?: Array<{\n    label: string\n    href?: string\n  }>\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon,\n  actions,\n  breadcrumbs,\n  className\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 mb-8\", className)}>\n      {/* Breadcrumbs */}\n      {breadcrumbs && breadcrumbs.length > 0 && (\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n            {breadcrumbs.map((crumb, index) => (\n              <li key={index} className=\"flex items-center\">\n                {index > 0 && (\n                  <svg\n                    className=\"h-4 w-4 mx-2\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n                {crumb.href ? (\n                  <a\n                    href={crumb.href}\n                    className=\"hover:text-foreground transition-colors\"\n                  >\n                    {crumb.label}\n                  </a>\n                ) : (\n                  <span className=\"text-foreground font-medium\">\n                    {crumb.label}\n                  </span>\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      )}\n\n      {/* Header Content */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-4\">\n          {icon && (\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              {icon}\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-muted-foreground text-lg mt-1\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions && (\n          <div className=\"flex items-center space-x-2\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\ninterface PageHeaderActionsProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function PageHeaderActions({ children, className }: PageHeaderActionsProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2\", className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface QuickStatsProps {\n  stats: Array<{\n    label: string\n    value: string | number\n    icon?: React.ReactNode\n    trend?: {\n      value: number\n      isPositive: boolean\n    }\n  }>\n  className?: string\n}\n\nexport function QuickStats({ stats, className }: QuickStatsProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\", className)}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-card rounded-xl p-4 border shadow-elegant hover:shadow-elegant-lg transition-all duration-300\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">\n                {stat.label}\n              </p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {stat.value}\n              </p>\n              {stat.trend && (\n                <p className={cn(\n                  \"text-xs flex items-center mt-1\",\n                  stat.trend.isPositive ? \"text-green-600\" : \"text-red-600\"\n                )}>\n                  <span className=\"mr-1\">\n                    {stat.trend.isPositive ? \"↗\" : \"↘\"}\n                  </span>\n                  {Math.abs(stat.trend.value)}%\n                </p>\n              )}\n            </div>\n            {stat.icon && (\n              <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n                {stat.icon}\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAeO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACX,SAAS,EACO;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAElC,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;gBAAI,WAAU;gBAAO,cAAW;0BAC/B,cAAA,6LAAC;oBAAG,WAAU;8BACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;4BAAe,WAAU;;gCACvB,QAAQ,mBACP,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAId,MAAM,IAAI,iBACT,6LAAC;oCACC,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;yDAGd,6LAAC;oCAAK,WAAU;8CACb,MAAM,KAAK;;;;;;;2BAvBT;;;;;;;;;;;;;;;0BAiCjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;KA3EgB;AAkFT,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;;;;;;AAGP;MANgB;AAqBT,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;kBAC7E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAEC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,KAAK,kBACT,6LAAC;oCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,kCACA,KAAK,KAAK,CAAC,UAAU,GAAG,mBAAmB;;sDAE3C,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM;;;;;;wCAEhC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK;wCAAE;;;;;;;;;;;;;wBAIjC,KAAK,IAAI,kBACR,6LAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI;;;;;;;;;;;;eAzBX;;;;;;;;;;AAiCf;MAtCgB", "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA,SAAS,6CACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/map-config.ts"], "sourcesContent": ["// 地图服务配置\nexport interface MapConfig {\n  provider: 'amap' | 'baidu' | 'auto'\n  apiKey: string\n  enableFallback: boolean\n}\n\nexport interface MapProviderConfig {\n  amap: {\n    apiKey: string\n    enabled: boolean\n  }\n  baidu: {\n    apiKey: string\n    enabled: boolean\n  }\n}\n\n// 默认配置\nconst DEFAULT_CONFIG: MapProviderConfig = {\n  amap: {\n    apiKey: process.env.NEXT_PUBLIC_AMAP_API_KEY || '',\n    enabled: true\n  },\n  baidu: {\n    apiKey: process.env.NEXT_PUBLIC_BAIDU_API_KEY || '',\n    enabled: true\n  }\n}\n\nexport class MapConfigManager {\n  private static config: MapProviderConfig = DEFAULT_CONFIG\n\n  /**\n   * 获取当前配置\n   */\n  static getConfig(): MapProviderConfig {\n    return this.config\n  }\n\n  /**\n   * 更新配置\n   */\n  static updateConfig(newConfig: Partial<MapProviderConfig>): void {\n    this.config = {\n      ...this.config,\n      ...newConfig\n    }\n  }\n\n  /**\n   * 检查高德地图是否可用\n   */\n  static isAmapAvailable(): boolean {\n    return this.config.amap.enabled && !!this.config.amap.apiKey && this.config.amap.apiKey !== 'your_amap_api_key'\n  }\n\n  /**\n   * 检查百度地图是否可用\n   */\n  static isBaiduAvailable(): boolean {\n    return this.config.baidu.enabled && !!this.config.baidu.apiKey && this.config.baidu.apiKey !== 'your_baidu_api_key'\n  }\n\n  /**\n   * 获取可用的地图服务提供商\n   */\n  static getAvailableProviders(): string[] {\n    const providers: string[] = []\n    \n    if (this.isAmapAvailable()) {\n      providers.push('高德地图')\n    }\n    \n    if (this.isBaiduAvailable()) {\n      providers.push('百度地图')\n    }\n    \n    if (providers.length === 0) {\n      providers.push('模拟服务')\n    }\n    \n    return providers\n  }\n\n  /**\n   * 获取推荐的地图服务提供商\n   */\n  static getRecommendedProvider(): 'amap' | 'baidu' | 'mock' {\n    if (this.isAmapAvailable()) {\n      return 'amap'\n    }\n    \n    if (this.isBaiduAvailable()) {\n      return 'baidu'\n    }\n    \n    return 'mock'\n  }\n\n  /**\n   * 设置高德地图API密钥\n   */\n  static setAmapApiKey(apiKey: string): void {\n    this.config.amap.apiKey = apiKey\n    this.config.amap.enabled = !!apiKey\n  }\n\n  /**\n   * 设置百度地图API密钥\n   */\n  static setBaiduApiKey(apiKey: string): void {\n    this.config.baidu.apiKey = apiKey\n    this.config.baidu.enabled = !!apiKey\n  }\n\n  /**\n   * 启用/禁用高德地图\n   */\n  static setAmapEnabled(enabled: boolean): void {\n    this.config.amap.enabled = enabled\n  }\n\n  /**\n   * 启用/禁用百度地图\n   */\n  static setBaiduEnabled(enabled: boolean): void {\n    this.config.baidu.enabled = enabled\n  }\n\n  /**\n   * 验证API密钥格式\n   */\n  static validateApiKey(provider: 'amap' | 'baidu', apiKey: string): boolean {\n    if (!apiKey || apiKey.trim().length === 0) {\n      return false\n    }\n\n    switch (provider) {\n      case 'amap':\n        // 高德地图API密钥通常是32位字符串\n        return /^[a-f0-9]{32}$/i.test(apiKey)\n      case 'baidu':\n        // 百度地图API密钥通常是24位字符串\n        return /^[a-zA-Z0-9]{24}$/.test(apiKey)\n      default:\n        return false\n    }\n  }\n\n  /**\n   * 获取API申请链接\n   */\n  static getApiApplicationUrl(provider: 'amap' | 'baidu'): string {\n    switch (provider) {\n      case 'amap':\n        return 'https://console.amap.com/dev/key/app'\n      case 'baidu':\n        return 'https://lbsyun.baidu.com/apiconsole/key'\n      default:\n        return ''\n    }\n  }\n\n  /**\n   * 获取API文档链接\n   */\n  static getApiDocumentationUrl(provider: 'amap' | 'baidu'): string {\n    switch (provider) {\n      case 'amap':\n        return 'https://lbs.amap.com/api/webservice/guide/api/georegeo'\n      case 'baidu':\n        return 'https://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-geocoding'\n      default:\n        return ''\n    }\n  }\n\n  /**\n   * 测试API密钥是否有效\n   */\n  static async testApiKey(provider: 'amap' | 'baidu', apiKey: string): Promise<boolean> {\n    try {\n      const testCoords = { latitude: 39.908823, longitude: 116.397470, accuracy: 100 } // 北京天安门坐标\n\n      switch (provider) {\n        case 'amap': {\n          const url = `https://restapi.amap.com/v3/geocode/regeo?key=${apiKey}&location=${testCoords.longitude},${testCoords.latitude}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`\n          const response = await fetch(url)\n          const data = await response.json()\n          return data.status === '1'\n        }\n        case 'baidu': {\n          const url = `https://api.map.baidu.com/reverse_geocoding/v3/?ak=${apiKey}&output=json&coordtype=wgs84ll&location=${testCoords.latitude},${testCoords.longitude}`\n          const response = await fetch(url)\n          const data = await response.json()\n          return data.status === 0\n        }\n        default:\n          return false\n      }\n    } catch (error) {\n      console.error(`测试${provider}API密钥失败:`, error)\n      return false\n    }\n  }\n\n  /**\n   * 获取配置状态报告\n   */\n  static getConfigStatus(): {\n    amap: { configured: boolean; valid: boolean; enabled: boolean }\n    baidu: { configured: boolean; valid: boolean; enabled: boolean }\n    hasValidProvider: boolean\n    recommendedProvider: string\n  } {\n    const amapConfigured = !!this.config.amap.apiKey && this.config.amap.apiKey !== 'your_amap_api_key'\n    const baiduConfigured = !!this.config.baidu.apiKey && this.config.baidu.apiKey !== 'your_baidu_api_key'\n    \n    const amapValid = amapConfigured && this.validateApiKey('amap', this.config.amap.apiKey)\n    const baiduValid = baiduConfigured && this.validateApiKey('baidu', this.config.baidu.apiKey)\n\n    return {\n      amap: {\n        configured: amapConfigured,\n        valid: amapValid,\n        enabled: this.config.amap.enabled\n      },\n      baidu: {\n        configured: baiduConfigured,\n        valid: baiduValid,\n        enabled: this.config.baidu.enabled\n      },\n      hasValidProvider: (amapValid && this.config.amap.enabled) || (baiduValid && this.config.baidu.enabled),\n      recommendedProvider: this.getRecommendedProvider()\n    }\n  }\n}\n\n// 地图服务错误类型\nexport enum MapServiceError {\n  API_KEY_MISSING = 'API_KEY_MISSING',\n  API_KEY_INVALID = 'API_KEY_INVALID',\n  API_QUOTA_EXCEEDED = 'API_QUOTA_EXCEEDED',\n  API_REQUEST_FAILED = 'API_REQUEST_FAILED',\n  COORDINATE_CONVERSION_FAILED = 'COORDINATE_CONVERSION_FAILED'\n}\n\n// 地图服务工具函数\nexport class MapServiceUtils {\n  /**\n   * 获取错误信息的中文描述\n   */\n  static getErrorMessage(error: MapServiceError, provider?: string): string {\n    const providerName = provider === 'amap' ? '高德地图' : provider === 'baidu' ? '百度地图' : '地图服务'\n    \n    switch (error) {\n      case MapServiceError.API_KEY_MISSING:\n        return `${providerName}API密钥未配置，请在设置中添加有效的API密钥`\n      case MapServiceError.API_KEY_INVALID:\n        return `${providerName}API密钥无效，请检查密钥是否正确`\n      case MapServiceError.API_QUOTA_EXCEEDED:\n        return `${providerName}API调用次数已超限，请稍后重试或升级服务`\n      case MapServiceError.API_REQUEST_FAILED:\n        return `${providerName}API请求失败，请检查网络连接`\n      case MapServiceError.COORDINATE_CONVERSION_FAILED:\n        return `坐标转换失败，请重试或使用其他地图服务`\n      default:\n        return `${providerName}服务暂时不可用，请稍后重试`\n    }\n  }\n\n  /**\n   * 检查是否为网络错误\n   */\n  static isNetworkError(error: any): boolean {\n    return error instanceof TypeError && error.message.includes('fetch')\n  }\n\n  /**\n   * 检查是否为API配额错误\n   */\n  static isQuotaError(error: any, provider: 'amap' | 'baidu'): boolean {\n    if (provider === 'amap') {\n      return error.message?.includes('DAILY_QUERY_OVER_LIMIT') || error.message?.includes('QUOTA_EXCEEDED')\n    } else if (provider === 'baidu') {\n      return error.status === 302 || error.message?.includes('quota')\n    }\n    return false\n  }\n}\n"], "names": [], "mappings": "AAAA,SAAS;;;;;;AAqBG;AAHZ,OAAO;AACP,MAAM,iBAAoC;IACxC,MAAM;QACJ,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;QAChD,SAAS;IACX;IACA,OAAO;QACL,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI;QACjD,SAAS;IACX;AACF;AAEO,MAAM;IACX,OAAe,SAA4B,eAAc;IAEzD;;GAEC,GACD,OAAO,YAA+B;QACpC,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA;;GAEC,GACD,OAAO,aAAa,SAAqC,EAAQ;QAC/D,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,SAAS;QACd;IACF;IAEA;;GAEC,GACD,OAAO,kBAA2B;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK;IAC9F;IAEA;;GAEC,GACD,OAAO,mBAA4B;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK;IACjG;IAEA;;GAEC,GACD,OAAO,wBAAkC;QACvC,MAAM,YAAsB,EAAE;QAE9B,IAAI,IAAI,CAAC,eAAe,IAAI;YAC1B,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,IAAI,CAAC,gBAAgB,IAAI;YAC3B,UAAU,IAAI,CAAC;QACjB;QAEA,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,UAAU,IAAI,CAAC;QACjB;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,yBAAoD;QACzD,IAAI,IAAI,CAAC,eAAe,IAAI;YAC1B,OAAO;QACT;QAEA,IAAI,IAAI,CAAC,gBAAgB,IAAI;YAC3B,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,cAAc,MAAc,EAAQ;QACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IAC/B;IAEA;;GAEC,GACD,OAAO,eAAe,MAAc,EAAQ;QAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;IAChC;IAEA;;GAEC,GACD,OAAO,eAAe,OAAgB,EAAQ;QAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG;IAC7B;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAgB,EAAQ;QAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG;IAC9B;IAEA;;GAEC,GACD,OAAO,eAAe,QAA0B,EAAE,MAAc,EAAW;QACzE,IAAI,CAAC,UAAU,OAAO,IAAI,GAAG,MAAM,KAAK,GAAG;YACzC,OAAO;QACT;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAqB;gBACrB,OAAO,kBAAkB,IAAI,CAAC;YAChC,KAAK;gBACH,qBAAqB;gBACrB,OAAO,oBAAoB,IAAI,CAAC;YAClC;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,OAAO,qBAAqB,QAA0B,EAAU;QAC9D,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,OAAO,uBAAuB,QAA0B,EAAU;QAChE,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,aAAa,WAAW,QAA0B,EAAE,MAAc,EAAoB;QACpF,IAAI;YACF,MAAM,aAAa;gBAAE,UAAU;gBAAW,WAAW;gBAAY,UAAU;YAAI,EAAE,UAAU;;YAE3F,OAAQ;gBACN,KAAK;oBAAQ;wBACX,MAAM,MAAM,CAAC,8CAA8C,EAAE,OAAO,UAAU,EAAE,WAAW,SAAS,CAAC,CAAC,EAAE,WAAW,QAAQ,CAAC,6DAA6D,CAAC;wBAC1L,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,OAAO,KAAK,MAAM,KAAK;oBACzB;gBACA,KAAK;oBAAS;wBACZ,MAAM,MAAM,CAAC,mDAAmD,EAAE,OAAO,wCAAwC,EAAE,WAAW,QAAQ,CAAC,CAAC,EAAE,WAAW,SAAS,EAAE;wBAChK,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,OAAO,KAAK,MAAM,KAAK;oBACzB;gBACA;oBACE,OAAO;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,EAAE,EAAE,SAAS,QAAQ,CAAC,EAAE;YACvC,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAO,kBAKL;QACA,MAAM,iBAAiB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK;QAChF,MAAM,kBAAkB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK;QAEnF,MAAM,YAAY,kBAAkB,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;QACvF,MAAM,aAAa,mBAAmB,IAAI,CAAC,cAAc,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;QAE3F,OAAO;YACL,MAAM;gBACJ,YAAY;gBACZ,OAAO;gBACP,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;YACnC;YACA,OAAO;gBACL,YAAY;gBACZ,OAAO;gBACP,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;YACpC;YACA,kBAAkB,AAAC,aAAa,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,IAAM,cAAc,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;YACrG,qBAAqB,IAAI,CAAC,sBAAsB;QAClD;IACF;AACF;AAGO,IAAA,AAAK,yCAAA;;;;;;WAAA;;AASL,MAAM;IACX;;GAEC,GACD,OAAO,gBAAgB,KAAsB,EAAE,QAAiB,EAAU;QACxE,MAAM,eAAe,aAAa,SAAS,SAAS,aAAa,UAAU,SAAS;QAEpF,OAAQ;YACN;gBACE,OAAO,GAAG,aAAa,wBAAwB,CAAC;YAClD;gBACE,OAAO,GAAG,aAAa,iBAAiB,CAAC;YAC3C;gBACE,OAAO,GAAG,aAAa,qBAAqB,CAAC;YAC/C;gBACE,OAAO,GAAG,aAAa,eAAe,CAAC;YACzC;gBACE,OAAO,CAAC,mBAAmB,CAAC;YAC9B;gBACE,OAAO,GAAG,aAAa,aAAa,CAAC;QACzC;IACF;IAEA;;GAEC,GACD,OAAO,eAAe,KAAU,EAAW;QACzC,OAAO,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC;IAC9D;IAEA;;GAEC,GACD,OAAO,aAAa,KAAU,EAAE,QAA0B,EAAW;QACnE,IAAI,aAAa,QAAQ;YACvB,OAAO,MAAM,OAAO,EAAE,SAAS,6BAA6B,MAAM,OAAO,EAAE,SAAS;QACtF,OAAO,IAAI,aAAa,SAAS;YAC/B,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,OAAO,EAAE,SAAS;QACzD;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/geolocation.ts"], "sourcesContent": ["import { MapConfigManager, MapServiceError, MapServiceUtils } from './map-config'\n\n// 地理定位工具\nexport interface LocationCoordinates {\n  latitude: number\n  longitude: number\n  accuracy: number\n}\n\nexport interface AddressInfo {\n  formatted_address: string\n  country: string\n  province: string\n  city: string\n  district: string\n  street: string\n  street_number: string\n}\n\nexport interface GeolocationResult {\n  coordinates: LocationCoordinates\n  address: AddressInfo\n}\n\n// 地理定位错误类型\nexport enum GeolocationError {\n  PERMISSION_DENIED = 'PERMISSION_DENIED',\n  POSITION_UNAVAILABLE = 'POSITION_UNAVAILABLE',\n  TIMEOUT = 'TIMEOUT',\n  NOT_SUPPORTED = 'NOT_SUPPORTED',\n  REVERSE_GEOCODING_FAILED = 'REVERSE_GEOCODING_FAILED'\n}\n\nexport class GeolocationService {\n  private static readonly TIMEOUT = 10000 // 10秒超时\n  private static readonly MAX_AGE = 300000 // 5分钟缓存\n\n  /**\n   * 检查浏览器是否支持地理定位\n   */\n  static isSupported(): boolean {\n    return 'geolocation' in navigator\n  }\n\n  /**\n   * 获取当前位置坐标\n   */\n  static async getCurrentPosition(): Promise<LocationCoordinates> {\n    if (!this.isSupported()) {\n      throw new Error(GeolocationError.NOT_SUPPORTED)\n    }\n\n    return new Promise((resolve, reject) => {\n      const options: PositionOptions = {\n        enableHighAccuracy: true,\n        timeout: this.TIMEOUT,\n        maximumAge: this.MAX_AGE\n      }\n\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          resolve({\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            accuracy: position.coords.accuracy\n          })\n        },\n        (error) => {\n          switch (error.code) {\n            case error.PERMISSION_DENIED:\n              reject(new Error(GeolocationError.PERMISSION_DENIED))\n              break\n            case error.POSITION_UNAVAILABLE:\n              reject(new Error(GeolocationError.POSITION_UNAVAILABLE))\n              break\n            case error.TIMEOUT:\n              reject(new Error(GeolocationError.TIMEOUT))\n              break\n            default:\n              reject(new Error(GeolocationError.POSITION_UNAVAILABLE))\n              break\n          }\n        },\n        options\n      )\n    })\n  }\n\n  /**\n   * 使用地图API进行逆地理编码（坐标转地址）\n   * 支持高德地图和百度地图API，自动选择最佳服务\n   */\n  static async reverseGeocode(coordinates: LocationCoordinates): Promise<AddressInfo> {\n    const config = MapConfigManager.getConfig()\n    const errors: string[] = []\n\n    // 根据配置优先级尝试不同的地图服务\n    if (MapConfigManager.isAmapAvailable()) {\n      try {\n        return await this.amapReverseGeocode(coordinates)\n      } catch (amapError) {\n        const errorMsg = `高德地图API调用失败: ${amapError}`\n        console.warn(errorMsg)\n        errors.push(errorMsg)\n      }\n    }\n\n    if (MapConfigManager.isBaiduAvailable()) {\n      try {\n        return await this.baiduReverseGeocode(coordinates)\n      } catch (baiduError) {\n        const errorMsg = `百度地图API调用失败: ${baiduError}`\n        console.warn(errorMsg)\n        errors.push(errorMsg)\n      }\n    }\n\n    // 如果所有API都失败，使用模拟数据作为降级方案\n    console.warn('所有地图API都不可用，使用模拟数据:', errors)\n    return this.mockReverseGeocode(coordinates)\n  }\n\n  /**\n   * 高德地图逆地理编码API\n   * 文档：https://lbs.amap.com/api/webservice/guide/api/georegeo\n   */\n  private static async amapReverseGeocode(coordinates: LocationCoordinates): Promise<AddressInfo> {\n    const config = MapConfigManager.getConfig()\n    const apiKey = config.amap.apiKey\n\n    if (!apiKey || apiKey === 'your_amap_api_key') {\n      throw new Error(MapServiceError.API_KEY_MISSING)\n    }\n\n    const url = `https://restapi.amap.com/v3/geocode/regeo?key=${apiKey}&location=${coordinates.longitude},${coordinates.latitude}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`\n\n    try {\n      const response = await fetch(url)\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n\n      if (data.status !== '1') {\n        if (data.info?.includes('DAILY_QUERY_OVER_LIMIT') || data.info?.includes('QUOTA_EXCEEDED')) {\n          throw new Error(MapServiceError.API_QUOTA_EXCEEDED)\n        }\n        if (data.info?.includes('INVALID_USER_KEY')) {\n          throw new Error(MapServiceError.API_KEY_INVALID)\n        }\n        throw new Error(`${MapServiceError.API_REQUEST_FAILED}: ${data.info}`)\n      }\n\n      const regeocode = data.regeocode\n      const addressComponent = regeocode.addressComponent\n\n      return {\n        formatted_address: regeocode.formatted_address,\n        country: addressComponent.country || '中国',\n        province: addressComponent.province || '',\n        city: addressComponent.city || addressComponent.province || '',\n        district: addressComponent.district || '',\n        street: addressComponent.township || addressComponent.streetNumber?.street || '',\n        street_number: addressComponent.streetNumber?.number || ''\n      }\n    } catch (error) {\n      if (MapServiceUtils.isNetworkError(error)) {\n        throw new Error(MapServiceError.API_REQUEST_FAILED)\n      }\n      throw error\n    }\n  }\n\n  /**\n   * 百度地图逆地理编码API\n   * 文档：https://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-geocoding-abroad\n   */\n  private static async baiduReverseGeocode(coordinates: LocationCoordinates): Promise<AddressInfo> {\n    const config = MapConfigManager.getConfig()\n    const apiKey = config.baidu.apiKey\n\n    if (!apiKey || apiKey === 'your_baidu_api_key') {\n      throw new Error(MapServiceError.API_KEY_MISSING)\n    }\n\n    try {\n      // 将GPS坐标转换为百度坐标系\n      const bdCoords = await this.convertToBaiduCoords(coordinates, apiKey)\n\n      const url = `https://api.map.baidu.com/reverse_geocoding/v3/?ak=${apiKey}&output=json&coordtype=bd09ll&location=${bdCoords.latitude},${bdCoords.longitude}`\n\n      const response = await fetch(url)\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n\n      if (data.status !== 0) {\n        if (data.status === 302 || data.message?.includes('quota')) {\n          throw new Error(MapServiceError.API_QUOTA_EXCEEDED)\n        }\n        if (data.status === 101 || data.message?.includes('AK')) {\n          throw new Error(MapServiceError.API_KEY_INVALID)\n        }\n        throw new Error(`${MapServiceError.API_REQUEST_FAILED}: ${data.message}`)\n      }\n\n      const result = data.result\n      const addressComponent = result.addressComponent\n\n      return {\n        formatted_address: result.formatted_address,\n        country: addressComponent.country || '中国',\n        province: addressComponent.province || '',\n        city: addressComponent.city || '',\n        district: addressComponent.district || '',\n        street: addressComponent.street || '',\n        street_number: addressComponent.street_number || ''\n      }\n    } catch (error) {\n      if (MapServiceUtils.isNetworkError(error)) {\n        throw new Error(MapServiceError.API_REQUEST_FAILED)\n      }\n      throw error\n    }\n  }\n\n  /**\n   * 将GPS坐标转换为百度坐标系\n   */\n  private static async convertToBaiduCoords(coordinates: LocationCoordinates, apiKey: string): Promise<LocationCoordinates> {\n    const url = `https://api.map.baidu.com/geoconv/v1/?coords=${coordinates.longitude},${coordinates.latitude}&from=1&to=5&ak=${apiKey}`\n\n    try {\n      const response = await fetch(url)\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n\n      if (data.status !== 0) {\n        throw new Error(MapServiceError.COORDINATE_CONVERSION_FAILED)\n      }\n\n      const result = data.result[0]\n      return {\n        latitude: result.y,\n        longitude: result.x,\n        accuracy: coordinates.accuracy\n      }\n    } catch (error) {\n      if (MapServiceUtils.isNetworkError(error)) {\n        throw new Error(MapServiceError.API_REQUEST_FAILED)\n      }\n      throw new Error(MapServiceError.COORDINATE_CONVERSION_FAILED)\n    }\n  }\n\n  /**\n   * 模拟逆地理编码（用于演示和降级）\n   * 当真实API不可用时使用\n   */\n  private static mockReverseGeocode(coordinates: LocationCoordinates): AddressInfo {\n    // 根据坐标范围模拟不同的地址\n    const { latitude, longitude } = coordinates\n\n    // 北京地区\n    if (latitude >= 39.4 && latitude <= 41.0 && longitude >= 115.7 && longitude <= 117.4) {\n      return {\n        formatted_address: '北京市朝阳区三里屯街道工人体育场北路8号',\n        country: '中国',\n        province: '北京市',\n        city: '北京市',\n        district: '朝阳区',\n        street: '工人体育场北路',\n        street_number: '8号'\n      }\n    }\n    \n    // 上海地区\n    if (latitude >= 30.7 && latitude <= 31.9 && longitude >= 120.8 && longitude <= 122.2) {\n      return {\n        formatted_address: '上海市黄浦区南京东路399号',\n        country: '中国',\n        province: '上海市',\n        city: '上海市',\n        district: '黄浦区',\n        street: '南京东路',\n        street_number: '399号'\n      }\n    }\n\n    // 广州地区\n    if (latitude >= 22.7 && latitude <= 23.9 && longitude >= 112.9 && longitude <= 114.0) {\n      return {\n        formatted_address: '广东省广州市天河区天河路208号',\n        country: '中国',\n        province: '广东省',\n        city: '广州市',\n        district: '天河区',\n        street: '天河路',\n        street_number: '208号'\n      }\n    }\n\n    // 深圳地区\n    if (latitude >= 22.4 && latitude <= 22.8 && longitude >= 113.7 && longitude <= 114.6) {\n      return {\n        formatted_address: '广东省深圳市南山区深南大道9988号',\n        country: '中国',\n        province: '广东省',\n        city: '深圳市',\n        district: '南山区',\n        street: '深南大道',\n        street_number: '9988号'\n      }\n    }\n\n    // 默认地址\n    return {\n      formatted_address: `中国某地区 (${latitude.toFixed(4)}, ${longitude.toFixed(4)})`,\n      country: '中国',\n      province: '未知省份',\n      city: '未知城市',\n      district: '未知区域',\n      street: '未知街道',\n      street_number: ''\n    }\n  }\n\n  /**\n   * 获取当前位置并转换为地址\n   */\n  static async getCurrentLocationAddress(): Promise<GeolocationResult> {\n    try {\n      const coordinates = await this.getCurrentPosition()\n      const address = await this.reverseGeocode(coordinates)\n      \n      return {\n        coordinates,\n        address\n      }\n    } catch (error) {\n      throw error\n    }\n  }\n\n  /**\n   * 获取错误信息的中文描述\n   */\n  static getErrorMessage(error: string): string {\n    switch (error) {\n      case GeolocationError.PERMISSION_DENIED:\n        return '用户拒绝了定位请求。请在浏览器设置中允许定位权限。'\n      case GeolocationError.POSITION_UNAVAILABLE:\n        return '无法获取位置信息。请检查GPS是否开启或网络连接。'\n      case GeolocationError.TIMEOUT:\n        return '定位请求超时。请重试或检查网络连接。'\n      case GeolocationError.NOT_SUPPORTED:\n        return '您的浏览器不支持地理定位功能。'\n      case GeolocationError.REVERSE_GEOCODING_FAILED:\n        return '地址解析失败。请手动输入地址或重试。'\n      default:\n        return '定位失败，请重试或手动输入地址。'\n    }\n  }\n}\n\n// 地址格式化工具\nexport class AddressFormatter {\n  /**\n   * 格式化完整地址\n   */\n  static formatFullAddress(address: AddressInfo): string {\n    return address.formatted_address\n  }\n\n  /**\n   * 格式化简短地址\n   */\n  static formatShortAddress(address: AddressInfo): string {\n    const parts = []\n    if (address.city && address.city !== address.province) {\n      parts.push(address.city)\n    }\n    if (address.district) {\n      parts.push(address.district)\n    }\n    if (address.street) {\n      parts.push(address.street)\n    }\n    if (address.street_number) {\n      parts.push(address.street_number)\n    }\n    return parts.join('')\n  }\n\n  /**\n   * 验证地址格式\n   */\n  static validateAddress(address: string): boolean {\n    // 基本的地址验证\n    if (!address || address.trim().length < 5) {\n      return false\n    }\n    \n    // 检查是否包含中文字符（适用于中国地址）\n    const chineseRegex = /[\\u4e00-\\u9fa5]/\n    return chineseRegex.test(address)\n  }\n}\n\n// 坐标工具\nexport class CoordinateUtils {\n  /**\n   * 计算两点之间的距离（米）\n   */\n  static calculateDistance(\n    coord1: LocationCoordinates,\n    coord2: LocationCoordinates\n  ): number {\n    const R = 6371e3 // 地球半径（米）\n    const φ1 = (coord1.latitude * Math.PI) / 180\n    const φ2 = (coord2.latitude * Math.PI) / 180\n    const Δφ = ((coord2.latitude - coord1.latitude) * Math.PI) / 180\n    const Δλ = ((coord2.longitude - coord1.longitude) * Math.PI) / 180\n\n    const a =\n      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +\n      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2)\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))\n\n    return R * c\n  }\n\n  /**\n   * 格式化坐标显示\n   */\n  static formatCoordinates(coordinates: LocationCoordinates): string {\n    return `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAyBO,IAAA,AAAK,0CAAA;;;;;;WAAA;;AAQL,MAAM;IACX,OAAwB,UAAU,MAAM,QAAQ;KAAT;IACvC,OAAwB,UAAU,OAAO,QAAQ;KAAT;IAExC;;GAEC,GACD,OAAO,cAAuB;QAC5B,OAAO,iBAAiB;IAC1B;IAEA;;GAEC,GACD,aAAa,qBAAmD;QAC9D,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;YACvB,MAAM,IAAI;QACZ;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,UAA2B;gBAC/B,oBAAoB;gBACpB,SAAS,IAAI,CAAC,OAAO;gBACrB,YAAY,IAAI,CAAC,OAAO;YAC1B;YAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,CAAC;gBACC,QAAQ;oBACN,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;oBACpC,UAAU,SAAS,MAAM,CAAC,QAAQ;gBACpC;YACF,GACA,CAAC;gBACC,OAAQ,MAAM,IAAI;oBAChB,KAAK,MAAM,iBAAiB;wBAC1B,OAAO,IAAI;wBACX;oBACF,KAAK,MAAM,oBAAoB;wBAC7B,OAAO,IAAI;wBACX;oBACF,KAAK,MAAM,OAAO;wBAChB,OAAO,IAAI;wBACX;oBACF;wBACE,OAAO,IAAI;wBACX;gBACJ;YACF,GACA;QAEJ;IACF;IAEA;;;GAGC,GACD,aAAa,eAAe,WAAgC,EAAwB;QAClF,MAAM,SAAS,8HAAA,CAAA,mBAAgB,CAAC,SAAS;QACzC,MAAM,SAAmB,EAAE;QAE3B,mBAAmB;QACnB,IAAI,8HAAA,CAAA,mBAAgB,CAAC,eAAe,IAAI;YACtC,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC;YACvC,EAAE,OAAO,WAAW;gBAClB,MAAM,WAAW,CAAC,aAAa,EAAE,WAAW;gBAC5C,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC;YACd;QACF;QAEA,IAAI,8HAAA,CAAA,mBAAgB,CAAC,gBAAgB,IAAI;YACvC,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC;YACxC,EAAE,OAAO,YAAY;gBACnB,MAAM,WAAW,CAAC,aAAa,EAAE,YAAY;gBAC7C,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC;YACd;QACF;QAEA,0BAA0B;QAC1B,QAAQ,IAAI,CAAC,uBAAuB;QACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC;IAEA;;;GAGC,GACD,aAAqB,mBAAmB,WAAgC,EAAwB;QAC9F,MAAM,SAAS,8HAAA,CAAA,mBAAgB,CAAC,SAAS;QACzC,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM;QAEjC,IAAI,CAAC,UAAU,WAAW,qBAAqB;YAC7C,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,eAAe;QACjD;QAEA,MAAM,MAAM,CAAC,8CAA8C,EAAE,OAAO,UAAU,EAAE,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,CAAC,6DAA6D,CAAC;QAE5L,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,MAAM,KAAK,KAAK;gBACvB,IAAI,KAAK,IAAI,EAAE,SAAS,6BAA6B,KAAK,IAAI,EAAE,SAAS,mBAAmB;oBAC1F,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,kBAAkB;gBACpD;gBACA,IAAI,KAAK,IAAI,EAAE,SAAS,qBAAqB;oBAC3C,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,eAAe;gBACjD;gBACA,MAAM,IAAI,MAAM,GAAG,8HAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,EAAE,EAAE,KAAK,IAAI,EAAE;YACvE;YAEA,MAAM,YAAY,KAAK,SAAS;YAChC,MAAM,mBAAmB,UAAU,gBAAgB;YAEnD,OAAO;gBACL,mBAAmB,UAAU,iBAAiB;gBAC9C,SAAS,iBAAiB,OAAO,IAAI;gBACrC,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,MAAM,iBAAiB,IAAI,IAAI,iBAAiB,QAAQ,IAAI;gBAC5D,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,QAAQ,iBAAiB,QAAQ,IAAI,iBAAiB,YAAY,EAAE,UAAU;gBAC9E,eAAe,iBAAiB,YAAY,EAAE,UAAU;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,IAAI,8HAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,QAAQ;gBACzC,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,kBAAkB;YACpD;YACA,MAAM;QACR;IACF;IAEA;;;GAGC,GACD,aAAqB,oBAAoB,WAAgC,EAAwB;QAC/F,MAAM,SAAS,8HAAA,CAAA,mBAAgB,CAAC,SAAS;QACzC,MAAM,SAAS,OAAO,KAAK,CAAC,MAAM;QAElC,IAAI,CAAC,UAAU,WAAW,sBAAsB;YAC9C,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,eAAe;QACjD;QAEA,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa;YAE9D,MAAM,MAAM,CAAC,mDAAmD,EAAE,OAAO,uCAAuC,EAAE,SAAS,QAAQ,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE;YAE3J,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,EAAE,SAAS,UAAU;oBAC1D,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,kBAAkB;gBACpD;gBACA,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,EAAE,SAAS,OAAO;oBACvD,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,eAAe;gBACjD;gBACA,MAAM,IAAI,MAAM,GAAG,8HAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,EAAE,EAAE,KAAK,OAAO,EAAE;YAC1E;YAEA,MAAM,SAAS,KAAK,MAAM;YAC1B,MAAM,mBAAmB,OAAO,gBAAgB;YAEhD,OAAO;gBACL,mBAAmB,OAAO,iBAAiB;gBAC3C,SAAS,iBAAiB,OAAO,IAAI;gBACrC,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,MAAM,iBAAiB,IAAI,IAAI;gBAC/B,UAAU,iBAAiB,QAAQ,IAAI;gBACvC,QAAQ,iBAAiB,MAAM,IAAI;gBACnC,eAAe,iBAAiB,aAAa,IAAI;YACnD;QACF,EAAE,OAAO,OAAO;YACd,IAAI,8HAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,QAAQ;gBACzC,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,kBAAkB;YACpD;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAqB,qBAAqB,WAAgC,EAAE,MAAc,EAAgC;QACxH,MAAM,MAAM,CAAC,6CAA6C,EAAE,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,CAAC,gBAAgB,EAAE,QAAQ;QAEpI,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;YACnE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,4BAA4B;YAC9D;YAEA,MAAM,SAAS,KAAK,MAAM,CAAC,EAAE;YAC7B,OAAO;gBACL,UAAU,OAAO,CAAC;gBAClB,WAAW,OAAO,CAAC;gBACnB,UAAU,YAAY,QAAQ;YAChC;QACF,EAAE,OAAO,OAAO;YACd,IAAI,8HAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,QAAQ;gBACzC,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,kBAAkB;YACpD;YACA,MAAM,IAAI,MAAM,8HAAA,CAAA,kBAAe,CAAC,4BAA4B;QAC9D;IACF;IAEA;;;GAGC,GACD,OAAe,mBAAmB,WAAgC,EAAe;QAC/E,gBAAgB;QAChB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;QAEhC,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,IAAI,YAAY,QAAQ,YAAY,QAAQ,aAAa,SAAS,aAAa,OAAO;YACpF,OAAO;gBACL,mBAAmB;gBACnB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;QACP,OAAO;YACL,mBAAmB,CAAC,OAAO,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5E,SAAS;YACT,UAAU;YACV,MAAM;YACN,UAAU;YACV,QAAQ;YACR,eAAe;QACjB;IACF;IAEA;;GAEC,GACD,aAAa,4BAAwD;QACnE,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,kBAAkB;YACjD,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,CAAC;YAE1C,OAAO;gBACL;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAa,EAAU;QAC5C,OAAQ;YACN;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;YACT;gBACE,OAAO;QACX;IACF;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,kBAAkB,OAAoB,EAAU;QACrD,OAAO,QAAQ,iBAAiB;IAClC;IAEA;;GAEC,GACD,OAAO,mBAAmB,OAAoB,EAAU;QACtD,MAAM,QAAQ,EAAE;QAChB,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,KAAK,QAAQ,QAAQ,EAAE;YACrD,MAAM,IAAI,CAAC,QAAQ,IAAI;QACzB;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,MAAM,IAAI,CAAC,QAAQ,QAAQ;QAC7B;QACA,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,IAAI,CAAC,QAAQ,MAAM;QAC3B;QACA,IAAI,QAAQ,aAAa,EAAE;YACzB,MAAM,IAAI,CAAC,QAAQ,aAAa;QAClC;QACA,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAW;QAC/C,UAAU;QACV,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,GAAG,GAAG;YACzC,OAAO;QACT;QAEA,sBAAsB;QACtB,MAAM,eAAe;QACrB,OAAO,aAAa,IAAI,CAAC;IAC3B;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,kBACL,MAA2B,EAC3B,MAA2B,EACnB;QACR,MAAM,IAAI,OAAO,UAAU;;QAC3B,MAAM,KAAK,AAAC,OAAO,QAAQ,GAAG,KAAK,EAAE,GAAI;QACzC,MAAM,KAAK,AAAC,OAAO,QAAQ,GAAG,KAAK,EAAE,GAAI;QACzC,MAAM,KAAK,AAAC,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAI;QAC7D,MAAM,KAAK,AAAC,CAAC,OAAO,SAAS,GAAG,OAAO,SAAS,IAAI,KAAK,EAAE,GAAI;QAE/D,MAAM,IACJ,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,KAAK,KACjC,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,KAAK;QACjE,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;QAErD,OAAO,IAAI;IACb;IAEA;;GAEC,GACD,OAAO,kBAAkB,WAAgC,EAAU;QACjE,OAAO,GAAG,YAAY,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,SAAS,CAAC,OAAO,CAAC,IAAI;IAClF;AACF", "debugId": null}}, {"offset": {"line": 1923, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/address-input.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MapPin, Loader2, AlertCircle, CheckCircle } from 'lucide-react'\nimport { Button } from './button'\nimport { Input } from './input'\nimport { \n  GeolocationService, \n  GeolocationError,\n  type GeolocationResult \n} from '@/lib/geolocation'\n\ninterface AddressInputProps {\n  value: string\n  onChange: (value: string) => void\n  placeholder?: string\n  disabled?: boolean\n  className?: string\n}\n\nexport function AddressInput({\n  value,\n  onChange,\n  placeholder = \"请输入地址\",\n  disabled = false,\n  className = \"\"\n}: AddressInputProps) {\n  const [isLocating, setIsLocating] = useState(false)\n  const [locationStatus, setLocationStatus] = useState<'idle' | 'success' | 'error'>('idle')\n  const [locationError, setLocationError] = useState<string>('')\n\n  const handleGetLocation = async () => {\n    if (!GeolocationService.isSupported()) {\n      setLocationError('您的浏览器不支持地理定位功能')\n      setLocationStatus('error')\n      return\n    }\n\n    setIsLocating(true)\n    setLocationStatus('idle')\n    setLocationError('')\n\n    try {\n      const result: GeolocationResult = await GeolocationService.getCurrentLocationAddress()\n      \n      // 使用获取到的地址\n      onChange(result.address.formatted_address)\n      setLocationStatus('success')\n      \n      // 3秒后清除成功状态\n      setTimeout(() => {\n        setLocationStatus('idle')\n      }, 3000)\n      \n    } catch (error) {\n      const errorMessage = GeolocationService.getErrorMessage((error as Error).message)\n      setLocationError(errorMessage)\n      setLocationStatus('error')\n      \n      // 5秒后清除错误状态\n      setTimeout(() => {\n        setLocationStatus('idle')\n        setLocationError('')\n      }, 5000)\n    } finally {\n      setIsLocating(false)\n    }\n  }\n\n  const getLocationButtonVariant = () => {\n    switch (locationStatus) {\n      case 'success':\n        return 'default'\n      case 'error':\n        return 'destructive'\n      default:\n        return 'outline'\n    }\n  }\n\n  const getLocationButtonIcon = () => {\n    if (isLocating) {\n      return <Loader2 className=\"h-4 w-4 animate-spin\" />\n    }\n    \n    switch (locationStatus) {\n      case 'success':\n        return <CheckCircle className=\"h-4 w-4\" />\n      case 'error':\n        return <AlertCircle className=\"h-4 w-4\" />\n      default:\n        return <MapPin className=\"h-4 w-4\" />\n    }\n  }\n\n  const getLocationButtonText = () => {\n    if (isLocating) {\n      return '定位中...'\n    }\n    \n    switch (locationStatus) {\n      case 'success':\n        return '定位成功'\n      case 'error':\n        return '定位失败'\n      default:\n        return '获取位置'\n    }\n  }\n\n  return (\n    <div className=\"space-y-2\">\n      <div className=\"flex space-x-2\">\n        <Input\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          placeholder={placeholder}\n          disabled={disabled}\n          className={className}\n        />\n        <Button\n          type=\"button\"\n          variant={getLocationButtonVariant()}\n          size=\"default\"\n          onClick={handleGetLocation}\n          disabled={disabled || isLocating}\n          className=\"flex-shrink-0 min-w-[100px]\"\n        >\n          {getLocationButtonIcon()}\n          <span className=\"ml-2\">{getLocationButtonText()}</span>\n        </Button>\n      </div>\n      \n      {/* 错误信息显示 */}\n      {locationStatus === 'error' && locationError && (\n        <div className=\"flex items-start space-x-2 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n          <AlertCircle className=\"h-4 w-4 text-red-500 mt-0.5 flex-shrink-0\" />\n          <div>\n            <p className=\"text-sm text-red-700 dark:text-red-300 font-medium\">\n              定位失败\n            </p>\n            <p className=\"text-sm text-red-600 dark:text-red-400\">\n              {locationError}\n            </p>\n          </div>\n        </div>\n      )}\n      \n      {/* 成功信息显示 */}\n      {locationStatus === 'success' && (\n        <div className=\"flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg\">\n          <CheckCircle className=\"h-4 w-4 text-green-500\" />\n          <p className=\"text-sm text-green-700 dark:text-green-300\">\n            已成功获取当前位置地址\n          </p>\n        </div>\n      )}\n      \n      {/* 定位提示信息 */}\n      {locationStatus === 'idle' && (\n        <div className=\"text-xs text-muted-foreground\">\n          点击\"获取位置\"按钮可自动获取当前位置作为商户地址\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAoBO,SAAS,aAAa,EAC3B,KAAK,EACL,QAAQ,EACR,cAAc,OAAO,EACrB,WAAW,KAAK,EAChB,YAAY,EAAE,EACI;;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACnF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,oBAAoB;QACxB,IAAI,CAAC,4HAAA,CAAA,qBAAkB,CAAC,WAAW,IAAI;YACrC,iBAAiB;YACjB,kBAAkB;YAClB;QACF;QAEA,cAAc;QACd,kBAAkB;QAClB,iBAAiB;QAEjB,IAAI;YACF,MAAM,SAA4B,MAAM,4HAAA,CAAA,qBAAkB,CAAC,yBAAyB;YAEpF,WAAW;YACX,SAAS,OAAO,OAAO,CAAC,iBAAiB;YACzC,kBAAkB;YAElB,YAAY;YACZ,WAAW;gBACT,kBAAkB;YACpB,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,4HAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC,AAAC,MAAgB,OAAO;YAChF,iBAAiB;YACjB,kBAAkB;YAElB,YAAY;YACZ,WAAW;gBACT,kBAAkB;gBAClB,iBAAiB;YACnB,GAAG;QACL,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,2BAA2B;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,YAAY;YACd,qBAAO,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,YAAY;YACd,OAAO;QACT;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBACJ,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,aAAa;wBACb,UAAU;wBACV,WAAW;;;;;;kCAEb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,MAAK;wBACL,SAAS;wBACT,UAAU,YAAY;wBACtB,WAAU;;4BAET;0CACD,6LAAC;gCAAK,WAAU;0CAAQ;;;;;;;;;;;;;;;;;;YAK3B,mBAAmB,WAAW,+BAC7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAqD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;;YAOR,mBAAmB,2BAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAA6C;;;;;;;;;;;;YAO7D,mBAAmB,wBAClB,6LAAC;gBAAI,WAAU;0BAAgC;;;;;;;;;;;;AAMvD;GAlJgB;KAAA", "debugId": null}}, {"offset": {"line": 2177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        purple:\n          \"border-transparent bg-purple-500 text-white hover:bg-purple-600\",\n        pink:\n          \"border-transparent bg-pink-500 text-white hover:bg-pink-600\",\n        indigo:\n          \"border-transparent bg-indigo-500 text-white hover:bg-indigo-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,QACE;YACF,MACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 2231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/settings/map-api-config.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  MapPin, \n  ExternalLink, \n  CheckCircle, \n  XCircle, \n  AlertCircle,\n  Loader2,\n  Eye,\n  EyeOff\n} from 'lucide-react'\nimport { MapConfigManager, type MapProviderConfig } from '@/lib/map-config'\n\ninterface MapApiConfigProps {\n  onConfigChange?: (config: MapProviderConfig) => void\n}\n\nexport function MapApiConfig({ onConfigChange }: MapApiConfigProps) {\n  const [config, setConfig] = useState<MapProviderConfig>(MapConfigManager.getConfig())\n  const [showApiKeys, setShowApiKeys] = useState({ amap: false, baidu: false })\n  const [testing, setTesting] = useState({ amap: false, baidu: false })\n  const [testResults, setTestResults] = useState({ amap: null as boolean | null, baidu: null as boolean | null })\n  const [errors, setErrors] = useState<string[]>([])\n\n  useEffect(() => {\n    const currentConfig = MapConfigManager.getConfig()\n    setConfig(currentConfig)\n  }, [])\n\n  const handleApiKeyChange = (provider: 'amap' | 'baidu', value: string) => {\n    const newConfig = {\n      ...config,\n      [provider]: {\n        ...config[provider],\n        apiKey: value\n      }\n    }\n    setConfig(newConfig)\n    setTestResults({ ...testResults, [provider]: null })\n    \n    // 更新全局配置\n    MapConfigManager.updateConfig(newConfig)\n    onConfigChange?.(newConfig)\n  }\n\n  const handleEnabledChange = (provider: 'amap' | 'baidu', enabled: boolean) => {\n    const newConfig = {\n      ...config,\n      [provider]: {\n        ...config[provider],\n        enabled\n      }\n    }\n    setConfig(newConfig)\n    \n    // 更新全局配置\n    MapConfigManager.updateConfig(newConfig)\n    onConfigChange?.(newConfig)\n  }\n\n  const testApiKey = async (provider: 'amap' | 'baidu') => {\n    const apiKey = config[provider].apiKey\n    \n    if (!apiKey || apiKey === `your_${provider}_api_key`) {\n      setErrors([`请先输入${provider === 'amap' ? '高德' : '百度'}地图API密钥`])\n      return\n    }\n\n    setTesting({ ...testing, [provider]: true })\n    setErrors([])\n\n    try {\n      const isValid = await MapConfigManager.testApiKey(provider, apiKey)\n      setTestResults({ ...testResults, [provider]: isValid })\n      \n      if (isValid) {\n        // 如果测试成功，自动启用该服务\n        handleEnabledChange(provider, true)\n      }\n    } catch (error) {\n      console.error(`测试${provider}API密钥失败:`, error)\n      setTestResults({ ...testResults, [provider]: false })\n      setErrors([`测试${provider === 'amap' ? '高德' : '百度'}地图API密钥失败，请检查密钥是否正确`])\n    } finally {\n      setTesting({ ...testing, [provider]: false })\n    }\n  }\n\n  const getStatusBadge = (provider: 'amap' | 'baidu') => {\n    const providerConfig = config[provider]\n    const testResult = testResults[provider]\n    const isConfigured = providerConfig.apiKey && providerConfig.apiKey !== `your_${provider}_api_key`\n    \n    if (!isConfigured) {\n      return <Badge variant=\"secondary\">未配置</Badge>\n    }\n    \n    if (!providerConfig.enabled) {\n      return <Badge variant=\"outline\">已禁用</Badge>\n    }\n    \n    if (testResult === true) {\n      return <Badge variant=\"default\" className=\"bg-green-500\">可用</Badge>\n    }\n    \n    if (testResult === false) {\n      return <Badge variant=\"destructive\">无效</Badge>\n    }\n    \n    return <Badge variant=\"outline\">未测试</Badge>\n  }\n\n  const getStatusIcon = (provider: 'amap' | 'baidu') => {\n    const testResult = testResults[provider]\n    const isConfigured = config[provider].apiKey && config[provider].apiKey !== `your_${provider}_api_key`\n    \n    if (!isConfigured) {\n      return <AlertCircle className=\"h-4 w-4 text-yellow-500\" />\n    }\n    \n    if (testResult === true) {\n      return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n    }\n    \n    if (testResult === false) {\n      return <XCircle className=\"h-4 w-4 text-red-500\" />\n    }\n    \n    return <AlertCircle className=\"h-4 w-4 text-gray-500\" />\n  }\n\n  const maskApiKey = (apiKey: string) => {\n    if (!apiKey || apiKey.length < 8) return apiKey\n    return apiKey.substring(0, 4) + '*'.repeat(apiKey.length - 8) + apiKey.substring(apiKey.length - 4)\n  }\n\n  const configStatus = MapConfigManager.getConfigStatus()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 配置状态概览 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <MapPin className=\"h-5 w-5 mr-2\" />\n            地图服务配置状态\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"text-2xl font-bold text-primary\">\n                {MapConfigManager.getAvailableProviders().length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">可用服务</div>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"text-2xl font-bold text-primary\">\n                {configStatus.hasValidProvider ? '✓' : '✗'}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">服务状态</div>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"text-sm font-medium\">推荐服务</div>\n              <div className=\"text-sm text-muted-foreground\">\n                {configStatus.recommendedProvider === 'amap' ? '高德地图' : \n                 configStatus.recommendedProvider === 'baidu' ? '百度地图' : '模拟服务'}\n              </div>\n            </div>\n          </div>\n          \n          {!configStatus.hasValidProvider && (\n            <div className=\"mt-4 p-3 bg-yellow-50 dark:bg-yellow-950/30 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n              <div className=\"flex items-start space-x-2\">\n                <AlertCircle className=\"h-4 w-4 text-yellow-500 mt-0.5\" />\n                <div>\n                  <p className=\"text-sm font-medium text-yellow-700 dark:text-yellow-300\">\n                    建议配置地图API\n                  </p>\n                  <p className=\"text-sm text-yellow-600 dark:text-yellow-400\">\n                    配置地图API密钥可以获得更准确的地址解析服务，否则将使用模拟数据。\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* 高德地图配置 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-blue-500 rounded mr-3 flex items-center justify-center\">\n                <span className=\"text-white text-xs font-bold\">高</span>\n              </div>\n              高德地图 API\n            </div>\n            {getStatusBadge('amap')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center space-x-2\">\n            {getStatusIcon('amap')}\n            <span className=\"text-sm\">\n              {config.amap.enabled ? '已启用' : '已禁用'} | \n              {config.amap.apiKey && config.amap.apiKey !== 'your_amap_api_key' ? ' 已配置' : ' 未配置'}\n            </span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">API密钥</label>\n            <div className=\"flex space-x-2\">\n              <div className=\"relative flex-1\">\n                <Input\n                  type={showApiKeys.amap ? 'text' : 'password'}\n                  value={config.amap.apiKey}\n                  onChange={(e) => handleApiKeyChange('amap', e.target.value)}\n                  placeholder=\"请输入高德地图API密钥\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"absolute right-1 top-1 h-8 w-8 p-0\"\n                  onClick={() => setShowApiKeys({ ...showApiKeys, amap: !showApiKeys.amap })}\n                >\n                  {showApiKeys.amap ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                </Button>\n              </div>\n              <Button\n                onClick={() => testApiKey('amap')}\n                disabled={testing.amap || !config.amap.apiKey}\n                variant=\"outline\"\n              >\n                {testing.amap ? (\n                  <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                ) : null}\n                测试\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <label className=\"text-sm font-medium\">启用高德地图服务</label>\n            <Button\n              variant={config.amap.enabled ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => handleEnabledChange('amap', !config.amap.enabled)}\n            >\n              {config.amap.enabled ? '已启用' : '已禁用'}\n            </Button>\n          </div>\n\n          <div className=\"flex space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open(MapConfigManager.getApiApplicationUrl('amap'), '_blank')}\n            >\n              <ExternalLink className=\"h-4 w-4 mr-2\" />\n              申请API密钥\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open(MapConfigManager.getApiDocumentationUrl('amap'), '_blank')}\n            >\n              <ExternalLink className=\"h-4 w-4 mr-2\" />\n              查看文档\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 百度地图配置 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <div className=\"h-8 w-8 bg-red-500 rounded mr-3 flex items-center justify-center\">\n                <span className=\"text-white text-xs font-bold\">百</span>\n              </div>\n              百度地图 API\n            </div>\n            {getStatusBadge('baidu')}\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center space-x-2\">\n            {getStatusIcon('baidu')}\n            <span className=\"text-sm\">\n              {config.baidu.enabled ? '已启用' : '已禁用'} | \n              {config.baidu.apiKey && config.baidu.apiKey !== 'your_baidu_api_key' ? ' 已配置' : ' 未配置'}\n            </span>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">API密钥</label>\n            <div className=\"flex space-x-2\">\n              <div className=\"relative flex-1\">\n                <Input\n                  type={showApiKeys.baidu ? 'text' : 'password'}\n                  value={config.baidu.apiKey}\n                  onChange={(e) => handleApiKeyChange('baidu', e.target.value)}\n                  placeholder=\"请输入百度地图API密钥\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"absolute right-1 top-1 h-8 w-8 p-0\"\n                  onClick={() => setShowApiKeys({ ...showApiKeys, baidu: !showApiKeys.baidu })}\n                >\n                  {showApiKeys.baidu ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                </Button>\n              </div>\n              <Button\n                onClick={() => testApiKey('baidu')}\n                disabled={testing.baidu || !config.baidu.apiKey}\n                variant=\"outline\"\n              >\n                {testing.baidu ? (\n                  <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                ) : null}\n                测试\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <label className=\"text-sm font-medium\">启用百度地图服务</label>\n            <Button\n              variant={config.baidu.enabled ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => handleEnabledChange('baidu', !config.baidu.enabled)}\n            >\n              {config.baidu.enabled ? '已启用' : '已禁用'}\n            </Button>\n          </div>\n\n          <div className=\"flex space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open(MapConfigManager.getApiApplicationUrl('baidu'), '_blank')}\n            >\n              <ExternalLink className=\"h-4 w-4 mr-2\" />\n              申请API密钥\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open(MapConfigManager.getApiDocumentationUrl('baidu'), '_blank')}\n            >\n              <ExternalLink className=\"h-4 w-4 mr-2\" />\n              查看文档\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 错误信息显示 */}\n      {errors.length > 0 && (\n        <div className=\"p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n          <div className=\"flex items-start space-x-2\">\n            <XCircle className=\"h-4 w-4 text-red-500 mt-0.5\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n                配置错误\n              </h4>\n              <ul className=\"text-sm text-red-600 dark:text-red-400 mt-1\">\n                {errors.map((error, index) => (\n                  <li key={index}>• {error}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;;;AAjBA;;;;;;;;AAuBO,SAAS,aAAa,EAAE,cAAc,EAAqB;;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,8HAAA,CAAA,mBAAgB,CAAC,SAAS;IAClF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAO,OAAO;IAAM;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAO,OAAO;IAAM;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAwB,OAAO;IAAuB;IAC7G,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,gBAAgB,8HAAA,CAAA,mBAAgB,CAAC,SAAS;YAChD,UAAU;QACZ;iCAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC,UAA4B;QACtD,MAAM,YAAY;YAChB,GAAG,MAAM;YACT,CAAC,SAAS,EAAE;gBACV,GAAG,MAAM,CAAC,SAAS;gBACnB,QAAQ;YACV;QACF;QACA,UAAU;QACV,eAAe;YAAE,GAAG,WAAW;YAAE,CAAC,SAAS,EAAE;QAAK;QAElD,SAAS;QACT,8HAAA,CAAA,mBAAgB,CAAC,YAAY,CAAC;QAC9B,iBAAiB;IACnB;IAEA,MAAM,sBAAsB,CAAC,UAA4B;QACvD,MAAM,YAAY;YAChB,GAAG,MAAM;YACT,CAAC,SAAS,EAAE;gBACV,GAAG,MAAM,CAAC,SAAS;gBACnB;YACF;QACF;QACA,UAAU;QAEV,SAAS;QACT,8HAAA,CAAA,mBAAgB,CAAC,YAAY,CAAC;QAC9B,iBAAiB;IACnB;IAEA,MAAM,aAAa,OAAO;QACxB,MAAM,SAAS,MAAM,CAAC,SAAS,CAAC,MAAM;QAEtC,IAAI,CAAC,UAAU,WAAW,CAAC,KAAK,EAAE,SAAS,QAAQ,CAAC,EAAE;YACpD,UAAU;gBAAC,CAAC,IAAI,EAAE,aAAa,SAAS,OAAO,KAAK,OAAO,CAAC;aAAC;YAC7D;QACF;QAEA,WAAW;YAAE,GAAG,OAAO;YAAE,CAAC,SAAS,EAAE;QAAK;QAC1C,UAAU,EAAE;QAEZ,IAAI;YACF,MAAM,UAAU,MAAM,8HAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,UAAU;YAC5D,eAAe;gBAAE,GAAG,WAAW;gBAAE,CAAC,SAAS,EAAE;YAAQ;YAErD,IAAI,SAAS;gBACX,iBAAiB;gBACjB,oBAAoB,UAAU;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,EAAE,EAAE,SAAS,QAAQ,CAAC,EAAE;YACvC,eAAe;gBAAE,GAAG,WAAW;gBAAE,CAAC,SAAS,EAAE;YAAM;YACnD,UAAU;gBAAC,CAAC,EAAE,EAAE,aAAa,SAAS,OAAO,KAAK,mBAAmB,CAAC;aAAC;QACzE,SAAU;YACR,WAAW;gBAAE,GAAG,OAAO;gBAAE,CAAC,SAAS,EAAE;YAAM;QAC7C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,iBAAiB,MAAM,CAAC,SAAS;QACvC,MAAM,aAAa,WAAW,CAAC,SAAS;QACxC,MAAM,eAAe,eAAe,MAAM,IAAI,eAAe,MAAM,KAAK,CAAC,KAAK,EAAE,SAAS,QAAQ,CAAC;QAElG,IAAI,CAAC,cAAc;YACjB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAY;;;;;;QACpC;QAEA,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAU;;;;;;QAClC;QAEA,IAAI,eAAe,MAAM;YACvB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAAe;;;;;;QAC3D;QAEA,IAAI,eAAe,OAAO;YACxB,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAc;;;;;;QACtC;QAEA,qBAAO,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAQ;sBAAU;;;;;;IAClC;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,WAAW,CAAC,SAAS;QACxC,MAAM,eAAe,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,KAAK,EAAE,SAAS,QAAQ,CAAC;QAEtG,IAAI,CAAC,cAAc;YACjB,qBAAO,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,eAAe,MAAM;YACvB,qBAAO,6LAAC,8NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,IAAI,eAAe,OAAO;YACxB,qBAAO,6LAAC,+MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QAEA,qBAAO,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG,OAAO;QACzC,OAAO,OAAO,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,KAAK,OAAO,SAAS,CAAC,OAAO,MAAM,GAAG;IACnG;IAEA,MAAM,eAAe,8HAAA,CAAA,mBAAgB,CAAC,eAAe;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIvC,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,8HAAA,CAAA,mBAAgB,CAAC,qBAAqB,GAAG,MAAM;;;;;;0DAElD,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAEjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,aAAa,gBAAgB,GAAG,MAAM;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAEjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsB;;;;;;0DACrC,6LAAC;gDAAI,WAAU;0DACZ,aAAa,mBAAmB,KAAK,SAAS,SAC9C,aAAa,mBAAmB,KAAK,UAAU,SAAS;;;;;;;;;;;;;;;;;;4BAK9D,CAAC,aAAa,gBAAgB,kBAC7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA2D;;;;;;8DAGxE,6LAAC;oDAAE,WAAU;8DAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxE,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;wCAC3C;;;;;;;gCAGP,eAAe;;;;;;;;;;;;kCAGpB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;oCACZ,cAAc;kDACf,6LAAC;wCAAK,WAAU;;4CACb,OAAO,IAAI,CAAC,OAAO,GAAG,QAAQ;4CAAM;4CACpC,OAAO,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,sBAAsB,SAAS;;;;;;;;;;;;;0CAIjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAM,YAAY,IAAI,GAAG,SAAS;wDAClC,OAAO,OAAO,IAAI,CAAC,MAAM;wDACzB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAC1D,aAAY;;;;;;kEAEd,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,eAAe;gEAAE,GAAG,WAAW;gEAAE,MAAM,CAAC,YAAY,IAAI;4DAAC;kEAEvE,YAAY,IAAI,iBAAG,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAAe,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGxE,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,WAAW;gDAC1B,UAAU,QAAQ,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM;gDAC7C,SAAQ;;oDAEP,QAAQ,IAAI,iBACX,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;+DACjB;oDAAK;;;;;;;;;;;;;;;;;;;0CAMf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,OAAO,IAAI,CAAC,OAAO,GAAG,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,oBAAoB,QAAQ,CAAC,OAAO,IAAI,CAAC,OAAO;kDAE9D,OAAO,IAAI,CAAC,OAAO,GAAG,QAAQ;;;;;;;;;;;;0CAInC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,8HAAA,CAAA,mBAAgB,CAAC,oBAAoB,CAAC,SAAS;;0DAE1E,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,8HAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC,SAAS;;0DAE5E,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;wCAC3C;;;;;;;gCAGP,eAAe;;;;;;;;;;;;kCAGpB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;oCACZ,cAAc;kDACf,6LAAC;wCAAK,WAAU;;4CACb,OAAO,KAAK,CAAC,OAAO,GAAG,QAAQ;4CAAM;4CACrC,OAAO,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,uBAAuB,SAAS;;;;;;;;;;;;;0CAIpF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAM,YAAY,KAAK,GAAG,SAAS;wDACnC,OAAO,OAAO,KAAK,CAAC,MAAM;wDAC1B,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC3D,aAAY;;;;;;kEAEd,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,eAAe;gEAAE,GAAG,WAAW;gEAAE,OAAO,CAAC,YAAY,KAAK;4DAAC;kEAEzE,YAAY,KAAK,iBAAG,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAAe,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGzE,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,WAAW;gDAC1B,UAAU,QAAQ,KAAK,IAAI,CAAC,OAAO,KAAK,CAAC,MAAM;gDAC/C,SAAQ;;oDAEP,QAAQ,KAAK,iBACZ,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;+DACjB;oDAAK;;;;;;;;;;;;;;;;;;;0CAMf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,OAAO,KAAK,CAAC,OAAO,GAAG,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,oBAAoB,SAAS,CAAC,OAAO,KAAK,CAAC,OAAO;kDAEhE,OAAO,KAAK,CAAC,OAAO,GAAG,QAAQ;;;;;;;;;;;;0CAIpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,8HAAA,CAAA,mBAAgB,CAAC,oBAAoB,CAAC,UAAU;;0DAE3E,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,8HAAA,CAAA,mBAAgB,CAAC,sBAAsB,CAAC,UAAU;;0DAE7E,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YAQhD,OAAO,MAAM,GAAG,mBACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,6LAAC;oCAAG,WAAU;8CACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;;gDAAe;gDAAG;;2CAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B;GA9WgB;KAAA", "debugId": null}}, {"offset": {"line": 3186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3401, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/settings.ts"], "sourcesContent": ["// Settings management for local storage\nexport interface BusinessSettings {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  timezone: string\n  currency: string\n}\n\nexport interface OperatingHours {\n  [key: string]: {\n    open: string\n    close: string\n    closed: boolean\n  }\n}\n\nexport interface NotificationSettings {\n  emailNotifications: boolean\n  smsNotifications: boolean\n  appointmentReminders: boolean\n  lowStockAlerts: boolean\n  dailyReports: boolean\n  weeklyReports: boolean\n}\n\nexport interface AppSettings {\n  business: BusinessSettings\n  operatingHours: OperatingHours\n  notifications: NotificationSettings\n  theme: string\n  language: string\n}\n\nconst DEFAULT_SETTINGS: AppSettings = {\n  business: {\n    name: '皇家理发店',\n    address: '北京市朝阳区三里屯街道1号',\n    phone: '+86 138-0013-8000',\n    email: '<EMAIL>',\n    website: 'www.royalcuts.cn',\n    timezone: 'Asia/Shanghai',\n    currency: 'CNY'\n  },\n  operatingHours: {\n    monday: { open: '09:00', close: '18:00', closed: false },\n    tuesday: { open: '09:00', close: '18:00', closed: false },\n    wednesday: { open: '09:00', close: '18:00', closed: false },\n    thursday: { open: '09:00', close: '19:00', closed: false },\n    friday: { open: '09:00', close: '19:00', closed: false },\n    saturday: { open: '08:00', close: '17:00', closed: false },\n    sunday: { open: '10:00', close: '16:00', closed: false }\n  },\n  notifications: {\n    emailNotifications: true,\n    smsNotifications: false,\n    appointmentReminders: true,\n    lowStockAlerts: true,\n    dailyReports: false,\n    weeklyReports: true\n  },\n  theme: 'royal-gold',\n  language: 'zh-CN'\n}\n\nconst SETTINGS_KEY = 'barbershop_settings'\n\nexport class SettingsManager {\n  static getSettings(): AppSettings {\n    if (typeof window === 'undefined') {\n      return DEFAULT_SETTINGS\n    }\n\n    try {\n      const stored = localStorage.getItem(SETTINGS_KEY)\n      if (stored) {\n        const parsed = JSON.parse(stored)\n        // Merge with defaults to ensure all properties exist\n        return {\n          ...DEFAULT_SETTINGS,\n          ...parsed,\n          business: { ...DEFAULT_SETTINGS.business, ...parsed.business },\n          operatingHours: { ...DEFAULT_SETTINGS.operatingHours, ...parsed.operatingHours },\n          notifications: { ...DEFAULT_SETTINGS.notifications, ...parsed.notifications }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading settings:', error)\n    }\n\n    return DEFAULT_SETTINGS\n  }\n\n  static saveSettings(settings: AppSettings): void {\n    if (typeof window === 'undefined') {\n      return\n    }\n\n    try {\n      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings))\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      throw new Error('无法保存设置')\n    }\n  }\n\n  static updateBusinessSettings(business: BusinessSettings): void {\n    const settings = this.getSettings()\n    settings.business = business\n    this.saveSettings(settings)\n  }\n\n  static updateOperatingHours(operatingHours: OperatingHours): void {\n    const settings = this.getSettings()\n    settings.operatingHours = operatingHours\n    this.saveSettings(settings)\n  }\n\n  static updateNotificationSettings(notifications: NotificationSettings): void {\n    const settings = this.getSettings()\n    settings.notifications = notifications\n    this.saveSettings(settings)\n  }\n\n  static updateTheme(theme: string): void {\n    const settings = this.getSettings()\n    settings.theme = theme\n    this.saveSettings(settings)\n  }\n\n  static resetToDefaults(): void {\n    this.saveSettings(DEFAULT_SETTINGS)\n  }\n\n  static exportSettings(): string {\n    const settings = this.getSettings()\n    return JSON.stringify(settings, null, 2)\n  }\n\n  static importSettings(settingsJson: string): void {\n    try {\n      const imported = JSON.parse(settingsJson)\n      // Validate the structure\n      if (imported.business && imported.operatingHours && imported.notifications) {\n        this.saveSettings(imported)\n      } else {\n        throw new Error('Invalid settings format')\n      }\n    } catch (error) {\n      console.error('Error importing settings:', error)\n      throw new Error('无效的设置格式')\n    }\n  }\n}\n\n// Utility functions for formatting\nexport function formatOperatingHours(hours: OperatingHours): string {\n  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']\n  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\n  \n  return days.map((day, index) => {\n    const dayHours = hours[day]\n    if (dayHours.closed) {\n      return `${dayNames[index]}: 休息`\n    }\n    return `${dayNames[index]}: ${dayHours.open} - ${dayHours.close}`\n  }).join('\\n')\n}\n\nexport function validateBusinessSettings(business: BusinessSettings): string[] {\n  const errors: string[] = []\n  \n  if (!business.name.trim()) {\n    errors.push('商户名称不能为空')\n  }\n  \n  if (!business.phone.trim()) {\n    errors.push('联系电话不能为空')\n  }\n  \n  if (!business.email.trim()) {\n    errors.push('邮箱地址不能为空')\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(business.email)) {\n    errors.push('邮箱地址格式不正确')\n  }\n  \n  if (!business.address.trim()) {\n    errors.push('商户地址不能为空')\n  }\n  \n  return errors\n}\n\nexport function validateOperatingHours(hours: OperatingHours): string[] {\n  const errors: string[] = []\n  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']\n  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']\n  \n  days.forEach((day, index) => {\n    const dayHours = hours[day]\n    if (!dayHours.closed) {\n      if (!dayHours.open || !dayHours.close) {\n        errors.push(`${dayNames[index]}的营业时间不完整`)\n      } else if (dayHours.open >= dayHours.close) {\n        errors.push(`${dayNames[index]}的开始时间必须早于结束时间`)\n      }\n    }\n  })\n  \n  return errors\n}\n\n// Theme utilities\nexport function applyTheme(theme: string): void {\n  if (typeof document === 'undefined') return\n  \n  const root = document.documentElement\n  \n  switch (theme) {\n    case 'royal-gold':\n      root.style.setProperty('--primary', '180 83% 40%')\n      root.style.setProperty('--primary-foreground', '0 0% 98%')\n      break\n    case 'ocean-blue':\n      root.style.setProperty('--primary', '217 91% 60%')\n      root.style.setProperty('--primary-foreground', '0 0% 98%')\n      break\n    case 'forest-green':\n      root.style.setProperty('--primary', '142 76% 36%')\n      root.style.setProperty('--primary-foreground', '0 0% 98%')\n      break\n    default:\n      // Keep default theme\n      break\n  }\n}\n\n// Initialize theme on load\nexport function initializeTheme(): void {\n  const settings = SettingsManager.getSettings()\n  applyTheme(settings.theme)\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;AAoCxC,MAAM,mBAAgC;IACpC,UAAU;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA,gBAAgB;QACd,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,SAAS;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACxD,WAAW;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QAC1D,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;IACzD;IACA,eAAe;QACb,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,eAAe;IACjB;IACA,OAAO;IACP,UAAU;AACZ;AAEA,MAAM,eAAe;AAEd,MAAM;IACX,OAAO,cAA2B;QAChC,uCAAmC;;QAEnC;QAEA,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,qDAAqD;gBACrD,OAAO;oBACL,GAAG,gBAAgB;oBACnB,GAAG,MAAM;oBACT,UAAU;wBAAE,GAAG,iBAAiB,QAAQ;wBAAE,GAAG,OAAO,QAAQ;oBAAC;oBAC7D,gBAAgB;wBAAE,GAAG,iBAAiB,cAAc;wBAAE,GAAG,OAAO,cAAc;oBAAC;oBAC/E,eAAe;wBAAE,GAAG,iBAAiB,aAAa;wBAAE,GAAG,OAAO,aAAa;oBAAC;gBAC9E;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;QAEA,OAAO;IACT;IAEA,OAAO,aAAa,QAAqB,EAAQ;QAC/C,uCAAmC;;QAEnC;QAEA,IAAI;YACF,aAAa,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC;QACpD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,uBAAuB,QAA0B,EAAQ;QAC9D,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,QAAQ,GAAG;QACpB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,qBAAqB,cAA8B,EAAQ;QAChE,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,cAAc,GAAG;QAC1B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,2BAA2B,aAAmC,EAAQ;QAC3E,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,aAAa,GAAG;QACzB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,YAAY,KAAa,EAAQ;QACtC,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,SAAS,KAAK,GAAG;QACjB,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,kBAAwB;QAC7B,IAAI,CAAC,YAAY,CAAC;IACpB;IAEA,OAAO,iBAAyB;QAC9B,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO,KAAK,SAAS,CAAC,UAAU,MAAM;IACxC;IAEA,OAAO,eAAe,YAAoB,EAAQ;QAChD,IAAI;YACF,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,yBAAyB;YACzB,IAAI,SAAS,QAAQ,IAAI,SAAS,cAAc,IAAI,SAAS,aAAa,EAAE;gBAC1E,IAAI,CAAC,YAAY,CAAC;YACpB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,SAAS,qBAAqB,KAAqB;IACxD,MAAM,OAAO;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAC3F,MAAM,WAAW;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE3D,OAAO,KAAK,GAAG,CAAC,CAAC,KAAK;QACpB,MAAM,WAAW,KAAK,CAAC,IAAI;QAC3B,IAAI,SAAS,MAAM,EAAE;YACnB,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;QACjC;QACA,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,SAAS,KAAK,EAAE;IACnE,GAAG,IAAI,CAAC;AACV;AAEO,SAAS,yBAAyB,QAA0B;IACjE,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;QAC1B,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;QAC7D,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAEO,SAAS,uBAAuB,KAAqB;IAC1D,MAAM,SAAmB,EAAE;IAC3B,MAAM,OAAO;QAAC;QAAU;QAAW;QAAa;QAAY;QAAU;QAAY;KAAS;IAC3F,MAAM,WAAW;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE3D,KAAK,OAAO,CAAC,CAAC,KAAK;QACjB,MAAM,WAAW,KAAK,CAAC,IAAI;QAC3B,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE;gBACrC,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC1C,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;gBAC1C,OAAO,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;YAC/C;QACF;IACF;IAEA,OAAO;AACT;AAGO,SAAS,WAAW,KAAa;IACtC,IAAI,OAAO,aAAa,aAAa;IAErC,MAAM,OAAO,SAAS,eAAe;IAErC,OAAQ;QACN,KAAK;YACH,KAAK,KAAK,CAAC,WAAW,CAAC,aAAa;YACpC,KAAK,KAAK,CAAC,WAAW,CAAC,wBAAwB;YAC/C;QACF,KAAK;YACH,KAAK,KAAK,CAAC,WAAW,CAAC,aAAa;YACpC,KAAK,KAAK,CAAC,WAAW,CAAC,wBAAwB;YAC/C;QACF,KAAK;YACH,KAAK,KAAK,CAAC,WAAW,CAAC,aAAa;YACpC,KAAK,KAAK,CAAC,WAAW,CAAC,wBAAwB;YAC/C;QACF;YAEE;IACJ;AACF;AAGO,SAAS;IACd,MAAM,WAAW,gBAAgB,WAAW;IAC5C,WAAW,SAAS,KAAK;AAC3B", "debugId": null}}, {"offset": {"line": 3664, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { PageHeader } from '@/components/layout/page-header'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { AddressInput } from '@/components/ui/address-input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { MapApiConfig } from '@/components/settings/map-api-config'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Settings, \n  Building, \n  Clock, \n  DollarSign, \n  Bell, \n  Shield, \n  Palette,\n  Globe,\n  Mail,\n  Phone,\n  MapPin,\n  Save,\n  User,\n  Key,\n  Database,\n  Smartphone,\n  Download,\n  Upload,\n  RotateCcw\n} from 'lucide-react'\nimport { useAuth } from '@/contexts/auth-context'\nimport {\n  SettingsManager,\n  validateBusinessSettings,\n  validateOperatingHours,\n  applyTheme,\n  type BusinessSettings,\n  type OperatingHours,\n  type NotificationSettings\n} from '@/lib/settings'\n\nexport default function SettingsPage() {\n  const { user } = useAuth()\n  const [loading, setLoading] = useState(false)\n  const [activeTab, setActiveTab] = useState('business')\n  const [errors, setErrors] = useState<string[]>([])\n\n  // Settings state\n  const [businessSettings, setBusinessSettings] = useState<BusinessSettings>({\n    name: '皇家理发店',\n    address: '北京市朝阳区三里屯街道1号',\n    phone: '+86 138-0013-8000',\n    email: '<EMAIL>',\n    website: 'www.royalcuts.cn',\n    timezone: 'Asia/Shanghai',\n    currency: 'CNY'\n  })\n\n  const [operatingHours, setOperatingHours] = useState<OperatingHours>({\n    monday: { open: '09:00', close: '18:00', closed: false },\n    tuesday: { open: '09:00', close: '18:00', closed: false },\n    wednesday: { open: '09:00', close: '18:00', closed: false },\n    thursday: { open: '09:00', close: '19:00', closed: false },\n    friday: { open: '09:00', close: '19:00', closed: false },\n    saturday: { open: '08:00', close: '17:00', closed: false },\n    sunday: { open: '10:00', close: '16:00', closed: false }\n  })\n\n  const [notifications, setNotifications] = useState<NotificationSettings>({\n    emailNotifications: true,\n    smsNotifications: false,\n    appointmentReminders: true,\n    lowStockAlerts: true,\n    dailyReports: false,\n    weeklyReports: true\n  })\n\n  const [selectedTheme, setSelectedTheme] = useState('royal-gold')\n\n  // Load settings on component mount\n  useEffect(() => {\n    const settings = SettingsManager.getSettings()\n    setBusinessSettings(settings.business)\n    setOperatingHours(settings.operatingHours)\n    setNotifications(settings.notifications)\n    setSelectedTheme(settings.theme)\n  }, [])\n\n  const handleSaveSettings = async () => {\n    setLoading(true)\n    setErrors([])\n\n    try {\n      // Validate settings\n      const businessErrors = validateBusinessSettings(businessSettings)\n      const hoursErrors = validateOperatingHours(operatingHours)\n      const allErrors = [...businessErrors, ...hoursErrors]\n\n      if (allErrors.length > 0) {\n        setErrors(allErrors)\n        setLoading(false)\n        return\n      }\n\n      // Save settings\n      const settings = {\n        business: businessSettings,\n        operatingHours,\n        notifications,\n        theme: selectedTheme,\n        language: 'zh-CN'\n      }\n\n      SettingsManager.saveSettings(settings)\n\n      // Apply theme immediately\n      applyTheme(selectedTheme)\n\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      alert('设置保存成功！')\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      alert('保存设置时出错，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleThemeChange = (theme: string) => {\n    setSelectedTheme(theme)\n    applyTheme(theme)\n  }\n\n  const handleResetSettings = () => {\n    if (confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {\n      SettingsManager.resetToDefaults()\n      const settings = SettingsManager.getSettings()\n      setBusinessSettings(settings.business)\n      setOperatingHours(settings.operatingHours)\n      setNotifications(settings.notifications)\n      setSelectedTheme(settings.theme)\n      applyTheme(settings.theme)\n      alert('设置已重置为默认值')\n    }\n  }\n\n  const handleExportSettings = () => {\n    try {\n      const settingsJson = SettingsManager.exportSettings()\n      const blob = new Blob([settingsJson], { type: 'application/json' })\n      const url = URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = 'barbershop-settings.json'\n      document.body.appendChild(a)\n      a.click()\n      document.body.removeChild(a)\n      URL.revokeObjectURL(url)\n    } catch (error) {\n      alert('导出设置失败')\n    }\n  }\n\n  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    const reader = new FileReader()\n    reader.onload = (e) => {\n      try {\n        const content = e.target?.result as string\n        SettingsManager.importSettings(content)\n        const settings = SettingsManager.getSettings()\n        setBusinessSettings(settings.business)\n        setOperatingHours(settings.operatingHours)\n        setNotifications(settings.notifications)\n        setSelectedTheme(settings.theme)\n        applyTheme(settings.theme)\n        alert('设置导入成功！')\n      } catch (error) {\n        alert('导入设置失败：文件格式不正确')\n      }\n    }\n    reader.readAsText(file)\n    // Reset input\n    event.target.value = ''\n  }\n\n  const tabs = [\n    { id: 'business', label: '商户信息', icon: Building },\n    { id: 'hours', label: '营业时间', icon: Clock },\n    { id: 'notifications', label: '通知设置', icon: Bell },\n    { id: 'appearance', label: '外观设置', icon: Palette },\n    { id: 'security', label: '安全设置', icon: Shield },\n    { id: 'integrations', label: '系统集成', icon: Database },\n    { id: 'maps', label: '地图服务', icon: MapPin }\n  ]\n\n  const renderBusinessSettings = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Building className=\"h-5 w-5 mr-2\" />\n            商户信息\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">商户名称</label>\n              <Input\n                value={businessSettings.name}\n                onChange={(e) => setBusinessSettings({...businessSettings, name: e.target.value})}\n                placeholder=\"皇家理发店\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">联系电话</label>\n              <div className=\"relative\">\n                <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.phone}\n                  onChange={(e) => setBusinessSettings({...businessSettings, phone: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"+86 138-0013-8000\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">邮箱地址</label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.email}\n                  onChange={(e) => setBusinessSettings({...businessSettings, email: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">网站地址</label>\n              <div className=\"relative\">\n                <Globe className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.website}\n                  onChange={(e) => setBusinessSettings({...businessSettings, website: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"www.royalcuts.cn\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">商户地址</label>\n            <AddressInput\n              value={businessSettings.address}\n              onChange={(value) => setBusinessSettings({...businessSettings, address: value})}\n              placeholder=\"北京市朝阳区三里屯街道1号\"\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">时区设置</label>\n              <Select value={businessSettings.timezone} onValueChange={(value) => setBusinessSettings({...businessSettings, timezone: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"Asia/Shanghai\">北京时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"Asia/Hong_Kong\">香港时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"Asia/Taipei\">台北时间 (GMT+8)</SelectItem>\n                  <SelectItem value=\"America/New_York\">美国东部时间 (ET)</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">货币设置</label>\n              <Select value={businessSettings.currency} onValueChange={(value) => setBusinessSettings({...businessSettings, currency: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"CNY\">CNY - 人民币</SelectItem>\n                  <SelectItem value=\"HKD\">HKD - 港币</SelectItem>\n                  <SelectItem value=\"TWD\">TWD - 新台币</SelectItem>\n                  <SelectItem value=\"USD\">USD - 美元</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderOperatingHours = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Clock className=\"h-5 w-5 mr-2\" />\n            营业时间\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {Object.entries(operatingHours).map(([day, hours]) => (\n              <div key={day} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"font-medium capitalize w-20\">\n                    {day === 'monday' ? '周一' :\n                     day === 'tuesday' ? '周二' :\n                     day === 'wednesday' ? '周三' :\n                     day === 'thursday' ? '周四' :\n                     day === 'friday' ? '周五' :\n                     day === 'saturday' ? '周六' :\n                     day === 'sunday' ? '周日' : day}\n                  </span>\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={!hours.closed}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, closed: !e.target.checked }\n                      })}\n                      className=\"rounded\"\n                    />\n                    <span className=\"text-sm\">营业</span>\n                  </label>\n                </div>\n                {!hours.closed && (\n                  <div className=\"flex items-center space-x-2\">\n                    <Input\n                      type=\"time\"\n                      value={hours.open}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, open: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                    <span className=\"text-muted-foreground\">至</span>\n                    <Input\n                      type=\"time\"\n                      value={hours.close}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, close: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                  </div>\n                )}\n                {hours.closed && (\n                  <Badge variant=\"secondary\">休息</Badge>\n                )}\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderNotifications = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Bell className=\"h-5 w-5 mr-2\" />\n            通知偏好设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[\n              { key: 'emailNotifications', label: '邮件通知', description: '通过邮件接收通知' },\n              { key: 'smsNotifications', label: '短信通知', description: '通过短信接收通知' },\n              { key: 'appointmentReminders', label: '预约提醒', description: '向客户发送预约提醒' },\n              { key: 'lowStockAlerts', label: '库存不足提醒', description: '库存不足时接收通知' },\n              { key: 'dailyReports', label: '日报', description: '接收每日业务摘要' },\n              { key: 'weeklyReports', label: '周报', description: '接收每周分析报告' }\n            ].map((setting) => (\n              <div key={setting.key} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div>\n                  <p className=\"font-medium\">{setting.label}</p>\n                  <p className=\"text-sm text-muted-foreground\">{setting.description}</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notifications[setting.key as keyof typeof notifications]}\n                    onChange={(e) => setNotifications({\n                      ...notifications,\n                      [setting.key]: e.target.checked\n                    })}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderAppearance = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Palette className=\"h-5 w-5 mr-2\" />\n            外观设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"p-4 border rounded-lg bg-muted/30\">\n              <p className=\"font-medium mb-2\">主题设置</p>\n              <p className=\"text-sm text-muted-foreground mb-4\">选择您喜欢的配色方案</p>\n              <div className=\"grid grid-cols-3 gap-3\">\n                <div\n                  className={`p-3 border rounded-lg cursor-pointer hover:bg-accent transition-all ${\n                    selectedTheme === 'royal-gold' ? 'ring-2 ring-primary' : ''\n                  }`}\n                  onClick={() => handleThemeChange('royal-gold')}\n                >\n                  <div className=\"h-8 bg-gradient-to-r from-amber-600 to-amber-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">\n                    皇家金色 {selectedTheme === 'royal-gold' ? '(当前)' : ''}\n                  </p>\n                </div>\n                <div\n                  className={`p-3 border rounded-lg cursor-pointer hover:bg-accent transition-all ${\n                    selectedTheme === 'ocean-blue' ? 'ring-2 ring-primary' : ''\n                  }`}\n                  onClick={() => handleThemeChange('ocean-blue')}\n                >\n                  <div className=\"h-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">\n                    海洋蓝 {selectedTheme === 'ocean-blue' ? '(当前)' : ''}\n                  </p>\n                </div>\n                <div\n                  className={`p-3 border rounded-lg cursor-pointer hover:bg-accent transition-all ${\n                    selectedTheme === 'forest-green' ? 'ring-2 ring-primary' : ''\n                  }`}\n                  onClick={() => handleThemeChange('forest-green')}\n                >\n                  <div className=\"h-8 bg-gradient-to-r from-emerald-600 to-emerald-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">\n                    森林绿 {selectedTheme === 'forest-green' ? '(当前)' : ''}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderSecurity = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Shield className=\"h-5 w-5 mr-2\" />\n            安全设置\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">双重身份验证</p>\n                <p className=\"text-sm text-muted-foreground\">为您的账户添加额外的安全保护</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Smartphone className=\"h-4 w-4 mr-2\" />\n                启用双重验证\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">修改密码</p>\n                <p className=\"text-sm text-muted-foreground\">更新您的账户密码</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Key className=\"h-4 w-4 mr-2\" />\n                修改密码\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">活跃会话</p>\n                <p className=\"text-sm text-muted-foreground\">管理您的活跃登录会话</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                查看会话\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderIntegrations = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Database className=\"h-5 w-5 mr-2\" />\n            系统集成\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center\">\n                  <Database className=\"h-5 w-5 text-green-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">Supabase 数据库</p>\n                  <p className=\"text-sm text-muted-foreground\">已连接并同步</p>\n                </div>\n              </div>\n              <Badge variant=\"success\">已连接</Badge>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                  <Mail className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">邮件服务</p>\n                  <p className=\"text-sm text-muted-foreground\">发送自动化邮件</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">连接</Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n                  <Smartphone className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">短信服务</p>\n                  <p className=\"text-sm text-muted-foreground\">发送短信通知</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">连接</Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderMaps = () => (\n    <Card className=\"shadow-elegant\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          <MapPin className=\"h-5 w-5 mr-2\" />\n          地图服务配置\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        <MapApiConfig />\n      </CardContent>\n    </Card>\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'business': return renderBusinessSettings()\n      case 'hours': return renderOperatingHours()\n      case 'notifications': return renderNotifications()\n      case 'appearance': return renderAppearance()\n      case 'security': return renderSecurity()\n      case 'integrations': return renderIntegrations()\n      case 'maps': return renderMaps()\n      default: return renderBusinessSettings()\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n        <PageHeader\n          title=\"系统设置\"\n          description=\"配置理发店管理系统\"\n          icon={<Settings className=\"h-6 w-6 text-white\" />}\n        />\n\n        {/* Error Display */}\n        {errors.length > 0 && (\n          <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg\">\n            <div className=\"flex items-start space-x-2\">\n              <div className=\"h-4 w-4 text-red-500 mt-0.5\">⚠️</div>\n              <div>\n                <h4 className=\"text-sm font-medium text-red-700 dark:text-red-300\">\n                  设置验证错误\n                </h4>\n                <ul className=\"text-sm text-red-600 dark:text-red-400 mt-1\">\n                  {errors.map((error, index) => (\n                    <li key={index}>• {error}</li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-col lg:flex-row gap-6\">\n          {/* Settings Navigation */}\n          <div className=\"lg:w-64\">\n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-4\">\n                <nav className=\"space-y-1\">\n                  {tabs.map((tab) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                        activeTab === tab.id\n                          ? 'bg-primary text-primary-foreground'\n                          : 'hover:bg-accent hover:text-accent-foreground'\n                      }`}\n                    >\n                      <tab.icon className=\"h-4 w-4\" />\n                      <span className=\"text-sm font-medium\">{tab.label}</span>\n                    </button>\n                  ))}\n                </nav>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Settings Content */}\n          <div className=\"flex-1\">\n            {renderTabContent()}\n            \n            {/* Action Buttons */}\n            <div className=\"mt-6 flex flex-col sm:flex-row gap-3 justify-between\">\n              <div className=\"flex gap-2\">\n                <Button variant=\"outline\" onClick={handleExportSettings}>\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  导出设置\n                </Button>\n                <div className=\"relative\">\n                  <input\n                    type=\"file\"\n                    accept=\".json\"\n                    onChange={handleImportSettings}\n                    className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n                  />\n                  <Button variant=\"outline\">\n                    <Upload className=\"h-4 w-4 mr-2\" />\n                    导入设置\n                  </Button>\n                </div>\n                <Button variant=\"outline\" onClick={handleResetSettings}>\n                  <RotateCcw className=\"h-4 w-4 mr-2\" />\n                  重置默认\n                </Button>\n              </div>\n\n              <Button onClick={handleSaveSettings} disabled={loading}>\n                {loading ? (\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                    <span>保存中...</span>\n                  </div>\n                ) : (\n                  <>\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    保存设置\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </MainLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;;;AAzCA;;;;;;;;;;;;;;;AAmDe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjD,iBAAiB;IACjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,SAAS;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACxD,WAAW;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QAC1D,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;IACzD;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QACvE,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,WAAW;YAC5C,oBAAoB,SAAS,QAAQ;YACrC,kBAAkB,SAAS,cAAc;YACzC,iBAAiB,SAAS,aAAa;YACvC,iBAAiB,SAAS,KAAK;QACjC;iCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,WAAW;QACX,UAAU,EAAE;QAEZ,IAAI;YACF,oBAAoB;YACpB,MAAM,iBAAiB,CAAA,GAAA,yHAAA,CAAA,2BAAwB,AAAD,EAAE;YAChD,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,yBAAsB,AAAD,EAAE;YAC3C,MAAM,YAAY;mBAAI;mBAAmB;aAAY;YAErD,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,UAAU;gBACV,WAAW;gBACX;YACF;YAEA,gBAAgB;YAChB,MAAM,WAAW;gBACf,UAAU;gBACV;gBACA;gBACA,OAAO;gBACP,UAAU;YACZ;YAEA,yHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;YAE7B,0BAA0B;YAC1B,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;YAEX,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE;IACb;IAEA,MAAM,sBAAsB;QAC1B,IAAI,QAAQ,4BAA4B;YACtC,yHAAA,CAAA,kBAAe,CAAC,eAAe;YAC/B,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,WAAW;YAC5C,oBAAoB,SAAS,QAAQ;YACrC,kBAAkB,SAAS,cAAc;YACzC,iBAAiB,SAAS,aAAa;YACvC,iBAAiB,SAAS,KAAK;YAC/B,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK;YACzB,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,eAAe,yHAAA,CAAA,kBAAe,CAAC,cAAc;YACnD,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAa,EAAE;gBAAE,MAAM;YAAmB;YACjE,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,UAAU,EAAE,MAAM,EAAE;gBAC1B,yHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;gBAC/B,MAAM,WAAW,yHAAA,CAAA,kBAAe,CAAC,WAAW;gBAC5C,oBAAoB,SAAS,QAAQ;gBACrC,kBAAkB,SAAS,cAAc;gBACzC,iBAAiB,SAAS,aAAa;gBACvC,iBAAiB,SAAS,KAAK;gBAC/B,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK;gBACzB,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QACA,OAAO,UAAU,CAAC;QAClB,cAAc;QACd,MAAM,MAAM,CAAC,KAAK,GAAG;IACvB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAQ,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAChD;YAAE,IAAI;YAAS,OAAO;YAAQ,MAAM,uMAAA,CAAA,QAAK;QAAC;QAC1C;YAAE,IAAI;YAAiB,OAAO;YAAQ,MAAM,qMAAA,CAAA,OAAI;QAAC;QACjD;YAAE,IAAI;YAAc,OAAO;YAAQ,MAAM,2MAAA,CAAA,UAAO;QAAC;QACjD;YAAE,IAAI;YAAY,OAAO;YAAQ,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC9C;YAAE,IAAI;YAAgB,OAAO;YAAQ,MAAM,6MAAA,CAAA,WAAQ;QAAC;QACpD;YAAE,IAAI;YAAQ,OAAO;YAAQ,MAAM,6MAAA,CAAA,SAAM;QAAC;KAC3C;IAED,MAAM,yBAAyB,kBAC7B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO,iBAAiB,IAAI;gDAC5B,UAAU,CAAC,IAAM,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC/E,aAAY;;;;;;;;;;;;kDAGhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAIlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,OAAO;wDAC/B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAClF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC,+IAAA,CAAA,eAAY;wCACX,OAAO,iBAAiB,OAAO;wCAC/B,UAAU,CAAC,QAAU,oBAAoB;gDAAC,GAAG,gBAAgB;gDAAE,SAAS;4CAAK;wCAC7E,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;0EAClC,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAiB;;;;;;0EACnC,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAc;;;;;;0EAChC,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;kDAI3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUxC,MAAM,uBAAuB,kBAC3B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAItC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC/C,6LAAC;oCAAc,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,QAAQ,WAAW,OACnB,QAAQ,YAAY,OACpB,QAAQ,cAAc,OACtB,QAAQ,aAAa,OACrB,QAAQ,WAAW,OACnB,QAAQ,aAAa,OACrB,QAAQ,WAAW,OAAO;;;;;;8DAE7B,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,CAAC,MAAM,MAAM;4DACtB,UAAU,CAAC,IAAM,kBAAkB;oEACjC,GAAG,cAAc;oEACjB,CAAC,IAAI,EAAE;wEAAE,GAAG,KAAK;wEAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,OAAO;oEAAC;gEAC/C;4DACA,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;wCAG7B,CAAC,MAAM,MAAM,kBACZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,MAAM,IAAI;oDACjB,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,CAAC,IAAI,EAAE;gEAAE,GAAG,KAAK;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1C;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,MAAM,KAAK;oDAClB,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,CAAC,IAAI,EAAE;gEAAE,GAAG,KAAK;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC3C;oDACA,WAAU;;;;;;;;;;;;wCAIf,MAAM,MAAM,kBACX,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;;mCAhDrB;;;;;;;;;;;;;;;;;;;;;;;;;;IA0DtB,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAsB,OAAO;oCAAQ,aAAa;gCAAW;gCACpE;oCAAE,KAAK;oCAAoB,OAAO;oCAAQ,aAAa;gCAAW;gCAClE;oCAAE,KAAK;oCAAwB,OAAO;oCAAQ,aAAa;gCAAY;gCACvE;oCAAE,KAAK;oCAAkB,OAAO;oCAAU,aAAa;gCAAY;gCACnE;oCAAE,KAAK;oCAAgB,OAAO;oCAAM,aAAa;gCAAW;gCAC5D;oCAAE,KAAK;oCAAiB,OAAO;oCAAM,aAAa;gCAAW;6BAC9D,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC;oCAAsB,WAAU;;sDAC/B,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAe,QAAQ,KAAK;;;;;;8DACzC,6LAAC;oDAAE,WAAU;8DAAiC,QAAQ,WAAW;;;;;;;;;;;;sDAEnE,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS,aAAa,CAAC,QAAQ,GAAG,CAA+B;oDACjE,UAAU,CAAC,IAAM,iBAAiB;4DAChC,GAAG,aAAa;4DAChB,CAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;wDACjC;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAfT,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;IAyBjC,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIxC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAClD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,kBAAkB,eAAe,wBAAwB,IACzD;gDACF,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;;4DAAsB;4DAC3B,kBAAkB,eAAe,SAAS;;;;;;;;;;;;;0DAGpD,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,kBAAkB,eAAe,wBAAwB,IACzD;gDACF,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;;4DAAsB;4DAC5B,kBAAkB,eAAe,SAAS;;;;;;;;;;;;;0DAGnD,6LAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,kBAAkB,iBAAiB,wBAAwB,IAC3D;gDACF,SAAS,IAAM,kBAAkB;;kEAEjC,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;;4DAAsB;4DAC5B,kBAAkB,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWnE,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAM7C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAMtC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUhD,MAAM,qBAAqB,kBACzB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;0CAIxC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQhD,MAAM,aAAa,kBACjB,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;8BAIvC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,yJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;IAKnB,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,iJAAA,CAAA,aAAU;;8BACT,6LAAC,iJAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;oBACZ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;gBAI3B,OAAO,MAAM,GAAG,mBACf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,6LAAC;wCAAG,WAAU;kDACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;;oDAAe;oDAAG;;+CAAV;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;gDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,uCACA,gDACJ;;kEAEF,6LAAC,IAAI,IAAI;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAuB,IAAI,KAAK;;;;;;;+CAT3C,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBvB,6LAAC;4BAAI,WAAU;;gCACZ;8CAGD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,QAAO;4DACP,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;;8EACd,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAIvC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAK1C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAoB,UAAU;sDAC5C,wBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAK;;;;;;;;;;;qEAGR;;kEACE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GAhqBwB;;QACL,sIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}