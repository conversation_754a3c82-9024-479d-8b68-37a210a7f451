import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key'

// Only create Supabase client if proper credentials are provided
const isSupabaseConfigured =
  supabaseUrl !== 'your_supabase_project_url' &&
  supabaseUrl !== 'https://demo.supabase.co' &&
  supabaseAnonKey !== 'your_supabase_anon_key' &&
  supabaseAnonKey !== 'demo-key'

export const supabase = isSupabaseConfigured
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null

export function createSupabaseClient() {
  if (!isSupabaseConfigured) return null
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Database helper functions
const createDbHelpers = () => {
  if (!supabase) {
    // Return mock functions when Supabase is not configured
    return {
      customers: {
        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') }),
        search: (query: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') })
      },
      staff: {
        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getActive: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })
      },
      services: {
        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getActive: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })
      },
      appointments: {
        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getByDate: (date: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getByStaff: (staffId: string, date?: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })
      },
      inventory: {
        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getLowStock: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        update: (id: string, data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        delete: (id: string) => Promise.resolve({ error: new Error('Supabase not configured') })
      },
      transactions: {
        getAll: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getById: (id: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        getByDateRange: (startDate: string, endDate: string) => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
        create: (data: any) => Promise.resolve({ data: null, error: new Error('Supabase not configured') })
      }
    }
  }

  return {
    // Customers
    customers: {
      getAll: () => supabase.from('customers').select('*').order('created_at', { ascending: false }),
      getById: (id: string) => supabase.from('customers').select('*').eq('id', id).single(),
      create: (data: any) => supabase.from('customers').insert(data).select().single(),
      update: (id: string, data: any) => supabase.from('customers').update(data).eq('id', id).select().single(),
      delete: (id: string) => supabase.from('customers').delete().eq('id', id),
      search: (query: string) => supabase
        .from('customers')
        .select('*')
        .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%,phone.ilike.%${query}%`)
        .order('created_at', { ascending: false })
    },

    // Staff
    staff: {
      getAll: () => supabase.from('staff').select('*').order('created_at', { ascending: false }),
      getById: (id: string) => supabase.from('staff').select('*').eq('id', id).single(),
      getActive: () => supabase.from('staff').select('*').eq('is_active', true).order('first_name'),
      create: (data: any) => supabase.from('staff').insert(data).select().single(),
      update: (id: string, data: any) => supabase.from('staff').update(data).eq('id', id).select().single(),
      delete: (id: string) => supabase.from('staff').delete().eq('id', id),
    },

    // Services
    services: {
      getAll: () => supabase.from('services').select('*').order('name'),
      getById: (id: string) => supabase.from('services').select('*').eq('id', id).single(),
      getActive: () => supabase.from('services').select('*').eq('is_active', true).order('name'),
      create: (data: any) => supabase.from('services').insert(data).select().single(),
      update: (id: string, data: any) => supabase.from('services').update(data).eq('id', id).select().single(),
      delete: (id: string) => supabase.from('services').delete().eq('id', id),
    },

    // Appointments
    appointments: {
      getAll: () => supabase
        .from('appointments')
        .select(`
          *,
          customer:customers(*),
          staff:staff(*),
          service:services(*)
        `)
        .order('appointment_date', { ascending: false }),
      getById: (id: string) => supabase
        .from('appointments')
        .select(`
          *,
          customer:customers(*),
          staff:staff(*),
          service:services(*)
        `)
        .eq('id', id)
        .single(),
      getByDate: (date: string) => supabase
        .from('appointments')
        .select(`
          *,
          customer:customers(*),
          staff:staff(*),
          service:services(*)
        `)
        .eq('appointment_date', date)
        .order('start_time'),
      getByStaff: (staffId: string, date?: string) => {
        let query = supabase
          .from('appointments')
          .select(`
            *,
            customer:customers(*),
            staff:staff(*),
            service:services(*)
          `)
          .eq('staff_id', staffId)

        if (date) {
          query = query.eq('appointment_date', date)
        }

        return query.order('appointment_date', { ascending: false })
      },
      create: (data: any) => supabase.from('appointments').insert(data).select().single(),
      update: (id: string, data: any) => supabase.from('appointments').update(data).eq('id', id).select().single(),
      delete: (id: string) => supabase.from('appointments').delete().eq('id', id),
    },

    // Inventory
    inventory: {
      getAll: () => supabase.from('inventory').select('*').order('name'),
      getById: (id: string) => supabase.from('inventory').select('*').eq('id', id).single(),
      getLowStock: () => supabase
        .from('inventory')
        .select('*')
        .filter('current_stock', 'lte', 'min_stock_level')
        .order('name'),
      create: (data: any) => supabase.from('inventory').insert(data).select().single(),
      update: (id: string, data: any) => supabase.from('inventory').update(data).eq('id', id).select().single(),
      delete: (id: string) => supabase.from('inventory').delete().eq('id', id),
    },

    // Transactions
    transactions: {
      getAll: () => supabase.from('transactions').select('*').order('transaction_date', { ascending: false }),
      getById: (id: string) => supabase.from('transactions').select('*').eq('id', id).single(),
      getByDateRange: (startDate: string, endDate: string) => supabase
        .from('transactions')
        .select('*')
        .gte('transaction_date', startDate)
        .lte('transaction_date', endDate)
        .order('transaction_date', { ascending: false }),
      create: (data: any) => supabase.from('transactions').insert(data).select().single(),
    }
  }
}

export const db = createDbHelpers()
