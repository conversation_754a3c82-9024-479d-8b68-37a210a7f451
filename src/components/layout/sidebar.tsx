'use client'

import { Navigation } from './navigation'
import { useAuth } from '@/contexts/auth-context'
import { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { getInitials } from '@/lib/utils'

export function Sidebar() {
  const { user, signOut } = useAuth()

  const handleSignOut = async () => {
    await signOut()
  }

  const userDisplayName = user?.user_metadata?.full_name ||
                          user?.email?.split('@')[0] ||
                          'User'

  const userInitials = getInitials(userDisplayName)

  return (
    <div className="flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b bg-gradient-primary">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <Crown className="h-7 w-7 text-white" />
            <Scissors className="h-4 w-4 text-white absolute -bottom-1 -right-1" />
          </div>
          <div>
            <span className="text-lg font-bold text-white">皇家理发店</span>
            <p className="text-xs text-white/80">专业美发沙龙</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 px-4 py-6 bg-gradient-secondary">
        <Navigation />
      </div>

      {/* User info */}
      <div className="border-t bg-card p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant">
            <span className="text-sm font-bold text-white">{userInitials}</span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-semibold text-foreground truncate">{userDisplayName}</p>
            <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
          </div>
          <div className="h-2 w-2 rounded-full bg-green-500" title="在线"></div>
        </div>

        {/* User Actions */}
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            className="flex-1 h-8 text-xs"
            onClick={() => {/* TODO: Open profile settings */}}
          >
            <User className="h-3 w-3 mr-1" />
            个人资料
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="flex-1 h-8 text-xs"
            onClick={handleSignOut}
          >
            <LogOut className="h-3 w-3 mr-1" />
            退出登录
          </Button>
        </div>
      </div>
    </div>
  )
}
