'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageHeader, PageHeaderActions } from '@/components/layout/page-header'
import { StaffForm } from '@/components/staff/staff-form'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { EmptyTableState } from '@/components/ui/empty-state'
import { Loading } from '@/components/ui/loading'
import { 
  UserCheck, 
  Plus, 
  Search, 
  Filter, 
  Phone,
  Mail,
  Calendar,
  Edit,
  Trash2,
  Clock,
  DollarSign,
  Crown,
  Scissors
} from 'lucide-react'
import { Staff } from '@/types'
import { db } from '@/lib/supabase'
import { formatDate, formatCurrency } from '@/lib/utils'

export default function StaffPage() {
  const [staff, setStaff] = useState<Staff[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredStaff, setFilteredStaff] = useState<Staff[]>([])
  const [showForm, setShowForm] = useState(false)
  const [editingStaff, setEditingStaff] = useState<Staff | undefined>()

  useEffect(() => {
    loadStaff()
  }, [])

  useEffect(() => {
    // Filter staff based on search term
    if (searchTerm.trim() === '') {
      setFilteredStaff(staff)
    } else {
      const filtered = staff.filter(member =>
        member.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.role.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredStaff(filtered)
    }
  }, [staff, searchTerm])

  const loadStaff = async () => {
    try {
      setLoading(true)
      const { data, error } = await db.staff.getAll()
      
      if (error) {
        console.error('Error loading staff:', error)
        // For demo purposes, use mock data if Supabase is not configured
        setStaff(mockStaff)
      } else {
        setStaff(data || [])
      }
    } catch (error) {
      console.error('Error loading staff:', error)
      // Use mock data as fallback
      setStaff(mockStaff)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteStaff = async (staffId: string) => {
    if (confirm('Are you sure you want to delete this staff member?')) {
      try {
        const { error } = await db.staff.delete(staffId)
        if (error) {
          console.error('Error deleting staff:', error)
        } else {
          setStaff(staff.filter(s => s.id !== staffId))
        }
      } catch (error) {
        console.error('Error deleting staff:', error)
      }
    }
  }

  const handleAddStaff = () => {
    setEditingStaff(undefined)
    setShowForm(true)
  }

  const handleEditStaff = (staff: Staff) => {
    setEditingStaff(staff)
    setShowForm(true)
  }

  const handleFormSuccess = (staff: Staff) => {
    if (editingStaff) {
      // Update existing staff
      setStaff(staffList => staffList.map(s => s.id === staff.id ? staff : s))
    } else {
      // Add new staff
      setStaff(staffList => [staff, ...staffList])
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'manager':
      case 'admin':
        return <Crown className="h-4 w-4" />
      case 'barber':
      case 'stylist':
        return <Scissors className="h-4 w-4" />
      default:
        return <UserCheck className="h-4 w-4" />
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive'
      case 'manager':
        return 'warning'
      case 'barber':
        return 'info'
      case 'stylist':
        return 'purple'
      default:
        return 'secondary'
    }
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <PageHeader
          title="员工管理"
          description="管理团队成员、排班和角色"
          icon={<UserCheck className="h-6 w-6 text-white" />}
          actions={
            <PageHeaderActions>
              <Button onClick={handleAddStaff}>
                <Plus className="h-4 w-4 mr-2" />
                添加员工
              </Button>
            </PageHeaderActions>
          }
        />

        <div className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-elegant">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search staff by name, email, or role..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Staff Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Staff</p>
                    <p className="text-2xl font-bold">{staff.length}</p>
                  </div>
                  <UserCheck className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Staff</p>
                    <p className="text-2xl font-bold">{staff.filter(s => s.is_active).length}</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Barbers</p>
                    <p className="text-2xl font-bold">{staff.filter(s => s.role === 'barber').length}</p>
                  </div>
                  <Scissors className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-elegant">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Stylists</p>
                    <p className="text-2xl font-bold">{staff.filter(s => s.role === 'stylist').length}</p>
                  </div>
                  <Crown className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Staff List */}
          <Card className="shadow-elegant">
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Loading text="Loading staff..." />
              ) : filteredStaff.length === 0 ? (
                searchTerm ? (
                  <EmptyTableState
                    icon={<Search className="h-8 w-8 text-muted-foreground" />}
                    title="No staff found"
                    description={`No staff members match your search for "${searchTerm}"`}
                    action={{
                      label: "Clear search",
                      onClick: () => setSearchTerm('')
                    }}
                  />
                ) : (
                  <EmptyTableState
                    icon={<UserCheck className="h-8 w-8 text-muted-foreground" />}
                    title="No staff members yet"
                    description="Start building your team by adding your first staff member"
                    action={{
                      label: "Add Staff Member",
                      onClick: handleAddStaff
                    }}
                  />
                )
              ) : (
                <div className="grid gap-4">
                  {filteredStaff.map((member) => (
                    <StaffCard
                      key={member.id}
                      staff={member}
                      onEdit={() => handleEditStaff(member)}
                      onDelete={handleDeleteStaff}
                      getRoleIcon={getRoleIcon}
                      getRoleBadgeVariant={getRoleBadgeVariant}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Staff Form Dialog */}
        <StaffForm
          staff={editingStaff}
          open={showForm}
          onOpenChange={setShowForm}
          onSuccess={handleFormSuccess}
        />
      </MainLayout>
    </ProtectedRoute>
  )
}

interface StaffCardProps {
  staff: Staff
  onEdit: () => void
  onDelete: (id: string) => void
  getRoleIcon: (role: string) => React.ReactNode
  getRoleBadgeVariant: (role: string) => any
}

function StaffCard({ staff, onEdit, onDelete, getRoleIcon, getRoleBadgeVariant }: StaffCardProps) {
  return (
    <div className="p-4 border rounded-lg hover:shadow-elegant transition-all duration-200 bg-card">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-gradient-primary flex items-center justify-center">
            <span className="text-white font-semibold">
              {staff.first_name[0]}{staff.last_name[0]}
            </span>
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="font-semibold text-foreground">
                {staff.first_name} {staff.last_name}
              </h3>
              <Badge variant={getRoleBadgeVariant(staff.role)} className="flex items-center space-x-1">
                {getRoleIcon(staff.role)}
                <span className="capitalize">{staff.role}</span>
              </Badge>
              {!staff.is_active && (
                <Badge variant="destructive">Inactive</Badge>
              )}
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Mail className="h-3 w-3 mr-1" />
                {staff.email}
              </div>
              <div className="flex items-center">
                <Phone className="h-3 w-3 mr-1" />
                {staff.phone}
              </div>
              {staff.hourly_rate && (
                <div className="flex items-center">
                  <DollarSign className="h-3 w-3 mr-1" />
                  {formatCurrency(staff.hourly_rate)}/hr
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            Since {formatDate(staff.hire_date)}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={onEdit}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(staff.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {staff.specialties && staff.specialties.length > 0 && (
        <div className="mt-3 pt-3 border-t">
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-muted-foreground">Specialties:</span>
            <div className="flex flex-wrap gap-1">
              {staff.specialties.map((specialty, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {specialty.replace('_', ' ')}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Mock data for demonstration
const mockStaff: Staff[] = [
  {
    id: '1',
    first_name: 'Alex',
    last_name: 'Rodriguez',
    email: '<EMAIL>',
    phone: '******-0201',
    role: 'manager',
    hire_date: '2020-01-15',
    hourly_rate: 25.00,
    is_active: true,
    specialties: ['management', 'customer_service', 'haircuts'],
    created_at: '2020-01-15T10:00:00Z',
    updated_at: '2020-01-15T10:00:00Z'
  },
  {
    id: '2',
    first_name: 'Maria',
    last_name: 'Garcia',
    email: '<EMAIL>',
    phone: '******-0202',
    role: 'barber',
    hire_date: '2021-03-10',
    hourly_rate: 22.00,
    is_active: true,
    specialties: ['haircuts', 'beard_trimming', 'classic_styles'],
    created_at: '2021-03-10T10:00:00Z',
    updated_at: '2021-03-10T10:00:00Z'
  },
  {
    id: '3',
    first_name: 'James',
    last_name: 'Thompson',
    email: '<EMAIL>',
    phone: '******-0203',
    role: 'stylist',
    hire_date: '2021-06-20',
    hourly_rate: 24.00,
    is_active: true,
    specialties: ['modern_cuts', 'styling', 'coloring'],
    created_at: '2021-06-20T10:00:00Z',
    updated_at: '2021-06-20T10:00:00Z'
  },
  {
    id: '4',
    first_name: 'Sofia',
    last_name: 'Martinez',
    email: '<EMAIL>',
    phone: '******-0204',
    role: 'stylist',
    hire_date: '2022-02-01',
    hourly_rate: 23.00,
    is_active: true,
    specialties: ['women_cuts', 'highlights', 'treatments'],
    created_at: '2022-02-01T10:00:00Z',
    updated_at: '2022-02-01T10:00:00Z'
  }
]
