// Settings management for local storage
export interface BusinessSettings {
  name: string
  address: string
  phone: string
  email: string
  website: string
  timezone: string
  currency: string
}

export interface DayHours {
  open: string
  close: string
  closed: boolean
}

export interface OperatingHours {
  [key: string]: DayHours
}

export interface Holiday {
  id: string
  name: string
  date: string // YYYY-MM-DD format
  type: 'fixed' | 'lunar' | 'custom' // 固定日期、农历、自定义
  recurring: boolean // 是否每年重复
  closed: boolean // 是否休息
  hours?: DayHours // 如果不休息，特殊营业时间
  description?: string
  source?: 'manual' | 'api' | 'wannianli' | 'china_holiday' | 'tianapi' | 'juhe' // 数据来源
}

export interface HolidaySettings {
  holidays: Holiday[]
  enableHolidayMode: boolean // 是否启用节假日模式
}

export interface NotificationSettings {
  emailNotifications: boolean
  smsNotifications: boolean
  appointmentReminders: boolean
  lowStockAlerts: boolean
  dailyReports: boolean
  weeklyReports: boolean
}

export interface AppSettings {
  business: BusinessSettings
  operatingHours: OperatingHours
  holidays: HolidaySettings
  notifications: NotificationSettings
  theme: string
  language: string
}

const DEFAULT_SETTINGS: AppSettings = {
  business: {
    name: '皇家理发店',
    address: '北京市朝阳区三里屯街道1号',
    phone: '+86 138-0013-8000',
    email: '<EMAIL>',
    website: 'www.royalcuts.cn',
    timezone: 'Asia/Shanghai',
    currency: 'CNY'
  },
  operatingHours: {
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '19:00', closed: false },
    friday: { open: '09:00', close: '19:00', closed: false },
    saturday: { open: '08:00', close: '17:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: false }
  },
  holidays: {
    enableHolidayMode: true,
    holidays: [
      {
        id: 'new-year',
        name: '元旦',
        date: '2024-01-01',
        type: 'fixed',
        recurring: true,
        closed: true,
        description: '新年第一天',
        source: 'manual'
      },
      {
        id: 'spring-festival',
        name: '春节',
        date: '2024-02-10',
        type: 'lunar',
        recurring: true,
        closed: true,
        description: '农历新年',
        source: 'manual'
      },
      {
        id: 'labor-day',
        name: '劳动节',
        date: '2024-05-01',
        type: 'fixed',
        recurring: true,
        closed: true,
        description: '国际劳动节',
        source: 'manual'
      },
      {
        id: 'national-day',
        name: '国庆节',
        date: '2024-10-01',
        type: 'fixed',
        recurring: true,
        closed: true,
        description: '中华人民共和国国庆节',
        source: 'manual'
      }
    ]
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    appointmentReminders: true,
    lowStockAlerts: true,
    dailyReports: false,
    weeklyReports: true
  },
  theme: 'royal-gold',
  language: 'zh-CN'
}

const SETTINGS_KEY = 'barbershop_settings'

export class SettingsManager {
  static getSettings(): AppSettings {
    if (typeof window === 'undefined') {
      return DEFAULT_SETTINGS
    }

    try {
      const stored = localStorage.getItem(SETTINGS_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        // Merge with defaults to ensure all properties exist
        return {
          ...DEFAULT_SETTINGS,
          ...parsed,
          business: { ...DEFAULT_SETTINGS.business, ...parsed.business },
          operatingHours: { ...DEFAULT_SETTINGS.operatingHours, ...parsed.operatingHours },
          holidays: { ...DEFAULT_SETTINGS.holidays, ...parsed.holidays },
          notifications: { ...DEFAULT_SETTINGS.notifications, ...parsed.notifications }
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }

    return DEFAULT_SETTINGS
  }

  static saveSettings(settings: AppSettings): void {
    if (typeof window === 'undefined') {
      return
    }

    try {
      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settings))
    } catch (error) {
      console.error('Error saving settings:', error)
      throw new Error('无法保存设置')
    }
  }

  static updateBusinessSettings(business: BusinessSettings): void {
    const settings = this.getSettings()
    settings.business = business
    this.saveSettings(settings)
  }

  static updateOperatingHours(operatingHours: OperatingHours): void {
    const settings = this.getSettings()
    settings.operatingHours = operatingHours
    this.saveSettings(settings)
  }

  static updateHolidaySettings(holidays: HolidaySettings): void {
    const settings = this.getSettings()
    settings.holidays = holidays
    this.saveSettings(settings)
  }

  static updateNotificationSettings(notifications: NotificationSettings): void {
    const settings = this.getSettings()
    settings.notifications = notifications
    this.saveSettings(settings)
  }

  static updateTheme(theme: string): void {
    const settings = this.getSettings()
    settings.theme = theme
    this.saveSettings(settings)
  }

  static resetToDefaults(): void {
    this.saveSettings(DEFAULT_SETTINGS)
  }

  static exportSettings(): string {
    const settings = this.getSettings()
    return JSON.stringify(settings, null, 2)
  }

  static importSettings(settingsJson: string): void {
    try {
      const imported = JSON.parse(settingsJson)
      // Validate the structure
      if (imported.business && imported.operatingHours && imported.notifications) {
        // Ensure holidays exist, use default if not
        if (!imported.holidays) {
          imported.holidays = DEFAULT_SETTINGS.holidays
        }
        this.saveSettings(imported)
      } else {
        throw new Error('Invalid settings format')
      }
    } catch (error) {
      console.error('Error importing settings:', error)
      throw new Error('无效的设置格式')
    }
  }
}

// Utility functions for formatting
export function formatOperatingHours(hours: OperatingHours): string {
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  
  return days.map((day, index) => {
    const dayHours = hours[day]
    if (dayHours.closed) {
      return `${dayNames[index]}: 休息`
    }
    return `${dayNames[index]}: ${dayHours.open} - ${dayHours.close}`
  }).join('\n')
}

export function validateBusinessSettings(business: BusinessSettings): string[] {
  const errors: string[] = []
  
  if (!business.name.trim()) {
    errors.push('商户名称不能为空')
  }
  
  if (!business.phone.trim()) {
    errors.push('联系电话不能为空')
  }
  
  if (!business.email.trim()) {
    errors.push('邮箱地址不能为空')
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(business.email)) {
    errors.push('邮箱地址格式不正确')
  }
  
  if (!business.address.trim()) {
    errors.push('商户地址不能为空')
  }
  
  return errors
}

export function validateOperatingHours(hours: OperatingHours): string[] {
  const errors: string[] = []
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  
  days.forEach((day, index) => {
    const dayHours = hours[day]
    if (!dayHours.closed) {
      if (!dayHours.open || !dayHours.close) {
        errors.push(`${dayNames[index]}的营业时间不完整`)
      } else if (dayHours.open >= dayHours.close) {
        errors.push(`${dayNames[index]}的开始时间必须早于结束时间`)
      }
    }
  })
  
  return errors
}

// Theme utilities
export function applyTheme(theme: string): void {
  if (typeof document === 'undefined') return
  
  const root = document.documentElement
  
  switch (theme) {
    case 'royal-gold':
      root.style.setProperty('--primary', '180 83% 40%')
      root.style.setProperty('--primary-foreground', '0 0% 98%')
      break
    case 'ocean-blue':
      root.style.setProperty('--primary', '217 91% 60%')
      root.style.setProperty('--primary-foreground', '0 0% 98%')
      break
    case 'forest-green':
      root.style.setProperty('--primary', '142 76% 36%')
      root.style.setProperty('--primary-foreground', '0 0% 98%')
      break
    default:
      // Keep default theme
      break
  }
}

// Initialize theme on load
export function initializeTheme(): void {
  const settings = SettingsManager.getSettings()
  applyTheme(settings.theme)
}

// Holiday management utilities
export function generateHolidayId(): string {
  return 'holiday-' + Math.random().toString(36).substr(2, 9)
}

export function isHolidayToday(holidays: Holiday[]): Holiday | null {
  const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD
  return holidays.find(holiday => {
    if (!holiday.recurring) {
      return holiday.date === today
    }

    // For recurring holidays, check if month and day match
    const holidayDate = new Date(holiday.date)
    const todayDate = new Date(today)

    if (holiday.type === 'fixed') {
      return holidayDate.getMonth() === todayDate.getMonth() &&
             holidayDate.getDate() === todayDate.getDate()
    }

    // For lunar holidays, would need lunar calendar conversion
    // For now, just check exact date
    return holiday.date === today
  }) || null
}

export function getEffectiveHours(
  date: string,
  operatingHours: OperatingHours,
  holidaySettings: HolidaySettings
): DayHours | null {
  if (!holidaySettings.enableHolidayMode) {
    // If holiday mode is disabled, use regular hours
    const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' })
    return operatingHours[dayOfWeek] || null
  }

  // Check if date is a holiday
  const holiday = holidaySettings.holidays.find(h => {
    if (!h.recurring) {
      return h.date === date
    }

    const holidayDate = new Date(h.date)
    const checkDate = new Date(date)

    if (h.type === 'fixed') {
      return holidayDate.getMonth() === checkDate.getMonth() &&
             holidayDate.getDate() === checkDate.getDate()
    }

    return h.date === date
  })

  if (holiday) {
    if (holiday.closed) {
      return { open: '', close: '', closed: true }
    }
    if (holiday.hours) {
      return holiday.hours
    }
  }

  // Use regular operating hours
  const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' })
  return operatingHours[dayOfWeek] || null
}

export function validateHoliday(holiday: Partial<Holiday>): string[] {
  const errors: string[] = []

  if (!holiday.name?.trim()) {
    errors.push('节假日名称不能为空')
  }

  if (!holiday.date) {
    errors.push('请选择日期')
  } else {
    const date = new Date(holiday.date)
    if (isNaN(date.getTime())) {
      errors.push('日期格式不正确')
    }
  }

  if (!holiday.type) {
    errors.push('请选择节假日类型')
  }

  if (!holiday.closed && holiday.hours) {
    if (!holiday.hours.open || !holiday.hours.close) {
      errors.push('请设置营业时间')
    } else if (holiday.hours.open >= holiday.hours.close) {
      errors.push('开始时间必须早于结束时间')
    }
  }

  return errors
}

export function formatHolidayDate(date: string): string {
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function getHolidayTypeLabel(type: string): string {
  switch (type) {
    case 'fixed': return '固定日期'
    case 'lunar': return '农历日期'
    case 'custom': return '自定义'
    default: return type
  }
}

export function getUpcomingHolidays(holidays: Holiday[], days: number = 30): Holiday[] {
  const today = new Date()
  const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000)

  return holidays.filter(holiday => {
    const holidayDate = new Date(holiday.date)

    if (!holiday.recurring) {
      return holidayDate >= today && holidayDate <= futureDate
    }

    // For recurring holidays, check if they occur within the next 'days' period
    if (holiday.type === 'fixed') {
      const thisYear = new Date(today.getFullYear(), holidayDate.getMonth(), holidayDate.getDate())
      const nextYear = new Date(today.getFullYear() + 1, holidayDate.getMonth(), holidayDate.getDate())

      return (thisYear >= today && thisYear <= futureDate) ||
             (nextYear >= today && nextYear <= futureDate)
    }

    return false
  }).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
}
