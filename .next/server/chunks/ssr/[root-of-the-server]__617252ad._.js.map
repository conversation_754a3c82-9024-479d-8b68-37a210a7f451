{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport {\n  Calendar,\n  Users,\n  Scissors,\n  UserCheck,\n  Package,\n  BarChart3,\n  Settings,\n  Home,\n  Navigation as NavigationIcon\n} from 'lucide-react'\n\nconst navigation = [\n  { name: '仪表板', href: '/', icon: Home },\n  { name: '预约管理', href: '/appointments', icon: Calendar },\n  { name: '客户管理', href: '/customers', icon: Users },\n  { name: '员工管理', href: '/staff', icon: UserCheck },\n  { name: '服务管理', href: '/services', icon: Scissors },\n  { name: '库存管理', href: '/inventory', icon: Package },\n  { name: '数据分析', href: '/analytics', icon: BarChart3 },\n  { name: '系统设置', href: '/settings', icon: Settings },\n  { name: '定位演示', href: '/demo-location', icon: NavigationIcon },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"flex flex-col space-y-2\">\n      {navigation.map((item, index) => {\n        const isActive = pathname === item.href\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            className={cn(\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',\n              isActive\n                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'\n                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'\n            )}\n            style={{ animationDelay: `${index * 50}ms` }}\n          >\n            <item.icon className={cn(\n              \"mr-3 h-5 w-5 transition-all duration-200\",\n              isActive\n                ? \"text-primary scale-110\"\n                : \"text-muted-foreground group-hover:text-primary group-hover:scale-105\"\n            )} />\n            <span className=\"font-medium\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary animate-pulse\"></div>\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACrC;QAAE,MAAM;QAAQ,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,oMAAA,CAAA,QAAK;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAU,MAAM,gNAAA,CAAA,YAAS;IAAC;IAChD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,wMAAA,CAAA,UAAO;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACpD;QAAE,MAAM;QAAQ,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClD;QAAE,MAAM;QAAQ,MAAM;QAAkB,MAAM,8MAAA,CAAA,aAAc;IAAC;CAC9D;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,WAAW,aAAa,KAAK,IAAI;YACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gBAEH,MAAM,KAAK,IAAI;gBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gHACA,WACI,qEACA;gBAEN,OAAO;oBAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAAC;;kCAE3C,8OAAC,KAAK,IAAI;wBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,2BACA;;;;;;kCAEN,8OAAC;wBAAK,WAAU;kCAAe,KAAK,IAAI;;;;;;oBACvC,0BACC,8OAAC;wBAAI,WAAU;;;;;;;eAlBZ,KAAK,IAAI;;;;;QAsBpB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-elegant hover:shadow-elegant-lg transform hover:scale-105 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-primary text-white hover:opacity-90\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700\",\n        outline:\n          \"border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-white\",\n        secondary:\n          \"bg-gradient-secondary text-secondary-foreground hover:opacity-90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-elegant\",\n        link: \"text-primary underline-offset-4 hover:underline shadow-none\",\n        success: \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\",\n        warning: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600\",\n        info: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-10 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getInitials } from '@/lib/utils'\n\nexport function Sidebar() {\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const userDisplayName = user?.user_metadata?.full_name ||\n                          user?.email?.split('@')[0] ||\n                          'User'\n\n  const userInitials = getInitials(userDisplayName)\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b bg-gradient-primary\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"relative\">\n            <Crown className=\"h-7 w-7 text-white\" />\n            <Scissors className=\"h-4 w-4 text-white absolute -bottom-1 -right-1\" />\n          </div>\n          <div>\n            <span className=\"text-lg font-bold text-white\">皇家理发店</span>\n            <p className=\"text-xs text-white/80\">专业美发沙龙</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 px-4 py-6 bg-gradient-secondary\">\n        <Navigation />\n      </div>\n\n      {/* User info */}\n      <div className=\"border-t bg-card p-4\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant\">\n            <span className=\"text-sm font-bold text-white\">{userInitials}</span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-foreground truncate\">{userDisplayName}</p>\n            <p className=\"text-xs text-muted-foreground truncate\">{user?.email}</p>\n          </div>\n          <div className=\"h-2 w-2 rounded-full bg-green-500\" title=\"在线\"></div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex space-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={() => {/* TODO: Open profile settings */}}\n          >\n            <User className=\"h-3 w-3 mr-1\" />\n            个人资料\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={handleSignOut}\n          >\n            <LogOut className=\"h-3 w-3 mr-1\" />\n            退出登录\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM,eAAe,aACrB,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAC1B;IAExB,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAA0C,MAAM;;;;;;;;;;;;0CAE/D,8OAAC;gCAAI,WAAU;gCAAoC,OAAM;;;;;;;;;;;;kCAI3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,KAAwC;;kDAEjD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { Sidebar } from './sidebar'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background overflow-hidden\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto bg-gradient-to-br from-background via-secondary/30 to-muted/50\">\n        <div className=\"p-8 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Crown, Scissors } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireAdmin?: boolean\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallback \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"flex flex-col items-center space-y-4\">\n            <div className=\"relative animate-pulse\">\n              <Crown className=\"h-12 w-12 text-primary\" />\n              <Scissors className=\"h-8 w-8 text-accent absolute -bottom-2 -right-2\" />\n            </div>\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-semibold text-foreground mb-2\">Royal Cuts</h2>\n              <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n            </div>\n            <div className=\"flex space-x-1\">\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Show unauthorized if user is not logged in\n  if (!user) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"text-center\">\n            <div className=\"mb-4\">\n              <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              You need to be logged in to access this page.\n            </p>\n            <button\n              onClick={() => router.push('/auth/login')}\n              className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Go to Login\n            </button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Check admin requirement\n  if (requireAdmin) {\n    const isAdmin = user?.user_metadata?.role === 'admin' || \n                    user?.email?.endsWith('@royalcuts.com') ||\n                    user?.app_metadata?.role === 'admin'\n\n    if (!isAdmin) {\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n          <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n            <CardContent className=\"text-center\">\n              <div className=\"mb-4\">\n                <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Admin Access Required</h2>\n              <p className=\"text-muted-foreground mb-4\">\n                You need administrator privileges to access this page.\n              </p>\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Go to Dashboard\n              </button>\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: { requireAdmin?: boolean }\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={options?.requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Hook for checking authentication status\nexport function useRequireAuth(requireAdmin = false) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireAdmin) {\n        const isAdmin = user?.user_metadata?.role === 'admin' || \n                        user?.email?.endsWith('@royalcuts.com') ||\n                        user?.app_metadata?.role === 'admin'\n        \n        if (!isAdmin) {\n          router.push('/')\n          return\n        }\n      }\n    }\n  }, [user, loading, requireAdmin, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,KAAK,EACpB,QAAQ,EACY;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAChG,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5G;IAEA,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,OAAO,0BACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,0BAA0B;IAC1B,IAAI,cAAc;QAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;QAE7C,IAAI,CAAC,SAAS;YACZ,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;QAOX;IACF;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,SACd,SAAiC,EACjC,OAAoC;IAEpC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,8OAAC;YAAe,cAAc,SAAS;sBACrC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,eAAe,KAAK;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,cAAc;gBAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;gBAE7C,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,OAAO;QAAE;QAAM;IAAQ;AACzB", "debugId": null}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/page-header.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: React.ReactNode\n  actions?: React.ReactNode\n  breadcrumbs?: Array<{\n    label: string\n    href?: string\n  }>\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon,\n  actions,\n  breadcrumbs,\n  className\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 mb-8\", className)}>\n      {/* Breadcrumbs */}\n      {breadcrumbs && breadcrumbs.length > 0 && (\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n            {breadcrumbs.map((crumb, index) => (\n              <li key={index} className=\"flex items-center\">\n                {index > 0 && (\n                  <svg\n                    className=\"h-4 w-4 mx-2\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n                {crumb.href ? (\n                  <a\n                    href={crumb.href}\n                    className=\"hover:text-foreground transition-colors\"\n                  >\n                    {crumb.label}\n                  </a>\n                ) : (\n                  <span className=\"text-foreground font-medium\">\n                    {crumb.label}\n                  </span>\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      )}\n\n      {/* Header Content */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-4\">\n          {icon && (\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              {icon}\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-muted-foreground text-lg mt-1\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions && (\n          <div className=\"flex items-center space-x-2\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\ninterface PageHeaderActionsProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function PageHeaderActions({ children, className }: PageHeaderActionsProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2\", className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface QuickStatsProps {\n  stats: Array<{\n    label: string\n    value: string | number\n    icon?: React.ReactNode\n    trend?: {\n      value: number\n      isPositive: boolean\n    }\n  }>\n  className?: string\n}\n\nexport function QuickStats({ stats, className }: QuickStatsProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\", className)}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-card rounded-xl p-4 border shadow-elegant hover:shadow-elegant-lg transition-all duration-300\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">\n                {stat.label}\n              </p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {stat.value}\n              </p>\n              {stat.trend && (\n                <p className={cn(\n                  \"text-xs flex items-center mt-1\",\n                  stat.trend.isPositive ? \"text-green-600\" : \"text-red-600\"\n                )}>\n                  <span className=\"mr-1\">\n                    {stat.trend.isPositive ? \"↗\" : \"↘\"}\n                  </span>\n                  {Math.abs(stat.trend.value)}%\n                </p>\n              )}\n            </div>\n            {stat.icon && (\n              <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n                {stat.icon}\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAeO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACX,SAAS,EACO;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAElC,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAU;gBAAO,cAAW;0BAC/B,cAAA,8OAAC;oBAAG,WAAU;8BACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;4BAAe,WAAU;;gCACvB,QAAQ,mBACP,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAId,MAAM,IAAI,iBACT,8OAAC;oCACC,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;yDAGd,8OAAC;oCAAK,WAAU;8CACb,MAAM,KAAK;;;;;;;2BAvBT;;;;;;;;;;;;;;;0BAiCjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ,sBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;AAOO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;;;;;;AAGP;AAeO,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;kBAC7E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAEC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,KAAK,kBACT,8OAAC;oCAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,kCACA,KAAK,KAAK,CAAC,UAAU,GAAG,mBAAmB;;sDAE3C,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM;;;;;;wCAEhC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK;wCAAE;;;;;;;;;;;;;wBAIjC,KAAK,IAAI,kBACR,8OAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI;;;;;;;;;;;;eAzBX;;;;;;;;;;AAiCf", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA,SAAS,6CACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1364, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { Crown, Scissors } from \"lucide-react\"\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  text?: string\n  fullScreen?: boolean\n}\n\nexport function Loading({ \n  size = 'md', \n  className, \n  text = 'Loading...', \n  fullScreen = false \n}: LoadingProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  const containerClasses = fullScreen \n    ? 'fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50'\n    : 'flex items-center justify-center p-4'\n\n  return (\n    <div className={cn(containerClasses, className)}>\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"relative animate-pulse\">\n          <Crown className={cn(sizeClasses[size], \"text-primary\")} />\n          <Scissors className={cn(\n            size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-5 w-5' : 'h-8 w-8',\n            \"text-accent absolute -bottom-1 -right-1\"\n          )} />\n        </div>\n        {text && (\n          <p className={cn(\n            \"text-muted-foreground\",\n            size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'\n          )}>\n            {text}\n          </p>\n        )}\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n          <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n          <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingSpinner({ \n  size = 'md', \n  className \n}: { \n  size?: 'sm' | 'md' | 'lg'\n  className?: string \n}) {\n  const sizeClasses = {\n    sm: 'h-4 w-4 border-2',\n    md: 'h-6 w-6 border-2',\n    lg: 'h-8 w-8 border-3'\n  }\n\n  return (\n    <div className={cn(\n      \"border-primary border-t-transparent rounded-full animate-spin\",\n      sizeClasses[size],\n      className\n    )} />\n  )\n}\n\nexport function LoadingDots({ \n  size = 'md', \n  className \n}: { \n  size?: 'sm' | 'md' | 'lg'\n  className?: string \n}) {\n  const dotSizes = {\n    sm: 'h-1 w-1',\n    md: 'h-2 w-2',\n    lg: 'h-3 w-3'\n  }\n\n  return (\n    <div className={cn(\"flex space-x-1\", className)}>\n      <div className={cn(dotSizes[size], \"bg-primary rounded-full animate-bounce\")}></div>\n      <div className={cn(dotSizes[size], \"bg-primary rounded-full animate-bounce\")} style={{ animationDelay: '0.1s' }}></div>\n      <div className={cn(dotSizes[size], \"bg-primary rounded-full animate-bounce\")} style={{ animationDelay: '0.2s' }}></div>\n    </div>\n  )\n}\n\nexport function LoadingCard({ \n  className,\n  children \n}: { \n  className?: string\n  children?: React.ReactNode \n}) {\n  return (\n    <div className={cn(\n      \"rounded-xl border bg-card p-6 shadow-elegant animate-pulse\",\n      className\n    )}>\n      <div className=\"space-y-3\">\n        <div className=\"h-4 bg-muted rounded w-3/4\"></div>\n        <div className=\"h-4 bg-muted rounded w-1/2\"></div>\n        <div className=\"h-4 bg-muted rounded w-5/6\"></div>\n      </div>\n      {children}\n    </div>\n  )\n}\n\nexport function LoadingTable({ \n  rows = 5,\n  columns = 4,\n  className \n}: { \n  rows?: number\n  columns?: number\n  className?: string \n}) {\n  return (\n    <div className={cn(\"space-y-3\", className)}>\n      {/* Header */}\n      <div className=\"flex space-x-4\">\n        {Array.from({ length: columns }).map((_, i) => (\n          <div key={i} className=\"h-4 bg-muted rounded flex-1 animate-pulse\"></div>\n        ))}\n      </div>\n      \n      {/* Rows */}\n      {Array.from({ length: rows }).map((_, rowIndex) => (\n        <div key={rowIndex} className=\"flex space-x-4\">\n          {Array.from({ length: columns }).map((_, colIndex) => (\n            <div \n              key={colIndex} \n              className=\"h-4 bg-muted/60 rounded flex-1 animate-pulse\"\n              style={{ animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s` }}\n            ></div>\n          ))}\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;;;;AASO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,SAAS,EACT,OAAO,YAAY,EACnB,aAAa,KAAK,EACL;IACb,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,mBAAmB,aACrB,0FACA;IAEJ,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;sCACxC,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACpB,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WACxD;;;;;;;;;;;;gBAGH,sBACC,8OAAC;oBAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,yBACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY;8BAEvD;;;;;;8BAGL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;4BAAiD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCAChG,8OAAC;4BAAI,WAAU;4BAAiD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;;;;;;;;;;;;;AAK1G;AAEO,SAAS,eAAe,EAC7B,OAAO,IAAI,EACX,SAAS,EAIV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,iEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAGN;AAEO,SAAS,YAAY,EAC1B,OAAO,IAAI,EACX,SAAS,EAIV;IACC,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;0BACnC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE;;;;;;0BACnC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE;gBAA2C,OAAO;oBAAE,gBAAgB;gBAAO;;;;;;0BAC9G,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE;gBAA2C,OAAO;oBAAE,gBAAgB;gBAAO;;;;;;;;;;;;AAGpH;AAEO,SAAS,YAAY,EAC1B,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,8DACA;;0BAEA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;YAEhB;;;;;;;AAGP;AAEO,SAAS,aAAa,EAC3B,OAAO,CAAC,EACR,UAAU,CAAC,EACX,SAAS,EAKV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,kBACvC,8OAAC;wBAAY,WAAU;uBAAb;;;;;;;;;;YAKb,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,8OAAC;oBAAmB,WAAU;8BAC3B,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,CAAC,WAAW,UAAU,QAAQ,IAAI,IAAI,CAAC,CAAC;4BAAC;2BAFhE;;;;;mBAHD;;;;;;;;;;;AAYlB", "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/inventory/inventory-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogFooter, \n  DialogHeader, \n  DialogTitle \n} from '@/components/ui/dialog'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { LoadingSpinner } from '@/components/ui/loading'\nimport { \n  Package, \n  DollarSign, \n  Hash, \n  FileText, \n  Building,\n  Calendar,\n  Droplets,\n  Scissors,\n  Sparkles,\n  Tag\n} from 'lucide-react'\nimport { InventoryItem, InventoryFormData } from '@/types'\nimport { db } from '@/lib/supabase'\n\nconst inventorySchema = z.object({\n  name: z.string().min(1, 'Item name is required').max(100, 'Name too long'),\n  description: z.string().optional(),\n  category: z.enum(['shampoo', 'conditioner', 'styling', 'tools', 'supplies', 'other'], {\n    required_error: 'Please select a category'\n  }),\n  brand: z.string().optional(),\n  sku: z.string().optional(),\n  current_stock: z.number().min(0, 'Stock cannot be negative'),\n  min_stock_level: z.number().min(0, 'Minimum level cannot be negative'),\n  unit_cost: z.number().min(0, 'Cost must be positive'),\n  supplier: z.string().optional()\n})\n\ninterface InventoryFormProps {\n  item?: InventoryItem\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onSuccess: (item: InventoryItem) => void\n}\n\nconst categoryOptions = [\n  { value: 'shampoo', label: 'Shampoo', icon: Droplets },\n  { value: 'conditioner', label: 'Conditioner', icon: Droplets },\n  { value: 'styling', label: 'Styling Products', icon: Sparkles },\n  { value: 'tools', label: 'Tools & Equipment', icon: Scissors },\n  { value: 'supplies', label: 'Supplies', icon: Package },\n  { value: 'other', label: 'Other', icon: Tag }\n]\n\nexport function InventoryForm({ item, open, onOpenChange, onSuccess }: InventoryFormProps) {\n  const [loading, setLoading] = useState(false)\n  const isEditing = !!item\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    setValue,\n    watch\n  } = useForm<InventoryFormData>({\n    resolver: zodResolver(inventorySchema),\n    defaultValues: item ? {\n      name: item.name,\n      description: item.description || '',\n      category: item.category,\n      brand: item.brand || '',\n      sku: item.sku || '',\n      current_stock: item.current_stock,\n      min_stock_level: item.min_stock_level,\n      unit_cost: item.unit_cost,\n      supplier: item.supplier || ''\n    } : {\n      name: '',\n      description: '',\n      category: 'supplies',\n      brand: '',\n      sku: '',\n      current_stock: 0,\n      min_stock_level: 0,\n      unit_cost: 0,\n      supplier: ''\n    }\n  })\n\n  const selectedCategory = watch('category')\n  const currentStock = watch('current_stock')\n  const unitCost = watch('unit_cost')\n\n  const onSubmit = async (data: InventoryFormData) => {\n    try {\n      setLoading(true)\n      \n      const inventoryData = {\n        ...data,\n        description: data.description || undefined,\n        brand: data.brand || undefined,\n        sku: data.sku || undefined,\n        supplier: data.supplier || undefined,\n        last_restocked: isEditing ? item.last_restocked : new Date().toISOString().split('T')[0]\n      }\n\n      let result\n      if (isEditing) {\n        result = await db.inventory.update(item.id, inventoryData)\n      } else {\n        result = await db.inventory.create(inventoryData)\n      }\n\n      if (result.error) {\n        console.error('Error saving inventory item:', result.error)\n        alert('Error saving inventory item. Please try again.')\n        return\n      }\n\n      onSuccess(result.data)\n      onOpenChange(false)\n      reset()\n    } catch (error) {\n      console.error('Error saving inventory item:', error)\n      alert('Error saving inventory item. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    onOpenChange(false)\n    reset()\n  }\n\n  const getCategoryIcon = (category: string) => {\n    const option = categoryOptions.find(opt => opt.value === category)\n    return option ? <option.icon className=\"h-4 w-4\" /> : <Package className=\"h-4 w-4\" />\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center space-x-2\">\n            <Package className=\"h-5 w-5\" />\n            <span>{isEditing ? 'Edit Inventory Item' : 'Add New Inventory Item'}</span>\n          </DialogTitle>\n          <DialogDescription>\n            {isEditing \n              ? 'Update inventory item information and stock levels'\n              : 'Add a new item to your inventory'\n            }\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* Basic Information */}\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg flex items-center\">\n                <Package className=\"h-4 w-4 mr-2\" />\n                Item Information\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label htmlFor=\"name\" className=\"text-sm font-medium\">\n                  Item Name *\n                </label>\n                <Input\n                  id=\"name\"\n                  {...register('name')}\n                  placeholder=\"Professional Shampoo\"\n                  error={!!errors.name}\n                />\n                {errors.name && (\n                  <p className=\"text-sm text-red-500\">{errors.name.message}</p>\n                )}\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"category\" className=\"text-sm font-medium\">\n                    Category *\n                  </label>\n                  <Select\n                    value={selectedCategory}\n                    onValueChange={(value) => setValue('category', value as any)}\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select category\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {categoryOptions.map((option) => (\n                        <SelectItem key={option.value} value={option.value}>\n                          <div className=\"flex items-center space-x-2\">\n                            <option.icon className=\"h-4 w-4\" />\n                            <span>{option.label}</span>\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  {errors.category && (\n                    <p className=\"text-sm text-red-500\">{errors.category.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"brand\" className=\"text-sm font-medium\">\n                    Brand\n                  </label>\n                  <div className=\"relative\">\n                    <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      id=\"brand\"\n                      {...register('brand')}\n                      placeholder=\"SalonPro\"\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"sku\" className=\"text-sm font-medium\">\n                    SKU\n                  </label>\n                  <div className=\"relative\">\n                    <Hash className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      id=\"sku\"\n                      {...register('sku')}\n                      placeholder=\"SP-SHAM-001\"\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"supplier\" className=\"text-sm font-medium\">\n                    Supplier\n                  </label>\n                  <div className=\"relative\">\n                    <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      id=\"supplier\"\n                      {...register('supplier')}\n                      placeholder=\"Beauty Supply Co\"\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"description\" className=\"text-sm font-medium\">\n                  Description\n                </label>\n                <div className=\"relative\">\n                  <FileText className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\n                  <textarea\n                    id=\"description\"\n                    {...register('description')}\n                    placeholder=\"Brief description of the item...\"\n                    className=\"w-full pl-10 pt-2 pb-2 pr-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                    rows={3}\n                  />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Stock and Pricing */}\n          <Card>\n            <CardHeader className=\"pb-3\">\n              <CardTitle className=\"text-lg flex items-center\">\n                <DollarSign className=\"h-4 w-4 mr-2\" />\n                Stock & Pricing\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-3 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"current_stock\" className=\"text-sm font-medium\">\n                    Current Stock *\n                  </label>\n                  <Input\n                    id=\"current_stock\"\n                    type=\"number\"\n                    {...register('current_stock', { valueAsNumber: true })}\n                    placeholder=\"0\"\n                    error={!!errors.current_stock}\n                  />\n                  {errors.current_stock && (\n                    <p className=\"text-sm text-red-500\">{errors.current_stock.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"min_stock_level\" className=\"text-sm font-medium\">\n                    Min Stock Level *\n                  </label>\n                  <Input\n                    id=\"min_stock_level\"\n                    type=\"number\"\n                    {...register('min_stock_level', { valueAsNumber: true })}\n                    placeholder=\"0\"\n                    error={!!errors.min_stock_level}\n                  />\n                  {errors.min_stock_level && (\n                    <p className=\"text-sm text-red-500\">{errors.min_stock_level.message}</p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"unit_cost\" className=\"text-sm font-medium\">\n                    Unit Cost *\n                  </label>\n                  <div className=\"relative\">\n                    <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                    <Input\n                      id=\"unit_cost\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      {...register('unit_cost', { valueAsNumber: true })}\n                      placeholder=\"0.00\"\n                      className=\"pl-10\"\n                      error={!!errors.unit_cost}\n                    />\n                  </div>\n                  {errors.unit_cost && (\n                    <p className=\"text-sm text-red-500\">{errors.unit_cost.message}</p>\n                  )}\n                </div>\n              </div>\n\n              {/* Stock Summary */}\n              <div className=\"p-4 bg-muted/30 rounded-lg\">\n                <h4 className=\"font-medium mb-2\">Stock Summary</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-muted-foreground\">Total Value:</span>\n                    <p className=\"font-medium\">\n                      ${((currentStock || 0) * (unitCost || 0)).toFixed(2)}\n                    </p>\n                  </div>\n                  <div>\n                    <span className=\"text-muted-foreground\">Stock Status:</span>\n                    <p className={`font-medium ${\n                      (currentStock || 0) <= 0 ? 'text-red-600' :\n                      (currentStock || 0) <= (watch('min_stock_level') || 0) ? 'text-orange-600' :\n                      'text-green-600'\n                    }`}>\n                      {(currentStock || 0) <= 0 ? 'Out of Stock' :\n                       (currentStock || 0) <= (watch('min_stock_level') || 0) ? 'Low Stock' :\n                       'In Stock'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={loading}>\n              {loading ? (\n                <div className=\"flex items-center space-x-2\">\n                  <LoadingSpinner size=\"sm\" />\n                  <span>{isEditing ? 'Updating...' : 'Creating...'}</span>\n                </div>\n              ) : (\n                isEditing ? 'Update Item' : 'Create Item'\n              )}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAQA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAtCA;;;;;;;;;;;;;;AAwCA,MAAM,kBAAkB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,KAAK;IAC1D,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAe;QAAW;QAAS;QAAY;KAAQ,EAAE;QACpF,gBAAgB;IAClB;IACA,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,KAAK,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACxB,eAAe,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC/B;AASA,MAAM,kBAAkB;IACtB;QAAE,OAAO;QAAW,OAAO;QAAW,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACrD;QAAE,OAAO;QAAe,OAAO;QAAe,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAC7D;QAAE,OAAO;QAAW,OAAO;QAAoB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAC9D;QAAE,OAAO;QAAS,OAAO;QAAqB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAC7D;QAAE,OAAO;QAAY,OAAO;QAAY,MAAM,wMAAA,CAAA,UAAO;IAAC;IACtD;QAAE,OAAO;QAAS,OAAO;QAAS,MAAM,gMAAA,CAAA,MAAG;IAAC;CAC7C;AAEM,SAAS,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAsB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,YAAY,CAAC,CAAC;IAEpB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAqB;QAC7B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe,OAAO;YACpB,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW,IAAI;YACjC,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK,IAAI;YACrB,KAAK,KAAK,GAAG,IAAI;YACjB,eAAe,KAAK,aAAa;YACjC,iBAAiB,KAAK,eAAe;YACrC,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ,IAAI;QAC7B,IAAI;YACF,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,KAAK;YACL,eAAe;YACf,iBAAiB;YACjB,WAAW;YACX,UAAU;QACZ;IACF;IAEA,MAAM,mBAAmB,MAAM;IAC/B,MAAM,eAAe,MAAM;IAC3B,MAAM,WAAW,MAAM;IAEvB,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,WAAW;YAEX,MAAM,gBAAgB;gBACpB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,IAAI;gBACjC,OAAO,KAAK,KAAK,IAAI;gBACrB,KAAK,KAAK,GAAG,IAAI;gBACjB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,gBAAgB,YAAY,KAAK,cAAc,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1F;YAEA,IAAI;YACJ,IAAI,WAAW;gBACb,SAAS,MAAM,sHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;YAC9C,OAAO;gBACL,SAAS,MAAM,sHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,MAAM,CAAC;YACrC;YAEA,IAAI,OAAO,KAAK,EAAE;gBAChB,QAAQ,KAAK,CAAC,gCAAgC,OAAO,KAAK;gBAC1D,MAAM;gBACN;YACF;YAEA,UAAU,OAAO,IAAI;YACrB,aAAa;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACzD,OAAO,uBAAS,8OAAC,OAAO,IAAI;YAAC,WAAU;;;;;iCAAe,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC3E;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAM,YAAY,wBAAwB;;;;;;;;;;;;sCAE7C,8OAAC,kIAAA,CAAA,oBAAiB;sCACf,YACG,uDACA;;;;;;;;;;;;8BAKR,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIxC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAAsB;;;;;;8DAGtD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,OAAO;oDACpB,aAAY;oDACZ,OAAO,CAAC,CAAC,OAAO,IAAI;;;;;;gDAErB,OAAO,IAAI,kBACV,8OAAC;oDAAE,WAAU;8DAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sDAI5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAAsB;;;;;;sEAG1D,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO;4DACP,eAAe,CAAC,QAAU,SAAS,YAAY;;8EAE/C,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,8OAAC,kIAAA,CAAA,gBAAa;8EACX,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,kIAAA,CAAA,aAAU;4EAAoB,OAAO,OAAO,KAAK;sFAChD,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,OAAO,IAAI;wFAAC,WAAU;;;;;;kGACvB,8OAAC;kGAAM,OAAO,KAAK;;;;;;;;;;;;2EAHN,OAAO,KAAK;;;;;;;;;;;;;;;;wDASlC,OAAO,QAAQ,kBACd,8OAAC;4DAAE,WAAU;sEAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8DAIhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAAsB;;;;;;sEAGvD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACF,GAAG,SAAS,QAAQ;oEACrB,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAM,WAAU;sEAAsB;;;;;;sEAGrD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACF,GAAG,SAAS,MAAM;oEACnB,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAAsB;;;;;;sEAG1D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACF,GAAG,SAAS,WAAW;oEACxB,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAsB;;;;;;8DAG7D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DACC,IAAG;4DACF,GAAG,SAAS,cAAc;4DAC3B,aAAY;4DACZ,WAAU;4DACV,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAgB,WAAU;sEAAsB;;;;;;sEAG/D,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACJ,GAAG,SAAS,iBAAiB;gEAAE,eAAe;4DAAK,EAAE;4DACtD,aAAY;4DACZ,OAAO,CAAC,CAAC,OAAO,aAAa;;;;;;wDAE9B,OAAO,aAAa,kBACnB,8OAAC;4DAAE,WAAU;sEAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;8DAIrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAkB,WAAU;sEAAsB;;;;;;sEAGjE,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACJ,GAAG,SAAS,mBAAmB;gEAAE,eAAe;4DAAK,EAAE;4DACxD,aAAY;4DACZ,OAAO,CAAC,CAAC,OAAO,eAAe;;;;;;wDAEhC,OAAO,eAAe,kBACrB,8OAAC;4DAAE,WAAU;sEAAwB,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;8DAIvE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAAsB;;;;;;sEAG3D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;8EACtB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,MAAK;oEACJ,GAAG,SAAS,aAAa;wEAAE,eAAe;oEAAK,EAAE;oEAClD,aAAY;oEACZ,WAAU;oEACV,OAAO,CAAC,CAAC,OAAO,SAAS;;;;;;;;;;;;wDAG5B,OAAO,SAAS,kBACf,8OAAC;4DAAE,WAAU;sEAAwB,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;;sDAMnE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAE,WAAU;;wEAAc;wEACvB,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;sEAGtD,8OAAC;;8EACC,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAE,WAAW,CAAC,YAAY,EACzB,CAAC,gBAAgB,CAAC,KAAK,IAAI,iBAC3B,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,sBAAsB,CAAC,IAAI,oBACzD,kBACA;8EACC,CAAC,gBAAgB,CAAC,KAAK,IAAI,iBAC3B,CAAC,gBAAgB,CAAC,KAAK,CAAC,MAAM,sBAAsB,CAAC,IAAI,cACzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQb,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,wBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,mIAAA,CAAA,iBAAc;gDAAC,MAAK;;;;;;0DACrB,8OAAC;0DAAM,YAAY,gBAAgB;;;;;;;;;;;+CAGrC,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 2728, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        purple:\n          \"border-transparent bg-purple-500 text-white hover:bg-purple-600\",\n        pink:\n          \"border-transparent bg-pink-500 text-white hover:bg-pink-600\",\n        indigo:\n          \"border-transparent bg-indigo-500 text-white hover:bg-indigo-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,QACE;YACF,MACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 2776, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/empty-state.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"./button\"\nimport { Card, CardContent } from \"./card\"\n\ninterface EmptyStateProps {\n  icon?: React.ReactNode\n  title: string\n  description?: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n  className?: string\n}\n\nexport function EmptyState({\n  icon,\n  title,\n  description,\n  action,\n  className\n}: EmptyStateProps) {\n  return (\n    <Card className={cn(\"shadow-elegant border-dashed border-2\", className)}>\n      <CardContent className=\"flex flex-col items-center justify-center py-12 px-6 text-center\">\n        {icon && (\n          <div className=\"mb-4 p-3 rounded-full bg-muted/50\">\n            {icon}\n          </div>\n        )}\n        \n        <h3 className=\"text-lg font-semibold text-foreground mb-2\">\n          {title}\n        </h3>\n        \n        {description && (\n          <p className=\"text-muted-foreground text-sm mb-6 max-w-sm\">\n            {description}\n          </p>\n        )}\n        \n        {action && (\n          <Button onClick={action.onClick} className=\"mt-2\">\n            {action.label}\n          </Button>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n\nexport function EmptyTableState({\n  icon,\n  title,\n  description,\n  action,\n  className\n}: EmptyStateProps) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center py-16 px-6 text-center\", className)}>\n      {icon && (\n        <div className=\"mb-4 p-4 rounded-full bg-muted/30\">\n          {icon}\n        </div>\n      )}\n      \n      <h3 className=\"text-xl font-semibold text-foreground mb-2\">\n        {title}\n      </h3>\n      \n      {description && (\n        <p className=\"text-muted-foreground mb-6 max-w-md\">\n          {description}\n        </p>\n      )}\n      \n      {action && (\n        <Button onClick={action.onClick} size=\"lg\">\n          {action.label}\n        </Button>\n      )}\n    </div>\n  )\n}\n\nexport function EmptySearchState({\n  searchTerm,\n  onClear,\n  className\n}: {\n  searchTerm: string\n  onClear?: () => void\n  className?: string\n}) {\n  return (\n    <div className={cn(\"flex flex-col items-center justify-center py-12 px-6 text-center\", className)}>\n      <div className=\"mb-4 p-4 rounded-full bg-muted/30\">\n        <svg\n          className=\"h-8 w-8 text-muted-foreground\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n          />\n        </svg>\n      </div>\n      \n      <h3 className=\"text-lg font-semibold text-foreground mb-2\">\n        No results found\n      </h3>\n      \n      <p className=\"text-muted-foreground mb-4\">\n        No results found for <span className=\"font-medium\">\"{searchTerm}\"</span>\n      </p>\n      \n      {onClear && (\n        <Button variant=\"outline\" onClick={onClear}>\n          Clear search\n        </Button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAaO,SAAS,WAAW,EACzB,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,SAAS,EACO;IAChB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;kBAC3D,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;gBACpB,sBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,8OAAC;oBAAG,WAAU;8BACX;;;;;;gBAGF,6BACC,8OAAC;oBAAE,WAAU;8BACV;;;;;;gBAIJ,wBACC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS,OAAO,OAAO;oBAAE,WAAU;8BACxC,OAAO,KAAK;;;;;;;;;;;;;;;;;AAMzB;AAEO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,KAAK,EACL,WAAW,EACX,MAAM,EACN,SAAS,EACO;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;;YACpF,sBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAGF,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,wBACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,SAAS,OAAO,OAAO;gBAAE,MAAK;0BACnC,OAAO,KAAK;;;;;;;;;;;;AAKvB;AAEO,SAAS,iBAAiB,EAC/B,UAAU,EACV,OAAO,EACP,SAAS,EAKV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;;0BACrF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;0BAKR,8OAAC;gBAAG,WAAU;0BAA6C;;;;;;0BAI3D,8OAAC;gBAAE,WAAU;;oBAA6B;kCACnB,8OAAC;wBAAK,WAAU;;4BAAc;4BAAE;4BAAW;;;;;;;;;;;;;YAGjE,yBACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,SAAS;0BAAS;;;;;;;;;;;;AAMpD", "debugId": null}}, {"offset": {"line": 2967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/inventory/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { PageHeader, PageHeaderActions } from '@/components/layout/page-header'\nimport { InventoryForm } from '@/components/inventory/inventory-form'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { EmptyTableState } from '@/components/ui/empty-state'\nimport { Loading } from '@/components/ui/loading'\nimport { \n  Package, \n  Plus, \n  Search, \n  Filter, \n  AlertTriangle,\n  TrendingDown,\n  TrendingUp,\n  Edit,\n  Trash2,\n  Eye,\n  ShoppingCart,\n  Droplets,\n  Scissors,\n  Sparkles\n} from 'lucide-react'\nimport { InventoryItem } from '@/types'\nimport { db } from '@/lib/supabase'\nimport { formatCurrency, formatDate } from '@/lib/utils'\n\nexport default function InventoryPage() {\n  const [inventory, setInventory] = useState<InventoryItem[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filteredInventory, setFilteredInventory] = useState<InventoryItem[]>([])\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  const [showForm, setShowForm] = useState(false)\n  const [editingItem, setEditingItem] = useState<InventoryItem | undefined>()\n\n  useEffect(() => {\n    loadInventory()\n  }, [])\n\n  useEffect(() => {\n    // Filter inventory based on search term and category\n    let filtered = inventory\n\n    if (searchTerm.trim() !== '') {\n      filtered = filtered.filter(item =>\n        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.brand?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        item.category.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    if (selectedCategory !== 'all') {\n      filtered = filtered.filter(item => item.category === selectedCategory)\n    }\n\n    setFilteredInventory(filtered)\n  }, [inventory, searchTerm, selectedCategory])\n\n  const loadInventory = async () => {\n    try {\n      setLoading(true)\n      const { data, error } = await db.inventory.getAll()\n      \n      if (error) {\n        console.error('Error loading inventory:', error)\n        // For demo purposes, use mock data if Supabase is not configured\n        setInventory(mockInventory)\n      } else {\n        setInventory(data || [])\n      }\n    } catch (error) {\n      console.error('Error loading inventory:', error)\n      // Use mock data as fallback\n      setInventory(mockInventory)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDeleteItem = async (itemId: string) => {\n    if (confirm('Are you sure you want to delete this inventory item?')) {\n      try {\n        const { error } = await db.inventory.delete(itemId)\n        if (error) {\n          console.error('Error deleting item:', error)\n        } else {\n          setInventory(inventory.filter(i => i.id !== itemId))\n        }\n      } catch (error) {\n        console.error('Error deleting item:', error)\n      }\n    }\n  }\n\n  const handleAddItem = () => {\n    setEditingItem(undefined)\n    setShowForm(true)\n  }\n\n  const handleEditItem = (item: InventoryItem) => {\n    setEditingItem(item)\n    setShowForm(true)\n  }\n\n  const handleFormSuccess = (item: InventoryItem) => {\n    if (editingItem) {\n      // Update existing item\n      setInventory(inventory.map(i => i.id === item.id ? item : i))\n    } else {\n      // Add new item\n      setInventory([item, ...inventory])\n    }\n  }\n\n  const handleRestock = (item: InventoryItem) => {\n    console.log('Restock item:', item.id)\n  }\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'shampoo':\n      case 'conditioner':\n        return <Droplets className=\"h-4 w-4\" />\n      case 'styling':\n        return <Sparkles className=\"h-4 w-4\" />\n      case 'tools':\n        return <Scissors className=\"h-4 w-4\" />\n      case 'supplies':\n        return <Package className=\"h-4 w-4\" />\n      default:\n        return <Package className=\"h-4 w-4\" />\n    }\n  }\n\n  const getCategoryBadgeVariant = (category: string) => {\n    switch (category) {\n      case 'shampoo':\n        return 'info'\n      case 'conditioner':\n        return 'purple'\n      case 'styling':\n        return 'warning'\n      case 'tools':\n        return 'destructive'\n      case 'supplies':\n        return 'secondary'\n      default:\n        return 'secondary'\n    }\n  }\n\n  const getStockStatus = (item: InventoryItem) => {\n    if (item.current_stock <= 0) return 'out_of_stock'\n    if (item.current_stock <= item.min_stock_level) return 'low_stock'\n    return 'in_stock'\n  }\n\n  const getStockBadgeVariant = (status: string) => {\n    switch (status) {\n      case 'out_of_stock':\n        return 'destructive'\n      case 'low_stock':\n        return 'warning'\n      default:\n        return 'success'\n    }\n  }\n\n  const categories = [\n    { value: 'all', label: 'All Categories' },\n    { value: 'shampoo', label: 'Shampoo' },\n    { value: 'conditioner', label: 'Conditioner' },\n    { value: 'styling', label: 'Styling' },\n    { value: 'tools', label: 'Tools' },\n    { value: 'supplies', label: 'Supplies' },\n    { value: 'other', label: 'Other' }\n  ]\n\n  const lowStockItems = inventory.filter(item => getStockStatus(item) === 'low_stock')\n  const outOfStockItems = inventory.filter(item => getStockStatus(item) === 'out_of_stock')\n  const totalValue = inventory.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0)\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n        <PageHeader\n          title=\"库存管理\"\n          description=\"跟踪库存水平、管理用品和监控库存成本\"\n          icon={<Package className=\"h-6 w-6 text-white\" />}\n          actions={\n            <PageHeaderActions>\n              <Button variant=\"outline\">\n                <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                订购用品\n              </Button>\n              <Button onClick={handleAddItem}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                添加物品\n              </Button>\n            </PageHeaderActions>\n          }\n        />\n\n        <div className=\"space-y-6\">\n          {/* Search and Filters */}\n          <Card className=\"shadow-elegant\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"relative flex-1\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                  <Input\n                    placeholder=\"按名称、品牌或SKU搜索...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n                <div className=\"flex gap-2\">\n                  {categories.map((category) => (\n                    <Button\n                      key={category.value}\n                      variant={selectedCategory === category.value ? \"default\" : \"outline\"}\n                      size=\"sm\"\n                      onClick={() => setSelectedCategory(category.value)}\n                    >\n                      {category.label}\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Inventory Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">Total Items</p>\n                    <p className=\"text-2xl font-bold\">{inventory.length}</p>\n                  </div>\n                  <Package className=\"h-8 w-8 text-blue-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">Low Stock</p>\n                    <p className=\"text-2xl font-bold text-orange-600\">{lowStockItems.length}</p>\n                  </div>\n                  <AlertTriangle className=\"h-8 w-8 text-orange-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">Out of Stock</p>\n                    <p className=\"text-2xl font-bold text-red-600\">{outOfStockItems.length}</p>\n                  </div>\n                  <TrendingDown className=\"h-8 w-8 text-red-500\" />\n                </div>\n              </CardContent>\n            </Card>\n            \n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-muted-foreground\">Total Value</p>\n                    <p className=\"text-2xl font-bold\">{formatCurrency(totalValue)}</p>\n                  </div>\n                  <TrendingUp className=\"h-8 w-8 text-green-500\" />\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Low Stock Alert */}\n          {lowStockItems.length > 0 && (\n            <Card className=\"shadow-elegant border-orange-200 bg-orange-50 dark:bg-orange-950/30\">\n              <CardHeader className=\"pb-3\">\n                <CardTitle className=\"flex items-center text-orange-700 dark:text-orange-300\">\n                  <AlertTriangle className=\"h-5 w-5 mr-2\" />\n                  Low Stock Alert\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-sm text-orange-600 dark:text-orange-400 mb-3\">\n                  {lowStockItems.length} item(s) are running low and need restocking:\n                </p>\n                <div className=\"flex flex-wrap gap-2\">\n                  {lowStockItems.slice(0, 5).map((item) => (\n                    <Badge key={item.id} variant=\"warning\" className=\"text-xs\">\n                      {item.name} ({item.current_stock} left)\n                    </Badge>\n                  ))}\n                  {lowStockItems.length > 5 && (\n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      +{lowStockItems.length - 5} more\n                    </Badge>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Inventory List */}\n          <Card className=\"shadow-elegant\">\n            <CardHeader>\n              <CardTitle>Inventory Items</CardTitle>\n            </CardHeader>\n            <CardContent>\n              {loading ? (\n                <Loading text=\"Loading inventory...\" />\n              ) : filteredInventory.length === 0 ? (\n                searchTerm || selectedCategory !== 'all' ? (\n                  <EmptyTableState\n                    icon={<Search className=\"h-8 w-8 text-muted-foreground\" />}\n                    title=\"No items found\"\n                    description=\"No inventory items match your current filters\"\n                    action={{\n                      label: \"Clear filters\",\n                      onClick: () => {\n                        setSearchTerm('')\n                        setSelectedCategory('all')\n                      }\n                    }}\n                  />\n                ) : (\n                  <EmptyTableState\n                    icon={<Package className=\"h-8 w-8 text-muted-foreground\" />}\n                    title=\"No inventory items\"\n                    description=\"Start managing your inventory by adding your first item\"\n                    action={{\n                      label: \"Add Item\",\n                      onClick: handleAddItem\n                    }}\n                  />\n                )\n              ) : (\n                <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n                  {filteredInventory.map((item) => (\n                    <InventoryCard\n                      key={item.id}\n                      item={item}\n                      onEdit={() => handleEditItem(item)}\n                      onDelete={handleDeleteItem}\n                      onRestock={handleRestock}\n                      getCategoryIcon={getCategoryIcon}\n                      getCategoryBadgeVariant={getCategoryBadgeVariant}\n                      getStockStatus={getStockStatus}\n                      getStockBadgeVariant={getStockBadgeVariant}\n                    />\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Inventory Form Dialog */}\n        <InventoryForm\n          item={editingItem}\n          open={showForm}\n          onOpenChange={setShowForm}\n          onSuccess={handleFormSuccess}\n        />\n      </MainLayout>\n    </ProtectedRoute>\n  )\n}\n\ninterface InventoryCardProps {\n  item: InventoryItem\n  onEdit: () => void\n  onDelete: (id: string) => void\n  onRestock: (item: InventoryItem) => void\n  getCategoryIcon: (category: string) => React.ReactNode\n  getCategoryBadgeVariant: (category: string) => any\n  getStockStatus: (item: InventoryItem) => string\n  getStockBadgeVariant: (status: string) => any\n}\n\nfunction InventoryCard({ \n  item, \n  onEdit, \n  onDelete, \n  onRestock,\n  getCategoryIcon, \n  getCategoryBadgeVariant,\n  getStockStatus,\n  getStockBadgeVariant\n}: InventoryCardProps) {\n  const stockStatus = getStockStatus(item)\n  \n  return (\n    <div className=\"p-4 border rounded-lg hover:shadow-elegant transition-all duration-200 bg-card\">\n      <div className=\"space-y-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <h3 className=\"font-semibold text-foreground\">{item.name}</h3>\n              <Badge variant={getStockBadgeVariant(stockStatus)} className=\"text-xs\">\n                {stockStatus === 'out_of_stock' ? 'Out of Stock' :\n                 stockStatus === 'low_stock' ? 'Low Stock' : 'In Stock'}\n              </Badge>\n            </div>\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Badge variant={getCategoryBadgeVariant(item.category)} className=\"flex items-center space-x-1\">\n                {getCategoryIcon(item.category)}\n                <span className=\"capitalize\">{item.category}</span>\n              </Badge>\n              {item.brand && (\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  {item.brand}\n                </Badge>\n              )}\n            </div>\n            {item.sku && (\n              <p className=\"text-xs text-muted-foreground\">SKU: {item.sku}</p>\n            )}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <span className=\"text-muted-foreground\">Current Stock:</span>\n            <p className=\"font-medium\">{item.current_stock}</p>\n          </div>\n          <div>\n            <span className=\"text-muted-foreground\">Min Level:</span>\n            <p className=\"font-medium\">{item.min_stock_level}</p>\n          </div>\n          <div>\n            <span className=\"text-muted-foreground\">Unit Cost:</span>\n            <p className=\"font-medium\">{formatCurrency(item.unit_cost)}</p>\n          </div>\n          <div>\n            <span className=\"text-muted-foreground\">Total Value:</span>\n            <p className=\"font-medium\">{formatCurrency(item.current_stock * item.unit_cost)}</p>\n          </div>\n        </div>\n\n        {item.last_restocked && (\n          <div className=\"text-xs text-muted-foreground\">\n            Last restocked: {formatDate(item.last_restocked)}\n          </div>\n        )}\n\n        <div className=\"flex items-center justify-between pt-2 border-t\">\n          {stockStatus !== 'in_stock' && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => onRestock(item)}\n              className=\"flex items-center space-x-1\"\n            >\n              <ShoppingCart className=\"h-3 w-3\" />\n              <span>Restock</span>\n            </Button>\n          )}\n          \n          <div className=\"flex space-x-1 ml-auto\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onEdit}\n            >\n              <Edit className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => onDelete(item.id)}\n            >\n              <Trash2 className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Mock data for demonstration\nconst mockInventory: InventoryItem[] = [\n  {\n    id: '1',\n    name: 'Professional Shampoo',\n    description: 'Moisturizing shampoo for all hair types',\n    category: 'shampoo',\n    brand: 'SalonPro',\n    sku: 'SP-SHAM-001',\n    current_stock: 15,\n    min_stock_level: 5,\n    unit_cost: 12.50,\n    supplier: 'Beauty Supply Co',\n    last_restocked: '2024-12-01',\n    created_at: '2024-01-01T10:00:00Z',\n    updated_at: '2024-12-01T10:00:00Z'\n  },\n  {\n    id: '2',\n    name: 'Hair Gel',\n    description: 'Strong hold styling gel',\n    category: 'styling',\n    brand: 'StyleMax',\n    sku: 'SM-GEL-001',\n    current_stock: 3,\n    min_stock_level: 8,\n    unit_cost: 8.50,\n    supplier: 'Style Products Inc',\n    last_restocked: '2024-11-15',\n    created_at: '2024-01-01T10:00:00Z',\n    updated_at: '2024-11-15T10:00:00Z'\n  },\n  {\n    id: '3',\n    name: 'Professional Scissors',\n    description: 'High-quality cutting scissors',\n    category: 'tools',\n    brand: 'CutMaster',\n    sku: 'CM-SCIS-001',\n    current_stock: 0,\n    min_stock_level: 2,\n    unit_cost: 85.00,\n    supplier: 'Professional Tools Ltd',\n    last_restocked: '2024-10-01',\n    created_at: '2024-01-01T10:00:00Z',\n    updated_at: '2024-10-01T10:00:00Z'\n  },\n  {\n    id: '4',\n    name: 'Disposable Razors',\n    description: 'Single-use safety razors',\n    category: 'supplies',\n    brand: 'SafeShave',\n    sku: 'SS-RAZ-001',\n    current_stock: 50,\n    min_stock_level: 20,\n    unit_cost: 0.75,\n    supplier: 'Barber Supplies Co',\n    last_restocked: '2024-12-10',\n    created_at: '2024-01-01T10:00:00Z',\n    updated_at: '2024-12-10T10:00:00Z'\n  }\n]\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AA/BA;;;;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC9E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qDAAqD;QACrD,IAAI,WAAW;QAEf,IAAI,WAAW,IAAI,OAAO,IAAI;YAC5B,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW,OACzD,KAAK,GAAG,EAAE,cAAc,SAAS,WAAW,WAAW,OACvD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE/D;QAEA,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACvD;QAEA,qBAAqB;IACvB,GAAG;QAAC;QAAW;QAAY;KAAiB;IAE5C,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,MAAM;YAEjD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,iEAAiE;gBACjE,aAAa;YACf,OAAO;gBACL,aAAa,QAAQ,EAAE;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,4BAA4B;YAC5B,aAAa;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,yDAAyD;YACnE,IAAI;gBACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,wBAAwB;gBACxC,OAAO;oBACL,aAAa,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,aAAa;YACf,uBAAuB;YACvB,aAAa,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG,OAAO;QAC5D,OAAO;YACL,eAAe;YACf,aAAa;gBAAC;mBAAS;aAAU;QACnC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,iBAAiB,KAAK,EAAE;IACtC;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,aAAa,IAAI,GAAG,OAAO;QACpC,IAAI,KAAK,aAAa,IAAI,KAAK,eAAe,EAAE,OAAO;QACvD,OAAO;IACT;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,OAAO;YAAO,OAAO;QAAiB;QACxC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAS,OAAO;QAAQ;KAClC;IAED,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAA,OAAQ,eAAe,UAAU;IACxE,MAAM,kBAAkB,UAAU,MAAM,CAAC,CAAA,OAAQ,eAAe,UAAU;IAC1E,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,aAAa,GAAG,KAAK,SAAS,EAAG;IAEhG,qBACE,8OAAC,gJAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,8IAAA,CAAA,aAAU;;8BACT,8OAAC,8IAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;oBACZ,oBAAM,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBACzB,uBACE,8OAAC,8IAAA,CAAA,oBAAiB;;0CAChB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG3C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,qBAAqB,SAAS,KAAK,GAAG,YAAY;oDAC3D,MAAK;oDACL,SAAS,IAAM,oBAAoB,SAAS,KAAK;8DAEhD,SAAS,KAAK;mDALV,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;sCAc/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAsB,UAAU,MAAM;;;;;;;;;;;;8DAErD,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKzB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAsC,cAAc,MAAM;;;;;;;;;;;;8DAEzE,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK/B,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAmC,gBAAgB,MAAM;;;;;;;;;;;;8DAExE,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK9B,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAsB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;8DAEpD,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAO7B,cAAc,MAAM,GAAG,mBACtB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI9C,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAE,WAAU;;gDACV,cAAc,MAAM;gDAAC;;;;;;;sDAExB,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC9B,8OAAC,iIAAA,CAAA,QAAK;wDAAe,SAAQ;wDAAU,WAAU;;4DAC9C,KAAK,IAAI;4DAAC;4DAAG,KAAK,aAAa;4DAAC;;uDADvB,KAAK,EAAE;;;;;gDAIpB,cAAc,MAAM,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;wDAAU;wDACzC,cAAc,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCASvC,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;8CACT,wBACC,8OAAC,mIAAA,CAAA,UAAO;wCAAC,MAAK;;;;;+CACZ,kBAAkB,MAAM,KAAK,IAC/B,cAAc,qBAAqB,sBACjC,8OAAC,0IAAA,CAAA,kBAAe;wCACd,oBAAM,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACxB,OAAM;wCACN,aAAY;wCACZ,QAAQ;4CACN,OAAO;4CACP,SAAS;gDACP,cAAc;gDACd,oBAAoB;4CACtB;wCACF;;;;;6DAGF,8OAAC,0IAAA,CAAA,kBAAe;wCACd,oBAAM,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCACzB,OAAM;wCACN,aAAY;wCACZ,QAAQ;4CACN,OAAO;4CACP,SAAS;wCACX;;;;;6DAIJ,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,qBACtB,8OAAC;gDAEC,MAAM;gDACN,QAAQ,IAAM,eAAe;gDAC7B,UAAU;gDACV,WAAW;gDACX,iBAAiB;gDACjB,yBAAyB;gDACzB,gBAAgB;gDAChB,sBAAsB;+CARjB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAkB1B,8OAAC,oJAAA,CAAA,gBAAa;oBACZ,MAAM;oBACN,MAAM;oBACN,cAAc;oBACd,WAAW;;;;;;;;;;;;;;;;;AAKrB;AAaA,SAAS,cAAc,EACrB,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,uBAAuB,EACvB,cAAc,EACd,oBAAoB,EACD;IACnB,MAAM,cAAc,eAAe;IAEnC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiC,KAAK,IAAI;;;;;;kDACxD,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAS,qBAAqB;wCAAc,WAAU;kDAC1D,gBAAgB,iBAAiB,iBACjC,gBAAgB,cAAc,cAAc;;;;;;;;;;;;0CAGjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAS,wBAAwB,KAAK,QAAQ;wCAAG,WAAU;;4CAC/D,gBAAgB,KAAK,QAAQ;0DAC9B,8OAAC;gDAAK,WAAU;0DAAc,KAAK,QAAQ;;;;;;;;;;;;oCAE5C,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAChC,KAAK,KAAK;;;;;;;;;;;;4BAIhB,KAAK,GAAG,kBACP,8OAAC;gCAAE,WAAU;;oCAAgC;oCAAM,KAAK,GAAG;;;;;;;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAe,KAAK,aAAa;;;;;;;;;;;;sCAEhD,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAe,KAAK,eAAe;;;;;;;;;;;;sCAElD,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;sCAE3D,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAe,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa,GAAG,KAAK,SAAS;;;;;;;;;;;;;;;;;;gBAIjF,KAAK,cAAc,kBAClB,8OAAC;oBAAI,WAAU;;wBAAgC;wBAC5B,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,cAAc;;;;;;;8BAInD,8OAAC;oBAAI,WAAU;;wBACZ,gBAAgB,4BACf,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,UAAU;4BACzB,WAAU;;8CAEV,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;8CAAK;;;;;;;;;;;;sCAIV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;8CAET,cAAA,8OAAC,2MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,SAAS,KAAK,EAAE;8CAE/B,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC;AAEA,8BAA8B;AAC9B,MAAM,gBAAiC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,KAAK;QACL,eAAe;QACf,iBAAiB;QACjB,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,KAAK;QACL,eAAe;QACf,iBAAiB;QACjB,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,KAAK;QACL,eAAe;QACf,iBAAiB;QACjB,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,KAAK;QACL,eAAe;QACf,iBAAiB;QACjB,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,YAAY;QACZ,YAAY;IACd;CACD", "debugId": null}}]}