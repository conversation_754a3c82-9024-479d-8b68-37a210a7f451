{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d)\n}\n\nexport function formatDateTime(date: string | Date): string {\n  const d = typeof date === 'string' ? new Date(date) : date\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d)\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const date = new Date()\n  date.setHours(parseInt(hours), parseInt(minutes))\n  return new Intl.DateTimeFormat('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n  }).format(date)\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,CAAC,SAAS,QAAQ,SAAS;IACxC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { \n  Calendar, \n  Users, \n  Scissors, \n  UserCheck, \n  Package, \n  BarChart3, \n  Settings,\n  Home\n} from 'lucide-react'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: Home },\n  { name: 'Appointments', href: '/appointments', icon: Calendar },\n  { name: 'Customers', href: '/customers', icon: Users },\n  { name: 'Staff', href: '/staff', icon: UserCheck },\n  { name: 'Services', href: '/services', icon: Scissors },\n  { name: 'Inventory', href: '/inventory', icon: Package },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Settings', href: '/settings', icon: Settings },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"flex flex-col space-y-2\">\n      {navigation.map((item, index) => {\n        const isActive = pathname === item.href\n        return (\n          <Link\n            key={item.name}\n            href={item.href}\n            className={cn(\n              'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 animate-fade-in',\n              isActive\n                ? 'bg-white text-primary shadow-elegant-lg border border-primary/20'\n                : 'text-muted-foreground hover:text-primary hover:bg-white/50 hover:shadow-elegant'\n            )}\n            style={{ animationDelay: `${index * 50}ms` }}\n          >\n            <item.icon className={cn(\n              \"mr-3 h-5 w-5 transition-all duration-200\",\n              isActive\n                ? \"text-primary scale-110\"\n                : \"text-muted-foreground group-hover:text-primary group-hover:scale-105\"\n            )} />\n            <span className=\"font-medium\">{item.name}</span>\n            {isActive && (\n              <div className=\"ml-auto h-2 w-2 rounded-full bg-primary animate-pulse\"></div>\n            )}\n          </Link>\n        )\n      })}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAgBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC3C;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAC9D;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,uMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,mNAAA,CAAA,YAAS;IAAC;IACjD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACtD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,2MAAA,CAAA,UAAO;IAAC;IACvD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACvD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,MAAM;YACrB,MAAM,WAAW,aAAa,KAAK,IAAI;YACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gBAEH,MAAM,KAAK,IAAI;gBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gHACA,WACI,qEACA;gBAEN,OAAO;oBAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;gBAAC;;kCAE3C,6LAAC,KAAK,IAAI;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,4CACA,WACI,2BACA;;;;;;kCAEN,6LAAC;wBAAK,WAAU;kCAAe,KAAK,IAAI;;;;;;oBACvC,0BACC,6LAAC;wBAAI,WAAU;;;;;;;eAlBZ,KAAK,IAAI;;;;;QAsBpB;;;;;;AAGN;GAlCgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-elegant hover:shadow-elegant-lg transform hover:scale-105 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-gradient-primary text-white hover:opacity-90\",\n        destructive:\n          \"bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700\",\n        outline:\n          \"border-2 border-primary bg-transparent text-primary hover:bg-primary hover:text-white\",\n        secondary:\n          \"bg-gradient-secondary text-secondary-foreground hover:opacity-90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground shadow-none hover:shadow-elegant\",\n        link: \"text-primary underline-offset-4 hover:underline shadow-none\",\n        success: \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\",\n        warning: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600\",\n        info: \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\",\n      },\n      size: {\n        default: \"h-11 px-6 py-2\",\n        sm: \"h-9 rounded-lg px-4 text-xs\",\n        lg: \"h-13 rounded-xl px-10 text-base\",\n        icon: \"h-11 w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uXACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Scissors, Crown, LogOut, Settings, User } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getInitials } from '@/lib/utils'\n\nexport function Sidebar() {\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const userDisplayName = user?.user_metadata?.full_name ||\n                          user?.email?.split('@')[0] ||\n                          'User'\n\n  const userInitials = getInitials(userDisplayName)\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r shadow-elegant animate-slide-in\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b bg-gradient-primary\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"relative\">\n            <Crown className=\"h-7 w-7 text-white\" />\n            <Scissors className=\"h-4 w-4 text-white absolute -bottom-1 -right-1\" />\n          </div>\n          <div>\n            <span className=\"text-lg font-bold text-white\">Royal Cuts</span>\n            <p className=\"text-xs text-white/80\">Professional Salon</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 px-4 py-6 bg-gradient-secondary\">\n        <Navigation />\n      </div>\n\n      {/* User info */}\n      <div className=\"border-t bg-card p-4\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center shadow-elegant\">\n            <span className=\"text-sm font-bold text-white\">{userInitials}</span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-semibold text-foreground truncate\">{userDisplayName}</p>\n            <p className=\"text-xs text-muted-foreground truncate\">{user?.email}</p>\n          </div>\n          <div className=\"h-2 w-2 rounded-full bg-green-500\" title=\"Online\"></div>\n        </div>\n\n        {/* User Actions */}\n        <div className=\"flex space-x-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={() => {/* TODO: Open profile settings */}}\n          >\n            <User className=\"h-3 w-3 mr-1\" />\n            Profile\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex-1 h-8 text-xs\"\n            onClick={handleSignOut}\n          >\n            <LogOut className=\"h-3 w-3 mr-1\" />\n            Sign Out\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,kBAAkB,MAAM,eAAe,aACrB,MAAM,OAAO,MAAM,IAAI,CAAC,EAAE,IAC1B;IAExB,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAEjC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;;8CACC,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;8CAC/C,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;;;;;;;;;;0BAIb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAC/D,6LAAC;wCAAE,WAAU;kDAA0C,MAAM;;;;;;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;gCAAoC,OAAM;;;;;;;;;;;;kCAI3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,KAAwC;;kDAEjD,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;;kDAET,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAvEgB;;QACY,sIAAA,CAAA,UAAO;;;KADnB", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { Sidebar } from './sidebar'\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"flex h-screen bg-background overflow-hidden\">\n      <Sidebar />\n      <main className=\"flex-1 overflow-auto bg-gradient-to-br from-background via-secondary/30 to-muted/50\">\n        <div className=\"p-8 animate-fade-in\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BACR,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;KAbgB", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-elegant hover:shadow-elegant-lg transition-all duration-300 backdrop-blur-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-4\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sIACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/auth-context'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Crown, Scissors } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requireAdmin?: boolean\n  fallback?: React.ReactNode\n}\n\nexport function ProtectedRoute({ \n  children, \n  requireAdmin = false, \n  fallback \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login')\n    }\n  }, [user, loading, router])\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"flex flex-col items-center space-y-4\">\n            <div className=\"relative animate-pulse\">\n              <Crown className=\"h-12 w-12 text-primary\" />\n              <Scissors className=\"h-8 w-8 text-accent absolute -bottom-2 -right-2\" />\n            </div>\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-semibold text-foreground mb-2\">Royal Cuts</h2>\n              <p className=\"text-muted-foreground\">Loading your dashboard...</p>\n            </div>\n            <div className=\"flex space-x-1\">\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\"></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"h-2 w-2 bg-primary rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Show unauthorized if user is not logged in\n  if (!user) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n        <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n          <CardContent className=\"text-center\">\n            <div className=\"mb-4\">\n              <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n            </div>\n            <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground mb-4\">\n              You need to be logged in to access this page.\n            </p>\n            <button\n              onClick={() => router.push('/auth/login')}\n              className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n            >\n              Go to Login\n            </button>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  // Check admin requirement\n  if (requireAdmin) {\n    const isAdmin = user?.user_metadata?.role === 'admin' || \n                    user?.email?.endsWith('@royalcuts.com') ||\n                    user?.app_metadata?.role === 'admin'\n\n    if (!isAdmin) {\n      return (\n        <div className=\"min-h-screen bg-gradient-to-br from-background via-secondary/30 to-muted/50 flex items-center justify-center\">\n          <Card className=\"shadow-elegant-lg border-0 bg-card/80 backdrop-blur-sm p-8\">\n            <CardContent className=\"text-center\">\n              <div className=\"mb-4\">\n                <Crown className=\"h-16 w-16 text-muted-foreground mx-auto\" />\n              </div>\n              <h2 className=\"text-2xl font-semibold text-foreground mb-2\">Admin Access Required</h2>\n              <p className=\"text-muted-foreground mb-4\">\n                You need administrator privileges to access this page.\n              </p>\n              <button\n                onClick={() => router.push('/')}\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Go to Dashboard\n              </button>\n            </CardContent>\n          </Card>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n\n// Higher-order component for protecting pages\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  options?: { requireAdmin?: boolean }\n) {\n  return function AuthenticatedComponent(props: P) {\n    return (\n      <ProtectedRoute requireAdmin={options?.requireAdmin}>\n        <Component {...props} />\n      </ProtectedRoute>\n    )\n  }\n}\n\n// Hook for checking authentication status\nexport function useRequireAuth(requireAdmin = false) {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push('/auth/login')\n        return\n      }\n\n      if (requireAdmin) {\n        const isAdmin = user?.user_metadata?.role === 'admin' || \n                        user?.email?.endsWith('@royalcuts.com') ||\n                        user?.app_metadata?.role === 'admin'\n        \n        if (!isAdmin) {\n          router.push('/')\n          return\n        }\n      }\n    }\n  }, [user, loading, requireAdmin, router])\n\n  return { user, loading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAcO,SAAS,eAAe,EAC7B,QAAQ,EACR,eAAe,KAAK,EACpB,QAAQ,EACY;;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAChG,6LAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5G;IAEA,6CAA6C;IAC7C,IAAI,CAAC,MAAM;QACT,OAAO,0BACL,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,0BAA0B;IAC1B,IAAI,cAAc;QAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;QAE7C,IAAI,CAAC,SAAS;YACZ,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;QAOX;IACF;IAEA,qBAAO;kBAAG;;AACZ;GAhGgB;;QAKY,sIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KANV;AAmGT,SAAS,SACd,SAAiC,EACjC,OAAoC;IAEpC,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,qBACE,6LAAC;YAAe,cAAc,SAAS;sBACrC,cAAA,6LAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAG1B;AACF;AAGO,SAAS,eAAe,eAAe,KAAK;;IACjD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,cAAc;oBAChB,MAAM,UAAU,MAAM,eAAe,SAAS,WAC9B,MAAM,OAAO,SAAS,qBACtB,MAAM,cAAc,SAAS;oBAE7C,IAAI,CAAC,SAAS;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;gBACF;YACF;QACF;mCAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,OAAO;QAAE;QAAM;IAAQ;AACzB;IAzBgB;;QACY,sIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/layout/page-header.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  icon?: React.ReactNode\n  actions?: React.ReactNode\n  breadcrumbs?: Array<{\n    label: string\n    href?: string\n  }>\n  className?: string\n}\n\nexport function PageHeader({\n  title,\n  description,\n  icon,\n  actions,\n  breadcrumbs,\n  className\n}: PageHeaderProps) {\n  return (\n    <div className={cn(\"space-y-4 mb-8\", className)}>\n      {/* Breadcrumbs */}\n      {breadcrumbs && breadcrumbs.length > 0 && (\n        <nav className=\"flex\" aria-label=\"Breadcrumb\">\n          <ol className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n            {breadcrumbs.map((crumb, index) => (\n              <li key={index} className=\"flex items-center\">\n                {index > 0 && (\n                  <svg\n                    className=\"h-4 w-4 mx-2\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path\n                      fillRule=\"evenodd\"\n                      d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                      clipRule=\"evenodd\"\n                    />\n                  </svg>\n                )}\n                {crumb.href ? (\n                  <a\n                    href={crumb.href}\n                    className=\"hover:text-foreground transition-colors\"\n                  >\n                    {crumb.label}\n                  </a>\n                ) : (\n                  <span className=\"text-foreground font-medium\">\n                    {crumb.label}\n                  </span>\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      )}\n\n      {/* Header Content */}\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex items-start space-x-4\">\n          {icon && (\n            <div className=\"h-12 w-12 rounded-xl bg-gradient-primary flex items-center justify-center shadow-elegant\">\n              {icon}\n            </div>\n          )}\n          <div>\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n              {title}\n            </h1>\n            {description && (\n              <p className=\"text-muted-foreground text-lg mt-1\">\n                {description}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {actions && (\n          <div className=\"flex items-center space-x-2\">\n            {actions}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\ninterface PageHeaderActionsProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function PageHeaderActions({ children, className }: PageHeaderActionsProps) {\n  return (\n    <div className={cn(\"flex items-center space-x-2\", className)}>\n      {children}\n    </div>\n  )\n}\n\ninterface QuickStatsProps {\n  stats: Array<{\n    label: string\n    value: string | number\n    icon?: React.ReactNode\n    trend?: {\n      value: number\n      isPositive: boolean\n    }\n  }>\n  className?: string\n}\n\nexport function QuickStats({ stats, className }: QuickStatsProps) {\n  return (\n    <div className={cn(\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\", className)}>\n      {stats.map((stat, index) => (\n        <div\n          key={index}\n          className=\"bg-card rounded-xl p-4 border shadow-elegant hover:shadow-elegant-lg transition-all duration-300\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-muted-foreground\">\n                {stat.label}\n              </p>\n              <p className=\"text-2xl font-bold text-foreground\">\n                {stat.value}\n              </p>\n              {stat.trend && (\n                <p className={cn(\n                  \"text-xs flex items-center mt-1\",\n                  stat.trend.isPositive ? \"text-green-600\" : \"text-red-600\"\n                )}>\n                  <span className=\"mr-1\">\n                    {stat.trend.isPositive ? \"↗\" : \"↘\"}\n                  </span>\n                  {Math.abs(stat.trend.value)}%\n                </p>\n              )}\n            </div>\n            {stat.icon && (\n              <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n                {stat.icon}\n              </div>\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAeO,SAAS,WAAW,EACzB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,OAAO,EACP,WAAW,EACX,SAAS,EACO;IAChB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;;YAElC,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;gBAAI,WAAU;gBAAO,cAAW;0BAC/B,cAAA,6LAAC;oBAAG,WAAU;8BACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;4BAAe,WAAU;;gCACvB,QAAQ,mBACP,6LAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;8CAER,cAAA,6LAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;gCAId,MAAM,IAAI,iBACT,6LAAC;oCACC,MAAM,MAAM,IAAI;oCAChB,WAAU;8CAET,MAAM,KAAK;;;;;yDAGd,6LAAC;oCAAK,WAAU;8CACb,MAAM,KAAK;;;;;;;2BAvBT;;;;;;;;;;;;;;;0BAiCjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,6BACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;oBAMR,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;KA3EgB;AAkFT,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C;;;;;;AAGP;MANgB;AAqBT,SAAS,WAAW,EAAE,KAAK,EAAE,SAAS,EAAmB;IAC9D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6DAA6D;kBAC7E,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gBAEC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;gCAEZ,KAAK,KAAK,kBACT,6LAAC;oCAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,kCACA,KAAK,KAAK,CAAC,UAAU,GAAG,mBAAmB;;sDAE3C,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,UAAU,GAAG,MAAM;;;;;;wCAEhC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK;wCAAE;;;;;;;;;;;;;wBAIjC,KAAK,IAAI,kBACR,6LAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI;;;;;;;;;;;;eAzBX;;;;;;;;;;AAiCf;MAtCgB", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA,SAAS,6CACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1308, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        purple:\n          \"border-transparent bg-purple-500 text-white hover:bg-purple-600\",\n        pink:\n          \"border-transparent bg-pink-500 text-white hover:bg-pink-600\",\n        indigo:\n          \"border-transparent bg-indigo-500 text-white hover:bg-indigo-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,QACE;YACF,MACE;YACF,QACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1577, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo2/src/app/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { ProtectedRoute } from '@/components/auth/protected-route'\nimport { PageHeader } from '@/components/layout/page-header'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { \n  Settings, \n  Building, \n  Clock, \n  DollarSign, \n  Bell, \n  Shield, \n  Palette,\n  Globe,\n  Mail,\n  Phone,\n  MapPin,\n  Save,\n  User,\n  Key,\n  Database,\n  Smartphone\n} from 'lucide-react'\nimport { useAuth } from '@/contexts/auth-context'\n\nexport default function SettingsPage() {\n  const { user } = useAuth()\n  const [loading, setLoading] = useState(false)\n  const [activeTab, setActiveTab] = useState('business')\n\n  // Business settings state\n  const [businessSettings, setBusinessSettings] = useState({\n    name: 'Royal Cuts Barbershop',\n    address: '123 Main Street, Downtown, NY 10001',\n    phone: '+****************',\n    email: '<EMAIL>',\n    website: 'www.royalcuts.com',\n    timezone: 'America/New_York',\n    currency: 'USD'\n  })\n\n  // Operating hours state\n  const [operatingHours, setOperatingHours] = useState({\n    monday: { open: '09:00', close: '18:00', closed: false },\n    tuesday: { open: '09:00', close: '18:00', closed: false },\n    wednesday: { open: '09:00', close: '18:00', closed: false },\n    thursday: { open: '09:00', close: '19:00', closed: false },\n    friday: { open: '09:00', close: '19:00', closed: false },\n    saturday: { open: '08:00', close: '17:00', closed: false },\n    sunday: { open: '10:00', close: '16:00', closed: false }\n  })\n\n  // Notification settings state\n  const [notifications, setNotifications] = useState({\n    emailNotifications: true,\n    smsNotifications: false,\n    appointmentReminders: true,\n    lowStockAlerts: true,\n    dailyReports: false,\n    weeklyReports: true\n  })\n\n  const handleSaveSettings = async () => {\n    setLoading(true)\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    setLoading(false)\n    alert('Settings saved successfully!')\n  }\n\n  const tabs = [\n    { id: 'business', label: 'Business Info', icon: Building },\n    { id: 'hours', label: 'Operating Hours', icon: Clock },\n    { id: 'notifications', label: 'Notifications', icon: Bell },\n    { id: 'appearance', label: 'Appearance', icon: Palette },\n    { id: 'security', label: 'Security', icon: Shield },\n    { id: 'integrations', label: 'Integrations', icon: Database }\n  ]\n\n  const renderBusinessSettings = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Building className=\"h-5 w-5 mr-2\" />\n            Business Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Business Name</label>\n              <Input\n                value={businessSettings.name}\n                onChange={(e) => setBusinessSettings({...businessSettings, name: e.target.value})}\n                placeholder=\"Royal Cuts Barbershop\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Phone Number</label>\n              <div className=\"relative\">\n                <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.phone}\n                  onChange={(e) => setBusinessSettings({...businessSettings, phone: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"+****************\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Email Address</label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.email}\n                  onChange={(e) => setBusinessSettings({...businessSettings, email: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Website</label>\n              <div className=\"relative\">\n                <Globe className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  value={businessSettings.website}\n                  onChange={(e) => setBusinessSettings({...businessSettings, website: e.target.value})}\n                  className=\"pl-10\"\n                  placeholder=\"www.royalcuts.com\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">Address</label>\n            <div className=\"relative\">\n              <MapPin className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\n              <textarea\n                value={businessSettings.address}\n                onChange={(e) => setBusinessSettings({...businessSettings, address: e.target.value})}\n                className=\"w-full pl-10 pt-2 pb-2 pr-3 border border-input rounded-md bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\"\n                rows={2}\n                placeholder=\"123 Main Street, Downtown, NY 10001\"\n              />\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Timezone</label>\n              <Select value={businessSettings.timezone} onValueChange={(value) => setBusinessSettings({...businessSettings, timezone: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"America/New_York\">Eastern Time (ET)</SelectItem>\n                  <SelectItem value=\"America/Chicago\">Central Time (CT)</SelectItem>\n                  <SelectItem value=\"America/Denver\">Mountain Time (MT)</SelectItem>\n                  <SelectItem value=\"America/Los_Angeles\">Pacific Time (PT)</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">Currency</label>\n              <Select value={businessSettings.currency} onValueChange={(value) => setBusinessSettings({...businessSettings, currency: value})}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"USD\">USD - US Dollar</SelectItem>\n                  <SelectItem value=\"EUR\">EUR - Euro</SelectItem>\n                  <SelectItem value=\"GBP\">GBP - British Pound</SelectItem>\n                  <SelectItem value=\"CAD\">CAD - Canadian Dollar</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderOperatingHours = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Clock className=\"h-5 w-5 mr-2\" />\n            Operating Hours\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {Object.entries(operatingHours).map(([day, hours]) => (\n              <div key={day} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"font-medium capitalize w-20\">{day}</span>\n                  <label className=\"flex items-center space-x-2\">\n                    <input\n                      type=\"checkbox\"\n                      checked={!hours.closed}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, closed: !e.target.checked }\n                      })}\n                      className=\"rounded\"\n                    />\n                    <span className=\"text-sm\">Open</span>\n                  </label>\n                </div>\n                {!hours.closed && (\n                  <div className=\"flex items-center space-x-2\">\n                    <Input\n                      type=\"time\"\n                      value={hours.open}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, open: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                    <span className=\"text-muted-foreground\">to</span>\n                    <Input\n                      type=\"time\"\n                      value={hours.close}\n                      onChange={(e) => setOperatingHours({\n                        ...operatingHours,\n                        [day]: { ...hours, close: e.target.value }\n                      })}\n                      className=\"w-24\"\n                    />\n                  </div>\n                )}\n                {hours.closed && (\n                  <Badge variant=\"secondary\">Closed</Badge>\n                )}\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderNotifications = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Bell className=\"h-5 w-5 mr-2\" />\n            Notification Preferences\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[\n              { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },\n              { key: 'smsNotifications', label: 'SMS Notifications', description: 'Receive notifications via text message' },\n              { key: 'appointmentReminders', label: 'Appointment Reminders', description: 'Send reminders to customers' },\n              { key: 'lowStockAlerts', label: 'Low Stock Alerts', description: 'Get notified when inventory is low' },\n              { key: 'dailyReports', label: 'Daily Reports', description: 'Receive daily business summaries' },\n              { key: 'weeklyReports', label: 'Weekly Reports', description: 'Receive weekly analytics reports' }\n            ].map((setting) => (\n              <div key={setting.key} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                <div>\n                  <p className=\"font-medium\">{setting.label}</p>\n                  <p className=\"text-sm text-muted-foreground\">{setting.description}</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={notifications[setting.key as keyof typeof notifications]}\n                    onChange={(e) => setNotifications({\n                      ...notifications,\n                      [setting.key]: e.target.checked\n                    })}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderAppearance = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Palette className=\"h-5 w-5 mr-2\" />\n            Appearance Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"p-4 border rounded-lg bg-muted/30\">\n              <p className=\"font-medium mb-2\">Theme</p>\n              <p className=\"text-sm text-muted-foreground mb-4\">Choose your preferred color scheme</p>\n              <div className=\"grid grid-cols-3 gap-3\">\n                <div className=\"p-3 border rounded-lg cursor-pointer hover:bg-accent\">\n                  <div className=\"h-8 bg-gradient-to-r from-amber-600 to-amber-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">Royal Gold (Current)</p>\n                </div>\n                <div className=\"p-3 border rounded-lg cursor-pointer hover:bg-accent opacity-50\">\n                  <div className=\"h-8 bg-gradient-to-r from-blue-600 to-blue-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">Ocean Blue</p>\n                </div>\n                <div className=\"p-3 border rounded-lg cursor-pointer hover:bg-accent opacity-50\">\n                  <div className=\"h-8 bg-gradient-to-r from-emerald-600 to-emerald-800 rounded mb-2\"></div>\n                  <p className=\"text-xs font-medium\">Forest Green</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderSecurity = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Shield className=\"h-5 w-5 mr-2\" />\n            Security Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">Two-Factor Authentication</p>\n                <p className=\"text-sm text-muted-foreground\">Add an extra layer of security to your account</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Smartphone className=\"h-4 w-4 mr-2\" />\n                Enable 2FA\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">Change Password</p>\n                <p className=\"text-sm text-muted-foreground\">Update your account password</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                <Key className=\"h-4 w-4 mr-2\" />\n                Change Password\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"font-medium\">Active Sessions</p>\n                <p className=\"text-sm text-muted-foreground\">Manage your active login sessions</p>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">\n                View Sessions\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderIntegrations = () => (\n    <div className=\"space-y-6\">\n      <Card className=\"shadow-elegant\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center\">\n            <Database className=\"h-5 w-5 mr-2\" />\n            Integrations\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"p-4 border rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center\">\n                  <Database className=\"h-5 w-5 text-green-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">Supabase Database</p>\n                  <p className=\"text-sm text-muted-foreground\">Connected and syncing</p>\n                </div>\n              </div>\n              <Badge variant=\"success\">Connected</Badge>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                  <Mail className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">Email Service</p>\n                  <p className=\"text-sm text-muted-foreground\">Send automated emails</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">Connect</Button>\n            </div>\n          </div>\n\n          <div className=\"p-4 border rounded-lg opacity-50\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n                  <Smartphone className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium\">SMS Service</p>\n                  <p className=\"text-sm text-muted-foreground\">Send text message notifications</p>\n                </div>\n              </div>\n              <Button variant=\"outline\" size=\"sm\">Connect</Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'business': return renderBusinessSettings()\n      case 'hours': return renderOperatingHours()\n      case 'notifications': return renderNotifications()\n      case 'appearance': return renderAppearance()\n      case 'security': return renderSecurity()\n      case 'integrations': return renderIntegrations()\n      default: return renderBusinessSettings()\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <MainLayout>\n        <PageHeader\n          title=\"Settings\"\n          description=\"Configure your barbershop management system\"\n          icon={<Settings className=\"h-6 w-6 text-white\" />}\n        />\n\n        <div className=\"flex flex-col lg:flex-row gap-6\">\n          {/* Settings Navigation */}\n          <div className=\"lg:w-64\">\n            <Card className=\"shadow-elegant\">\n              <CardContent className=\"p-4\">\n                <nav className=\"space-y-1\">\n                  {tabs.map((tab) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id)}\n                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                        activeTab === tab.id\n                          ? 'bg-primary text-primary-foreground'\n                          : 'hover:bg-accent hover:text-accent-foreground'\n                      }`}\n                    >\n                      <tab.icon className=\"h-4 w-4\" />\n                      <span className=\"text-sm font-medium\">{tab.label}</span>\n                    </button>\n                  ))}\n                </nav>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Settings Content */}\n          <div className=\"flex-1\">\n            {renderTabContent()}\n            \n            {/* Save Button */}\n            <div className=\"mt-6 flex justify-end\">\n              <Button onClick={handleSaveSettings} disabled={loading}>\n                {loading ? (\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                    <span>Saving...</span>\n                  </div>\n                ) : (\n                  <>\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    Save Settings\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </MainLayout>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;;;AAnCA;;;;;;;;;;;;AAqCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,0BAA0B;IAC1B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,wBAAwB;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,SAAS;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACxD,WAAW;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QAC1D,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACvD,UAAU;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;QACzD,QAAQ;YAAE,MAAM;YAAS,OAAO;YAAS,QAAQ;QAAM;IACzD;IAEA,8BAA8B;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,WAAW;QACX,MAAM;IACR;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAiB,MAAM,6MAAA,CAAA,WAAQ;QAAC;QACzD;YAAE,IAAI;YAAS,OAAO;YAAmB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACrD;YAAE,IAAI;YAAiB,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC1D;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,2MAAA,CAAA,UAAO;QAAC;QACvD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,yMAAA,CAAA,SAAM;QAAC;QAClD;YAAE,IAAI;YAAgB,OAAO;YAAgB,MAAM,6MAAA,CAAA,WAAQ;QAAC;KAC7D;IAED,MAAM,yBAAyB,kBAC7B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,OAAO,iBAAiB,IAAI;gDAC5B,UAAU,CAAC,IAAM,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC/E,aAAY;;;;;;;;;;;;kDAGhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,KAAK;wDAC7B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAChF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAIlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,iBAAiB,OAAO;wDAC/B,UAAU,CAAC,IAAM,oBAAoB;gEAAC,GAAG,gBAAgB;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAClF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDACC,OAAO,iBAAiB,OAAO;gDAC/B,UAAU,CAAC,IAAM,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAClF,WAAU;gDACV,MAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAmB;;;;;;0EACrC,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAkB;;;;;;0EACpC,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAiB;;;;;;0EACnC,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAsB;;;;;;;;;;;;;;;;;;;;;;;;kDAI9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO,iBAAiB,QAAQ;gDAAE,eAAe,CAAC,QAAU,oBAAoB;wDAAC,GAAG,gBAAgB;wDAAE,UAAU;oDAAK;;kEAC3H,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUxC,MAAM,uBAAuB,kBAC3B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAItC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC/C,6LAAC;oCAAc,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;8DAC/C,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,CAAC,MAAM,MAAM;4DACtB,UAAU,CAAC,IAAM,kBAAkB;oEACjC,GAAG,cAAc;oEACjB,CAAC,IAAI,EAAE;wEAAE,GAAG,KAAK;wEAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,OAAO;oEAAC;gEAC/C;4DACA,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;wCAG7B,CAAC,MAAM,MAAM,kBACZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,MAAM,IAAI;oDACjB,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,CAAC,IAAI,EAAE;gEAAE,GAAG,KAAK;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1C;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,MAAM,KAAK;oDAClB,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,CAAC,IAAI,EAAE;gEAAE,GAAG,KAAK;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC3C;oDACA,WAAU;;;;;;;;;;;;wCAIf,MAAM,MAAM,kBACX,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;;mCAxCrB;;;;;;;;;;;;;;;;;;;;;;;;;;IAkDtB,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAsB,OAAO;oCAAuB,aAAa;gCAAkC;gCAC1G;oCAAE,KAAK;oCAAoB,OAAO;oCAAqB,aAAa;gCAAyC;gCAC7G;oCAAE,KAAK;oCAAwB,OAAO;oCAAyB,aAAa;gCAA8B;gCAC1G;oCAAE,KAAK;oCAAkB,OAAO;oCAAoB,aAAa;gCAAqC;gCACtG;oCAAE,KAAK;oCAAgB,OAAO;oCAAiB,aAAa;gCAAmC;gCAC/F;oCAAE,KAAK;oCAAiB,OAAO;oCAAkB,aAAa;gCAAmC;6BAClG,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC;oCAAsB,WAAU;;sDAC/B,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAe,QAAQ,KAAK;;;;;;8DACzC,6LAAC;oDAAE,WAAU;8DAAiC,QAAQ,WAAW;;;;;;;;;;;;sDAEnE,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS,aAAa,CAAC,QAAQ,GAAG,CAA+B;oDACjE,UAAU,CAAC,IAAM,iBAAiB;4DAChC,GAAG,aAAa;4DAChB,CAAC,QAAQ,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;wDACjC;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAfT,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;IAyBjC,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIxC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAClD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;0DAErC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;0DAErC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUnD,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAM7C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAMtC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAc;;;;;;8DAC3B,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAE/C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUhD,MAAM,qBAAqB,kBACzB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;0CAI7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;0CAIxC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQhD,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,iJAAA,CAAA,aAAU;;8BACT,6LAAC,iJAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAY;oBACZ,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAG5B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;gDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,uCACA,gDACJ;;kEAEF,6LAAC,IAAI,IAAI;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAuB,IAAI,KAAK;;;;;;;+CAT3C,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBvB,6LAAC;4BAAI,WAAU;;gCACZ;8CAGD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAoB,UAAU;kDAC5C,wBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;iEAGR;;8DACE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD;GAtewB;;QACL,sIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}