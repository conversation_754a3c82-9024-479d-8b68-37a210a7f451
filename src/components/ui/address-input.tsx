'use client'

import { useState } from 'react'
import { MapPin, Loader2, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from './button'
import { Input } from './input'
import { 
  GeolocationService, 
  GeolocationError,
  type GeolocationResult 
} from '@/lib/geolocation'

interface AddressInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function AddressInput({
  value,
  onChange,
  placeholder = "请输入地址",
  disabled = false,
  className = ""
}: AddressInputProps) {
  const [isLocating, setIsLocating] = useState(false)
  const [locationStatus, setLocationStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [locationError, setLocationError] = useState<string>('')

  const handleGetLocation = async () => {
    if (!GeolocationService.isSupported()) {
      setLocationError('您的浏览器不支持地理定位功能')
      setLocationStatus('error')
      return
    }

    setIsLocating(true)
    setLocationStatus('idle')
    setLocationError('')

    try {
      const result: GeolocationResult = await GeolocationService.getCurrentLocationAddress()
      
      // 使用获取到的地址
      onChange(result.address.formatted_address)
      setLocationStatus('success')
      
      // 3秒后清除成功状态
      setTimeout(() => {
        setLocationStatus('idle')
      }, 3000)
      
    } catch (error) {
      const errorMessage = GeolocationService.getErrorMessage((error as Error).message)
      setLocationError(errorMessage)
      setLocationStatus('error')
      
      // 5秒后清除错误状态
      setTimeout(() => {
        setLocationStatus('idle')
        setLocationError('')
      }, 5000)
    } finally {
      setIsLocating(false)
    }
  }

  const getLocationButtonVariant = () => {
    switch (locationStatus) {
      case 'success':
        return 'default'
      case 'error':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const getLocationButtonIcon = () => {
    if (isLocating) {
      return <Loader2 className="h-4 w-4 animate-spin" />
    }
    
    switch (locationStatus) {
      case 'success':
        return <CheckCircle className="h-4 w-4" />
      case 'error':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <MapPin className="h-4 w-4" />
    }
  }

  const getLocationButtonText = () => {
    if (isLocating) {
      return '定位中...'
    }
    
    switch (locationStatus) {
      case 'success':
        return '定位成功'
      case 'error':
        return '定位失败'
      default:
        return '获取位置'
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex space-x-2">
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className={className}
        />
        <Button
          type="button"
          variant={getLocationButtonVariant()}
          size="default"
          onClick={handleGetLocation}
          disabled={disabled || isLocating}
          className="flex-shrink-0 min-w-[100px]"
        >
          {getLocationButtonIcon()}
          <span className="ml-2">{getLocationButtonText()}</span>
        </Button>
      </div>
      
      {/* 错误信息显示 */}
      {locationStatus === 'error' && locationError && (
        <div className="flex items-start space-x-2 p-3 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
          <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-sm text-red-700 dark:text-red-300 font-medium">
              定位失败
            </p>
            <p className="text-sm text-red-600 dark:text-red-400">
              {locationError}
            </p>
          </div>
        </div>
      )}
      
      {/* 成功信息显示 */}
      {locationStatus === 'success' && (
        <div className="flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <p className="text-sm text-green-700 dark:text-green-300">
            已成功获取当前位置地址
          </p>
        </div>
      )}
      
      {/* 定位提示信息 */}
      {locationStatus === 'idle' && (
        <div className="text-xs text-muted-foreground">
          点击"获取位置"按钮可自动获取当前位置作为商户地址
        </div>
      )}
    </div>
  )
}
