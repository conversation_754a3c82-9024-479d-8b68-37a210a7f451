import { cn } from "@/lib/utils"
import { Crown, Scissors } from "lucide-react"

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
  text?: string
  fullScreen?: boolean
}

export function Loading({ 
  size = 'md', 
  className, 
  text = 'Loading...', 
  fullScreen = false 
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  const containerClasses = fullScreen 
    ? 'fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50'
    : 'flex items-center justify-center p-4'

  return (
    <div className={cn(containerClasses, className)}>
      <div className="flex flex-col items-center space-y-4">
        <div className="relative animate-pulse">
          <Crown className={cn(sizeClasses[size], "text-primary")} />
          <Scissors className={cn(
            size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-5 w-5' : 'h-8 w-8',
            "text-accent absolute -bottom-1 -right-1"
          )} />
        </div>
        {text && (
          <p className={cn(
            "text-muted-foreground",
            size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'
          )}>
            {text}
          </p>
        )}
        <div className="flex space-x-1">
          <div className="h-2 w-2 bg-primary rounded-full animate-bounce"></div>
          <div className="h-2 w-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="h-2 w-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  )
}

export function LoadingSpinner({ 
  size = 'md', 
  className 
}: { 
  size?: 'sm' | 'md' | 'lg'
  className?: string 
}) {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-6 w-6 border-2',
    lg: 'h-8 w-8 border-3'
  }

  return (
    <div className={cn(
      "border-primary border-t-transparent rounded-full animate-spin",
      sizeClasses[size],
      className
    )} />
  )
}

export function LoadingDots({ 
  size = 'md', 
  className 
}: { 
  size?: 'sm' | 'md' | 'lg'
  className?: string 
}) {
  const dotSizes = {
    sm: 'h-1 w-1',
    md: 'h-2 w-2',
    lg: 'h-3 w-3'
  }

  return (
    <div className={cn("flex space-x-1", className)}>
      <div className={cn(dotSizes[size], "bg-primary rounded-full animate-bounce")}></div>
      <div className={cn(dotSizes[size], "bg-primary rounded-full animate-bounce")} style={{ animationDelay: '0.1s' }}></div>
      <div className={cn(dotSizes[size], "bg-primary rounded-full animate-bounce")} style={{ animationDelay: '0.2s' }}></div>
    </div>
  )
}

export function LoadingCard({ 
  className,
  children 
}: { 
  className?: string
  children?: React.ReactNode 
}) {
  return (
    <div className={cn(
      "rounded-xl border bg-card p-6 shadow-elegant animate-pulse",
      className
    )}>
      <div className="space-y-3">
        <div className="h-4 bg-muted rounded w-3/4"></div>
        <div className="h-4 bg-muted rounded w-1/2"></div>
        <div className="h-4 bg-muted rounded w-5/6"></div>
      </div>
      {children}
    </div>
  )
}

export function LoadingTable({ 
  rows = 5,
  columns = 4,
  className 
}: { 
  rows?: number
  columns?: number
  className?: string 
}) {
  return (
    <div className={cn("space-y-3", className)}>
      {/* Header */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <div key={i} className="h-4 bg-muted rounded flex-1 animate-pulse"></div>
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div 
              key={colIndex} 
              className="h-4 bg-muted/60 rounded flex-1 animate-pulse"
              style={{ animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s` }}
            ></div>
          ))}
        </div>
      ))}
    </div>
  )
}
